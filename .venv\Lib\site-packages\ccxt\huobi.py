# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.htx import htx
from ccxt.abstract.huobi import ImplicitAPI
from ccxt.base.types import Any


class huobi(htx, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(huobi, self).describe(), {
            'id': 'huobi',
            'alias': True,
        })
