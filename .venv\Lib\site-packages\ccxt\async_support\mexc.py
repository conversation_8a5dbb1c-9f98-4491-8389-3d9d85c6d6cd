# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.base.exchange import Exchange
from ccxt.abstract.mexc import ImplicitAPI
import asyncio
import hashlib
from ccxt.base.types import Account, Any, Balances, Currencies, Currency, DepositAddress, IndexType, Int, Leverage, LeverageTier, LeverageTiers, MarginModification, Market, Num, Order, OrderBook, OrderRequest, OrderSide, OrderType, Position, Str, Strings, Ticker, Tickers, FundingRate, Trade, TradingFeeInterface, Transaction, TransferEntry
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import AuthenticationError
from ccxt.base.errors import PermissionDenied
from ccxt.base.errors import AccountSuspended
from ccxt.base.errors import ArgumentsRequired
from ccxt.base.errors import BadRequest
from ccxt.base.errors import BadSymbol
from ccxt.base.errors import InsufficientFunds
from ccxt.base.errors import InvalidAddress
from ccxt.base.errors import InvalidOrder
from ccxt.base.errors import NotSupported
from ccxt.base.errors import OnMaintenance
from ccxt.base.errors import InvalidNonce
from ccxt.base.decimal_to_precision import TICK_SIZE
from ccxt.base.precise import Precise


class mexc(Exchange, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(mexc, self).describe(), {
            'id': 'mexc',
            'name': 'MEXC Global',
            'countries': ['SC'],  # Seychelles
            'rateLimit': 50,  # default rate limit is 20 times per second
            'version': 'v3',
            'certified': True,
            'pro': True,
            'has': {
                'CORS': None,
                'spot': True,
                'margin': True,
                'swap': True,
                'future': False,
                'option': False,
                'addMargin': True,
                'borrowCrossMargin': False,
                'borrowIsolatedMargin': False,
                'borrowMargin': False,
                'cancelAllOrders': True,
                'cancelOrder': True,
                'cancelOrders': None,
                'closeAllPositions': False,
                'closePosition': False,
                'createDepositAddress': True,
                'createMarketBuyOrderWithCost': True,
                'createMarketOrderWithCost': True,
                'createMarketSellOrderWithCost': True,
                'createOrder': True,
                'createOrders': True,
                'createPostOnlyOrder': True,
                'createReduceOnlyOrder': True,
                'createStopLimitOrder': True,
                'createStopMarketOrder': True,
                'createStopOrder': True,
                'createTriggerOrder': True,
                'deposit': None,
                'editOrder': None,
                'fetchAccounts': True,
                'fetchBalance': True,
                'fetchBidsAsks': True,
                'fetchBorrowInterest': False,
                'fetchBorrowRate': False,
                'fetchBorrowRateHistories': False,
                'fetchBorrowRateHistory': False,
                'fetchBorrowRates': False,
                'fetchBorrowRatesPerSymbol': False,
                'fetchCanceledOrders': True,
                'fetchClosedOrder': None,
                'fetchClosedOrders': True,
                'fetchCrossBorrowRate': False,
                'fetchCrossBorrowRates': False,
                'fetchCurrencies': True,
                'fetchDeposit': None,
                'fetchDepositAddress': True,
                'fetchDepositAddresses': None,
                'fetchDepositAddressesByNetwork': True,
                'fetchDeposits': True,
                'fetchDepositWithdrawFee': 'emulated',
                'fetchDepositWithdrawFees': True,
                'fetchFundingHistory': True,
                'fetchFundingInterval': True,
                'fetchFundingIntervals': False,
                'fetchFundingRate': True,
                'fetchFundingRateHistory': True,
                'fetchFundingRates': False,
                'fetchIndexOHLCV': True,
                'fetchIsolatedBorrowRate': False,
                'fetchIsolatedBorrowRates': False,
                'fetchIsolatedPositions': False,
                'fetchL2OrderBook': True,
                'fetchLedger': None,
                'fetchLedgerEntry': None,
                'fetchLeverage': True,
                'fetchLeverages': False,
                'fetchLeverageTiers': True,
                'fetchMarginAdjustmentHistory': False,
                'fetchMarginMode': False,
                'fetchMarketLeverageTiers': 'emulated',
                'fetchMarkets': True,
                'fetchMarkOHLCV': True,
                'fetchMyTrades': True,
                'fetchOHLCV': True,
                'fetchOpenInterest': False,
                'fetchOpenInterestHistory': False,
                'fetchOpenOrder': None,
                'fetchOpenOrders': True,
                'fetchOrder': True,
                'fetchOrderBook': True,
                'fetchOrderBooks': None,
                'fetchOrders': True,
                'fetchOrderTrades': True,
                'fetchPosition': 'emulated',
                'fetchPositionHistory': 'emulated',
                'fetchPositionMode': True,
                'fetchPositions': True,
                'fetchPositionsHistory': True,
                'fetchPositionsRisk': None,
                'fetchPremiumIndexOHLCV': False,
                'fetchStatus': True,
                'fetchTicker': True,
                'fetchTickers': True,
                'fetchTime': True,
                'fetchTrades': True,
                'fetchTradingFee': True,
                'fetchTradingFees': False,
                'fetchTradingLimits': None,
                'fetchTransactionFee': 'emulated',
                'fetchTransactionFees': True,
                'fetchTransactions': None,
                'fetchTransfer': True,
                'fetchTransfers': True,
                'fetchWithdrawal': None,
                'fetchWithdrawals': True,
                'reduceMargin': True,
                'repayCrossMargin': False,
                'repayIsolatedMargin': False,
                'setLeverage': True,
                'setMarginMode': True,
                'setPositionMode': True,
                'signIn': None,
                'transfer': None,
                'withdraw': True,
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/1294454/137283979-8b2a818d-8633-461b-bfca-de89e8c446b2.jpg',
                'api': {
                    'spot': {
                        'public': 'https://api.mexc.com',
                        'private': 'https://api.mexc.com',
                    },
                    'spot2': {
                        'public': 'https://www.mexc.com/open/api/v2',
                        'private': 'https://www.mexc.com/open/api/v2',
                    },
                    'contract': {
                        'public': 'https://contract.mexc.com/api/v1/contract',
                        'private': 'https://contract.mexc.com/api/v1/private',
                    },
                    'broker': {
                        'private': 'https://api.mexc.com/api/v3/broker',
                    },
                },
                'www': 'https://www.mexc.com/',
                'doc': [
                    'https://mexcdevelop.github.io/apidocs/',
                ],
                'fees': [
                    'https://www.mexc.com/fee',
                ],
                'referral': 'https://www.mexc.com/register?inviteCode=mexc-1FQ1GNu1',
            },
            'api': {
                'spot': {
                    'public': {
                        'get': {
                            'ping': 1,
                            'time': 1,
                            'exchangeInfo': 10,
                            'depth': 1,
                            'trades': 5,
                            'historicalTrades': 1,
                            'aggTrades': 1,
                            'klines': 1,
                            'avgPrice': 1,
                            'ticker/24hr': 1,
                            'ticker/price': 1,
                            'ticker/bookTicker': 1,
                            'etf/info': 1,
                        },
                    },
                    'private': {
                        'get': {
                            'order': 2,
                            'openOrders': 3,
                            'allOrders': 10,
                            'account': 10,
                            'myTrades': 10,
                            'tradeFee': 10,
                            'sub-account/list': 1,
                            'sub-account/apiKey': 1,
                            'capital/config/getall': 10,
                            'capital/deposit/hisrec': 1,
                            'capital/withdraw/history': 1,
                            'capital/withdraw/address': 10,
                            'capital/deposit/address': 10,
                            'capital/transfer': 1,
                            'capital/transfer/tranId': 1,
                            'capital/transfer/internal': 1,
                            'capital/sub-account/universalTransfer': 1,
                            'capital/convert': 1,
                            'capital/convert/list': 1,
                            'margin/loan': 1,
                            'margin/allOrders': 1,
                            'margin/myTrades': 1,
                            'margin/openOrders': 1,
                            'margin/maxTransferable': 1,
                            'margin/priceIndex': 1,
                            'margin/order': 1,
                            'margin/isolated/account': 1,
                            'margin/maxBorrowable': 1,
                            'margin/repay': 1,
                            'margin/isolated/pair': 1,
                            'margin/forceLiquidationRec': 1,
                            'margin/isolatedMarginData': 1,
                            'margin/isolatedMarginTier': 1,
                            'rebate/taxQuery': 1,
                            'rebate/detail': 1,
                            'rebate/detail/kickback': 1,
                            'rebate/referCode': 1,
                            'rebate/affiliate/commission': 1,
                            'rebate/affiliate/withdraw': 1,
                            'rebate/affiliate/commission/detail': 1,
                            'mxDeduct/enable': 1,
                            'userDataStream': 1,
                            'selfSymbols': 1,
                        },
                        'post': {
                            'order': 1,
                            'order/test': 1,
                            'sub-account/virtualSubAccount': 1,
                            'sub-account/apiKey': 1,
                            'sub-account/futures': 1,
                            'sub-account/margin': 1,
                            'batchOrders': 10,
                            'capital/withdraw/apply': 1,
                            'capital/withdraw': 1,
                            'capital/transfer': 1,
                            'capital/transfer/internal': 1,
                            'capital/deposit/address': 1,
                            'capital/sub-account/universalTransfer': 1,
                            'capital/convert': 10,
                            'mxDeduct/enable': 1,
                            'userDataStream': 1,
                        },
                        'put': {
                            'userDataStream': 1,
                        },
                        'delete': {
                            'order': 1,
                            'openOrders': 1,
                            'sub-account/apiKey': 1,
                            'margin/order': 1,
                            'margin/openOrders': 1,
                            'userDataStream': 1,
                            'capital/withdraw': 1,
                        },
                    },
                },
                'contract': {
                    'public': {
                        'get': {
                            'ping': 2,
                            'detail': 100,
                            'support_currencies': 2,
                            'depth/{symbol}': 2,
                            'depth_commits/{symbol}/{limit}': 2,
                            'index_price/{symbol}': 2,
                            'fair_price/{symbol}': 2,
                            'funding_rate/{symbol}': 2,
                            'kline/{symbol}': 2,
                            'kline/index_price/{symbol}': 2,
                            'kline/fair_price/{symbol}': 2,
                            'deals/{symbol}': 2,
                            'ticker': 2,
                            'risk_reverse': 2,
                            'risk_reverse/history': 2,
                            'funding_rate/history': 2,
                        },
                    },
                    'private': {
                        'get': {
                            'account/assets': 2,
                            'account/asset/{currency}': 2,
                            'account/transfer_record': 2,
                            'position/list/history_positions': 2,
                            'position/open_positions': 2,
                            'position/funding_records': 2,
                            'position/position_mode': 2,
                            'order/list/open_orders/{symbol}': 2,
                            'order/list/history_orders': 2,
                            'order/external/{symbol}/{external_oid}': 2,
                            'order/get/{order_id}': 2,
                            'order/batch_query': 8,
                            'order/deal_details/{order_id}': 2,
                            'order/list/order_deals': 2,
                            'planorder/list/orders': 2,
                            'stoporder/list/orders': 2,
                            'stoporder/order_details/{stop_order_id}': 2,
                            'account/risk_limit': 2,  # TO_DO: gets max/min position size, allowed sides, leverage, maintenance margin, initial margin, etc...
                            'account/tiered_fee_rate': 2,  # TO_DO: taker/maker fees for account
                            'position/leverage': 2,
                        },
                        'post': {
                            'position/change_margin': 2,
                            'position/change_leverage': 2,
                            'position/change_position_mode': 2,
                            'order/submit': 2,
                            'order/submit_batch': 40,
                            'order/cancel': 2,
                            'order/cancel_with_external': 2,
                            'order/cancel_all': 2,
                            'account/change_risk_level': 2,
                            'planorder/place': 2,
                            'planorder/cancel': 2,
                            'planorder/cancel_all': 2,
                            'stoporder/cancel': 2,
                            'stoporder/cancel_all': 2,
                            'stoporder/change_price': 2,
                            'stoporder/change_plan_price': 2,
                        },
                    },
                },
                'spot2': {
                    'public': {
                        'get': {
                            'market/symbols': 1,
                            'market/coin/list': 2,
                            'common/timestamp': 1,
                            'common/ping': 2,
                            'market/ticker': 1,
                            'market/depth': 1,
                            'market/deals': 1,
                            'market/kline': 1,
                            'market/api_default_symbols': 2,
                        },
                    },
                    'private': {
                        'get': {
                            'account/info': 1,
                            'order/open_orders': 1,
                            'order/list': 1,
                            'order/query': 1,
                            'order/deals': 1,
                            'order/deal_detail': 1,
                            'asset/deposit/address/list': 2,
                            'asset/deposit/list': 2,
                            'asset/address/list': 2,
                            'asset/withdraw/list': 2,
                            'asset/internal/transfer/record': 10,
                            'account/balance': 10,
                            'asset/internal/transfer/info': 10,
                            'market/api_symbols': 2,
                        },
                        'post': {
                            'order/place': 1,
                            'order/place_batch': 1,
                            'order/advanced/place_batch': 1,
                            'asset/withdraw': 2,
                            'asset/internal/transfer': 10,
                        },
                        'delete': {
                            'order/cancel': 1,
                            'order/cancel_by_symbol': 1,
                            'asset/withdraw': 2,
                        },
                    },
                },
                'broker': {
                    'private': {
                        'get': {
                            'sub-account/universalTransfer': 1,
                            'sub-account/list': 1,
                            'sub-account/apiKey': 1,
                            'capital/deposit/subAddress': 1,
                            'capital/deposit/subHisrec': 1,
                            'capital/deposit/subHisrec/getall': 1,
                        },
                        'post': {
                            'sub-account/virtualSubAccount': 1,
                            'sub-account/apiKey': 1,
                            'capital/deposit/subAddress': 1,
                            'capital/withdraw/apply': 1,
                            'sub-account/universalTransfer': 1,
                            'sub-account/futures': 1,
                        },
                        'delete': {
                            'sub-account/apiKey': 1,
                        },
                    },
                },
            },
            'precisionMode': TICK_SIZE,
            'timeframes': {
                '1m': '1m',  # spot, swap
                '5m': '5m',  # spot, swap
                '15m': '15m',  # spot, swap
                '30m': '30m',  # spot, swap
                '1h': '1h',  # spot, swap
                '4h': '4h',  # spot, swap
                '8h': '8h',  # swap
                '1d': '1d',  # spot, swap
                '1w': '1w',  # swap
                '1M': '1M',  # spot, swap
            },
            'fees': {
                'trading': {
                    'tierBased': False,
                    'percentage': True,
                    'maker': self.parse_number('0.002'),  # maker / taker
                    'taker': self.parse_number('0.002'),
                },
            },
            'options': {
                'adjustForTimeDifference': False,
                'timeDifference': 0,
                'unavailableContracts': {
                    'BTC/USDT:USDT': True,
                    'LTC/USDT:USDT': True,
                    'ETH/USDT:USDT': True,
                },
                'fetchMarkets': {
                    'types': {
                        'spot': True,
                        'swap': {
                            'linear': True,
                            'inverse': False,
                        },
                    },
                },
                'timeframes': {
                    'spot': {
                        '1m': '1m',
                        '5m': '5m',
                        '15m': '15m',
                        '30m': '30m',
                        '1h': '60m',
                        '4h': '4h',
                        '1d': '1d',
                        '1w': '1W',
                        '1M': '1M',
                    },
                    'swap': {
                        '1m': 'Min1',
                        '5m': 'Min5',
                        '15m': 'Min15',
                        '30m': 'Min30',
                        '1h': 'Min60',
                        '4h': 'Hour4',
                        '8h': 'Hour8',
                        '1d': 'Day1',
                        '1w': 'Week1',
                        '1M': 'Month1',
                    },
                },
                'defaultType': 'spot',  # spot, swap
                'defaultNetwork': 'ETH',
                'defaultNetworks': {
                    'ETH': 'ETH',
                    'USDT': 'ERC20',
                    'USDC': 'ERC20',
                    'BTC': 'BTC',
                    'LTC': 'LTC',
                },
                'networks': {
                    'ZKSYNC': 'ZKSYNCERA',
                    'TRC20': 'TRX',
                    'TON': 'TONCOIN',
                    'AVAXC': 'AVAX_CCHAIN',
                    'ERC20': 'ETH',
                    'ACA': 'ACALA',
                    'BEP20': 'BSC',
                    'OPTIMISM': 'OP',
                    # 'ADA': 'Cardano(ADA)',
                    # 'AE': 'AE',
                    # 'ALGO': 'Algorand(ALGO)',
                    # 'ALPH': 'Alephium(ALPH)',
                    # 'ARB': 'Arbitrum One(ARB)',
                    # 'ARBONE': 'ArbitrumOne(ARB)',
                    'ASTR': 'ASTAR',  # ASTAREVM is different
                    # 'ATOM': 'Cosmos(ATOM)',
                    # 'AVAXC': 'Avalanche C Chain(AVAX CCHAIN)',
                    # 'AVAXX': 'Avalanche X Chain(AVAX XCHAIN)',
                    # 'AZERO': 'Aleph Zero(AZERO)',
                    # 'BCH': 'Bitcoin Cash(BCH)',
                    # 'BNCDOT': 'BNCPOLKA',
                    # 'BSV': 'Bitcoin SV(BSV)',
                    # 'BTC': 'Bitcoin(BTC)',
                    'BTM': 'BTM2',
                    # 'CHZ': 'Chiliz Legacy Chain(CHZ)',
                    # 'CHZ2': 'Chiliz Chain(CHZ2)',
                    # 'CLORE': 'Clore.ai(CLORE)',
                    'CRC20': 'CRONOS',
                    # 'DC': 'Dogechain(DC)',
                    # 'DNX': 'Dynex(DNX)',
                    # 'DOGE': 'Dogecoin(DOGE)',
                    # 'DOT': 'Polkadot(DOT)',
                    # 'DYM': 'Dymension(DYM)',
                    'ETHF': 'ETF',
                    'HRC20': 'HECO',
                    # 'KLAY': 'Klaytn(KLAY)',
                    'OASIS': 'ROSE',
                    'OKC': 'OKT',
                    'RSK': 'RBTC',
                    # 'RVN': 'Ravencoin(RVN)',
                    # 'SATOX': 'Satoxcoin(SATOX)',
                    # 'SC': 'SC',
                    # 'SCRT': 'SCRT',
                    # 'SDN': 'SDN',
                    # 'SGB': 'SGB',
                    # 'SOL': 'Solana(SOL)',
                    # 'STAR': 'STAR',
                    # 'STARK': 'Starknet(STARK)',
                    # 'STEEM': 'STEEM',
                    # 'SYS': 'SYS',
                    # 'TAO': 'Bittensor(TAO)',
                    # 'TIA': 'Celestia(TIA)',
                    # 'TOMO': 'TOMO',
                    # 'TON': 'Toncoin(TON)',
                    # 'TRC10': 'TRC10',
                    # 'TRC20': 'Tron(TRC20)',
                    # 'UGAS': 'UGAS(Ultrain)',
                    # 'VET': 'VeChain(VET)',
                    # 'VEX': 'Vexanium(VEX)',
                    # 'VSYS': 'VSYS',
                    # 'WAVES': 'WAVES',
                    # 'WAX': 'WAX',
                    # 'WEMIX': 'WEMIX',
                    # 'XCH': 'Chia(XCH)',
                    # 'XDC': 'XDC',
                    # 'XEC': 'XEC',
                    # 'XLM': 'Stellar(XLM)',
                    # 'XMR': 'Monero(XMR)',
                    # 'XNA': 'Neurai(XNA)',
                    # 'XPR': 'XPR Network',
                    # 'XRD': 'XRD',
                    # 'XRP': 'Ripple(XRP)',
                    # 'XTZ': 'XTZ',
                    # 'XVG': 'XVG',
                    # 'XYM': 'XYM',
                    # 'ZEC': 'ZEC',
                    # 'ZEN': 'ZEN',
                    # 'ZIL': 'Zilliqa(ZIL)',
                    # 'ZTG': 'ZTG',
                    # todo: uncomment below after concensus
                    # 'ALAYA': 'ATP',
                    # 'ANDUSCHAIN': 'DEB',
                    # 'ASSETMANTLE': 'MNTL',
                    # 'AXE': 'AXE',
                    # 'BITCOINHD': 'BHD',
                    # 'BITCOINVAULT': 'BTCV',
                    # 'BITKUB': 'KUB',
                    # 'BITSHARES_OLD': 'BTS',
                    # 'BITSHARES': 'NBS',
                    # 'BYTZ': 'BYTZ',
                    # 'CANTO': 'CANTO',  # CANTOEVM
                    # 'CENNZ': 'CENNZ',
                    # 'CHAINX': 'PCX',
                    # 'CONCODRIUM': 'CCD',
                    # 'CONTENTVALUENETWORK': 'CVNT',
                    # 'CORTEX': 'CTXC',
                    # 'CYPHERIUM': 'CPH',
                    # 'DANGNN': 'DGC',
                    # 'DARWINIASMARTCHAIN': 'Darwinia Smart Chain',
                    # 'DHEALTH': 'DHP',
                    # 'DOGECOIN': ['DOGE', 'DOGECHAIN'],  # todo after unification
                    # 'DRAC': 'DRAC',
                    # 'DRAKEN': 'DRK',
                    # 'ECOCHAIN': 'ECOC',
                    # 'ELECTRAPROTOCOL': 'XEP',
                    # 'EMERALD': 'EMERALD',  # sits on top of OASIS
                    # 'EVMOS': 'EVMOS',  # EVMOSETH is different
                    # 'EXOSAMA': 'SAMA',
                    # 'FIBOS': 'FO',
                    # 'FILECASH': 'FIC',
                    # 'FIRMACHAIN': 'FCT',
                    # 'FIRO': 'XZC',
                    # 'FNCY': 'FNCY',
                    # 'FRUITS': 'FRTS',
                    # 'GLEEC': 'GLEEC',
                    # 'GXCHAIN': 'GXC',
                    # 'HANDSHAKE': 'HNS',
                    # 'HPB': 'HPB',
                    # 'HSHARE': 'HC',
                    # 'HUAHUA': 'HUAHUA',
                    # 'HUPAYX': 'HPX',
                    # 'INDEXCHAIN': 'IDX',
                    # 'INTCHAIN': 'INT',
                    # 'INTEGRITEE': 'TEER',
                    # 'INTERLAY': 'INTR',
                    # 'IOEX': 'IOEX',
                    # 'JUNO': 'JUNO',
                    # 'KASPA': 'KASPA',
                    # 'KEKCHAIN': 'KEKCHAIN',
                    # 'KINTSUGI': 'KINT',
                    # 'KOINOS': 'KOINOS',
                    # 'KONSTELLATION': 'DARC',
                    # 'KUJIRA': 'KUJI',
                    # 'KULUPU': 'KLP',
                    # 'LBRY': 'LBC',
                    # 'LEDGIS': 'LED',
                    # 'LIGHTNINGBITCOIN': 'LBTC',
                    # 'LINE': 'LINE',
                    # 'MDNA': 'DNA',
                    # 'MDUKEY': 'MDU',
                    # 'METAMUI': 'MMUI',
                    # 'METAVERSE_ETP': 'ETP',
                    # 'METER': 'MTRG',
                    # 'MEVERSE': 'MEVerse',
                    # 'NEWTON': 'NEW',
                    # 'NODLE': 'NODLE',
                    # 'ORIGYN': 'OGY',
                    # 'PAC': 'PAC',
                    # 'PASTEL': 'PSL',
                    # 'PHALA': 'Khala',
                    # 'PLEX': 'PLEX',
                    # 'PMG': 'PMG',
                    # 'POINT': 'POINT',  # POINTEVM is different
                    # 'PROOFOFMEMES': 'POM',
                    # 'PROXIMAX': 'XPX',
                    # 'RCHAIN': 'REV',
                    # 'REBUS': 'REBUS',  # REBUSEVM is different
                    # 'RIZON': 'ATOLO',
                    # 'SENTINEL': 'DVPN',
                    # 'SERO': 'SERO',
                    # 'TECHPAY': 'TPC',
                    # 'TELOSCOIN': 'TLOS',  # todo
                    # 'TERRA': 'LUNA2',
                    # 'TERRACLASSIC': 'LUNC',
                    # 'TLOS': 'TELOS',  # todo
                    # 'TOMAINFO': 'TON',
                    # 'TONGTONG': 'TTC',
                    # 'TURTLECOIN': 'TRTL',
                    # 'ULORD': 'UT',
                    # 'ULTRAIN': 'UGAS',
                    # 'UMEE': 'UMEE',
                    # 'VDIMENSION': 'VOLLAR',
                    # 'VEXANIUM': 'VEX',
                    # 'VNT': 'VNT',
                    # 'WAYKICHAIN': 'WICC',
                    # 'WHITECOIN': 'XWC',
                    # 'WITNET': 'WIT',
                    # 'XDAI': 'XDAI',
                    # 'XX': 'XX',
                    # 'YAS': 'YAS',
                    # 'ZENITH': 'ZENITH',
                    # 'ZKSYNC': 'ZKSYNC',
                    #  # 'BAJUN': '',
                    # OKB <> OKT(for usdt it's exception) for OKC, PMEER, FLARE, STRD, ZEL, FUND, "NONE", CRING, FREETON, QTZ  (probably unique network is meant), HT, BSC(RACAV1), BSC(RACAV2), AMBROSUS, BAJUN, NOM. their individual info is at https://www.mexc.com/api/platform/asset/spot/{COINNAME}
                },
                'networksById': {
                    'BNB Smart Chain(BEP20-RACAV1)': 'BSC',
                    'BNB Smart Chain(BEP20-RACAV2)': 'BSC',
                    'BNB Smart Chain(BEP20)': 'BSC',
                    # TODO: uncomment below after deciding unified name
                    # 'PEPE COIN BSC':
                    # 'SMART BLOCKCHAIN':
                    # 'f(x)Core':
                    # 'Syscoin Rollux':
                    # 'Syscoin UTXO':
                    # 'zkSync Era':
                    # 'zkSync Lite':
                    # 'Darwinia Smart Chain':
                    # 'Arbitrum One(ARB-Bridged)':
                    # 'Optimism(OP-Bridged)':
                    # 'Polygon(MATIC-Bridged)':
                },
                'recvWindow': 5 * 1000,  # 5 sec, default
                'maxTimeTillEnd': 90 * 86400 * 1000 - 1,  # 90 days
                'broker': 'CCXT',
            },
            'features': {
                'default': {
                    'sandbox': False,
                    'createOrder': {
                        'marginMode': True,
                        'triggerPrice': False,
                        'triggerDirection': False,
                        'triggerPriceType': {
                            'last': False,
                            'mark': False,
                            'index': False,
                        },
                        'stopLossPrice': False,  # todo
                        'takeProfitPrice': False,
                        'attachedStopLossTakeProfit': None,
                        'timeInForce': {
                            'IOC': True,
                            'FOK': True,
                            'PO': True,
                            'GTD': False,
                        },
                        'hedged': True,  # todo implement
                        'trailing': False,
                        'leverage': True,  # todo implement
                        'marketBuyByCost': True,
                        'marketBuyRequiresPrice': False,
                        'selfTradePrevention': False,
                        'iceberg': False,
                    },
                    'createOrders': {
                        'max': 20,
                    },
                    'fetchMyTrades': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 30,
                        'untilDays': None,
                        'symbolRequired': True,
                    },
                    'fetchOrder': {
                        'marginMode': False,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,
                    },
                    'fetchOpenOrders': {
                        'marginMode': True,
                        'limit': None,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,
                    },
                    'fetchOrders': {
                        'marginMode': True,
                        'limit': 1000,
                        'daysBack': 7,
                        'untilDays': 7,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,
                    },
                    'fetchClosedOrders': {
                        'marginMode': True,
                        'limit': 1000,
                        'daysBack': 7,
                        'daysBackCanceled': 7,
                        'untilDays': 7,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,
                    },
                    'fetchOHLCV': {
                        'limit': 1000,
                    },
                },
                'spot': {
                    'extends': 'default',
                },
                'forDerivs': {
                    'extends': 'default',
                    'createOrder': {
                        'triggerPrice': True,
                        'triggerPriceType': {
                            'last': True,
                            'mark': True,
                            'index': True,
                        },
                        'triggerDirection': True,  # todo
                        'stopLossPrice': False,  # todo
                        'takeProfitPrice': False,  # todo
                        'hedged': True,
                        'leverage': True,  # todo
                        'marketBuyByCost': False,
                    },
                    'createOrders': None,  # todo: needs implementation https://mexcdevelop.github.io/apidocs/contract_v1_en/#order-under-maintenance:~:text=Order%20the%20contract%20in%20batch
                    'fetchMyTrades': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 90,
                        'untilDays': 90,
                    },
                    'fetchOrder': {
                        'marginMode': False,
                    },
                    'fetchOpenOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'trigger': True,
                        'trailing': False,
                    },
                    'fetchOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 90,
                        'untilDays': 90,
                        'trigger': True,
                        'trailing': False,
                    },
                    'fetchClosedOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 90,
                        'daysBackCanceled': None,
                        'untilDays': 90,
                        'trigger': True,
                        'trailing': False,
                    },
                    'fetchOHLCV': {
                        'limit': 2000,
                    },
                },
                'swap': {
                    'linear': {
                        'extends': 'forDerivs',
                    },
                    'inverse': {
                        'extends': 'forDerivs',
                    },
                },
                'future': {
                    'linear': None,
                    'inverse': None,
                },
            },
            'commonCurrencies': {
                'BEYONDPROTOCOL': 'BEYOND',
                'BIFI': 'BIFIF',
                'BYN': 'BEYONDFI',
                'COFI': 'COFIX',  # conflict with CoinFi
                'DFI': 'DFISTARTER',
                'DFT': 'DFUTURE',
                'DRK': 'DRK',
                'EGC': 'EGORASCREDIT',
                'FLUX1': 'FLUX',  # switched places
                'FLUX': 'FLUX1',  # switched places
                'FREE': 'FREEROSSDAO',  # conflict with FREE Coin
                'GAS': 'GASDAO',
                'GASNEO': 'GAS',
                'GMT': 'GMTTOKEN',  # Conflict with GMT(STEPN)
                'STEPN': 'GMT',  # Conflict with GMT Token
                'HERO': 'STEPHERO',  # conflict with Metahero
                'MIMO': 'MIMOSA',
                'PROS': 'PROSFINANCE',  # conflict with Prosper
                'SIN': 'SINCITYTOKEN',
                'SOUL': 'SOULSWAP',
            },
            'exceptions': {
                'exact': {
                    # until mexc migrates fully to v3, it might be worth to note the version & market aside errors, not easily remove obsolete version's exceptions in future
                    '-1128': BadRequest,
                    '-2011': BadRequest,
                    '-1121': BadSymbol,
                    '10101': InsufficientFunds,  # {"msg":"资金不足","code":10101}
                    '2009': InvalidOrder,  # {"success":false,"code":2009,"message":"Position is not exists or closed."}
                    '2011': BadRequest,
                    '30004': InsufficientFunds,
                    '33333': BadRequest,  # {"msg":"Not support transfer","code":33333}
                    '44444': BadRequest,
                    '1002': InvalidOrder,
                    '30019': BadRequest,
                    '30005': InvalidOrder,
                    '2003': InvalidOrder,
                    '2005': InsufficientFunds,
                    '400': BadRequest,  # {"msg":"The start time cannot be earlier than 90 days","code":400}
                    # '500': OnMaintenance,  # {"code": 500,"message": "Under maintenance, please try again later","announcement": "https://www.mexc.com/support/articles/17827791510263"}
                    '600': BadRequest,
                    '70011': PermissionDenied,  # {"code":70011,"msg":"Pair user ban trade apikey."}
                    '88004': InsufficientFunds,  # {"msg":"超出最大可借，最大可借币为:18.09833211","code":88004}
                    '88009': ExchangeError,  # v3 {"msg":"Loan record does not exist","code":88009}
                    '88013': InvalidOrder,  # {"msg":"最小交易额不能小于：5USDT","code":88013}
                    '88015': InsufficientFunds,  # {"msg":"持仓不足","code":88015}
                    '700003': InvalidNonce,  # {"code":700003,"msg":"Timestamp for self request is outside of the recvWindow."}
                    '26': ExchangeError,  # operation not allowed
                    '602': AuthenticationError,  # Signature verification failed
                    '10001': AuthenticationError,  # user does not exist
                    '10007': BadSymbol,  # {"code":10007,"msg":"bad symbol"}
                    '10015': BadRequest,  # user id cannot be null
                    '10072': BadRequest,  # invalid access key
                    '10073': BadRequest,  # invalid Request-Time
                    '10095': InvalidOrder,  # amount cannot be null
                    '10096': InvalidOrder,  # amount decimal places is too long
                    '10097': InvalidOrder,  # amount is error
                    '10098': InvalidOrder,  # risk control system detected abnormal
                    '10099': BadRequest,  # user sub account does not open
                    '10100': BadRequest,  # self currency transfer is not supported
                    '10102': InvalidOrder,  # amount cannot be zero or negative
                    '10103': ExchangeError,  # self account transfer is not supported
                    '10200': BadRequest,  # transfer operation processing
                    '10201': BadRequest,  # transfer in failed
                    '10202': BadRequest,  # transfer out failed
                    '10206': BadRequest,  # transfer is disabled
                    '10211': BadRequest,  # transfer is forbidden
                    '10212': BadRequest,  # This withdrawal address is not on the commonly used address list or has been invalidated
                    '10216': ExchangeError,  # no address available. Please try again later
                    '10219': ExchangeError,  # asset flow writing failed please try again
                    '10222': BadRequest,  # currency cannot be null
                    '10232': BadRequest,  # currency does not exist
                    '10259': ExchangeError,  # Intermediate account does not configured in redisredis
                    '10265': ExchangeError,  # Due to risk control, withdrawal is unavailable, please try again later
                    '10268': BadRequest,  # remark length is too long
                    '20001': ExchangeError,  # subsystem is not supported
                    '20002': ExchangeError,  # Internal system error please contact support
                    '22222': BadRequest,  # record does not exist
                    '30000': ExchangeError,  # suspended transaction for the symbol
                    '30001': InvalidOrder,  # The current transaction direction is not allowed to place an order
                    '30002': InvalidOrder,  # The minimum transaction volume cannot be less than :
                    '30003': InvalidOrder,  # The maximum transaction volume cannot be greater than :
                    '30010': InvalidOrder,  # no valid trade price
                    '30014': InvalidOrder,  # invalid symbol
                    '30016': InvalidOrder,  # trading disabled
                    '30018': AccountSuspended,  # {"msg":"账号暂时不能下单，请联系客服","code":30018}
                    '30020': AuthenticationError,  # no permission for the symbol
                    '30021': BadRequest,  # invalid symbol
                    '30025': InvalidOrder,  # no exist opponent order
                    '30026': BadRequest,  # invalid order ids
                    '30027': InvalidOrder,  # The currency has reached the maximum position limit, the buying is suspended
                    '30028': InvalidOrder,  # The currency triggered the platform risk control, the selling is suspended
                    '30029': InvalidOrder,  # Cannot exceed the maximum order limit
                    '30032': InvalidOrder,  # Cannot exceed the maximum position
                    '30041': InvalidOrder,  # current order type can not place order
                    '60005': ExchangeError,  # your account is abnormal
                    '700001': AuthenticationError,  # {"code":700002,"msg":"Signature for self request is not valid."}  # same message for expired API keys
                    '700002': AuthenticationError,  # Signature for self request is not valid  # or the API secret is incorrect
                    '700004': BadRequest,  # Param 'origClientOrderId' or 'orderId' must be sent, but both were empty/null
                    '700005': InvalidNonce,  # recvWindow must less than 60000
                    '700006': BadRequest,  # IP non white list
                    '700007': AuthenticationError,  # No permission to access the endpoint
                    '700008': BadRequest,  # Illegal characters found in parameter
                    '700013': AuthenticationError,  # Invalid Content-Type v3
                    '730001': BadRequest,  # Pair not found
                    '730002': BadRequest,  # Your input param is invalid
                    '730000': ExchangeError,  # Request failed, please contact the customer service
                    '730003': ExchangeError,  # Unsupported operation, please contact the customer service
                    '730100': ExchangeError,  # Unusual user status
                    '730600': BadRequest,  # Sub-account Name cannot be null
                    '730601': BadRequest,  # Sub-account Name must be a combination of 8-32 letters and numbers
                    '730602': BadRequest,  # Sub-account remarks cannot be null
                    '730700': BadRequest,  # API KEY remarks cannot be null
                    '730701': BadRequest,  # API KEY permission cannot be null
                    '730702': BadRequest,  # API KEY permission does not exist
                    '730703': BadRequest,  # The IP information is incorrect, and a maximum of 10 IPs are allowed to be bound only
                    '730704': BadRequest,  # The bound IP format is incorrect, please refill
                    '730705': BadRequest,  # At most 30 groups of Api Keys are allowed to be created only
                    '730706': BadRequest,  # API KEY information does not exist
                    '730707': BadRequest,  # accessKey cannot be null
                    '730101': BadRequest,  # The user Name already exists
                    '140001': BadRequest,  # sub account does not exist
                    '140002': AuthenticationError,  # sub account is forbidden
                },
                'broad': {
                    'Order quantity error, please try to modify.': BadRequest,  # code:2011
                    'Combination of optional parameters invalid': BadRequest,  # code:-2011
                    'api market order is disabled': BadRequest,  #
                    'Contract not allow place order!': InvalidOrder,  # code:1002
                    'Oversold': InsufficientFunds,  # code:30005
                    'Insufficient position': InsufficientFunds,  # code:30004
                    'Insufficient balance!': InsufficientFunds,  # code:2005
                    'Bid price is great than max allow price': InvalidOrder,  # code:2003
                    'Invalid symbol.': BadSymbol,  # code:-1121
                    'Param error!': BadRequest,  # code:600
                    'maintenance': OnMaintenance,  # {"code": 500,"message": "Under maintenance, please try again later","announcement": "https://www.mexc.com/support/articles/17827791510263"}
                },
            },
        })

    async def fetch_status(self, params={}):
        """
        the latest known information on the availability of the exchange API

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#test-connectivity
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-server-time

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `status structure <https://docs.ccxt.com/#/?id=exchange-status-structure>`
        """
        marketType, query = self.handle_market_type_and_params('fetchStatus', None, params)
        response = None
        status = None
        updated = None
        if marketType == 'spot':
            response = await self.spotPublicGetPing(query)
            #
            #     {}
            #
            keys = list(response.keys())
            length = len(keys)
            status = self.json(response) if length else 'ok'
        elif marketType == 'swap':
            response = await self.contractPublicGetPing(query)
            #
            #     {"success":true,"code":"0","data":"1648124374985"}
            #
            status = 'ok' if self.safe_value(response, 'success') else self.json(response)
            updated = self.safe_integer(response, 'data')
        return {
            'status': status,
            'updated': updated,
            'url': None,
            'eta': None,
            'info': response,
        }

    async def fetch_time(self, params={}) -> Int:
        """
        fetches the current integer timestamp in milliseconds from the exchange server

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#check-server-time
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-server-time

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int: the current integer timestamp in milliseconds from the exchange server
        """
        marketType, query = self.handle_market_type_and_params('fetchTime', None, params)
        response = None
        if marketType == 'spot':
            response = await self.spotPublicGetTime(query)
            #
            #     {"serverTime": "1647519277579"}
            #
            return self.safe_integer(response, 'serverTime')
        elif marketType == 'swap':
            response = await self.contractPublicGetPing(query)
            #
            #     {"success":true,"code":"0","data":"1648124374985"}
            #
            return self.safe_integer(response, 'data')
        return None

    async def fetch_currencies(self, params={}) -> Currencies:
        """
        fetches all available currencies on an exchange

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#query-the-currency-information

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an associative dictionary of currencies
        """
        # self endpoint requires authentication
        # while fetchCurrencies is a public API method by design
        # therefore we check the keys here
        # and fallback to generating the currencies from the markets
        if not self.check_required_credentials(False):
            return None
        response = await self.spotPrivateGetCapitalConfigGetall(params)
        #
        # {
        #     "coin": "QANX",
        #     "name": "QANplatform",
        #     "networkList": [
        #       {
        #         "coin": "QANX",
        #         "depositDesc": null,
        #         "depositEnable": True,
        #         "minConfirm": "0",
        #         "name": "QANplatform",
        #         "network": "BEP20(BSC)",
        #         "withdrawEnable": False,
        #         "withdrawFee": "42.000000000000000000",
        #         "withdrawIntegerMultiple": null,
        #         "withdrawMax": "24000000.000000000000000000",
        #         "withdrawMin": "20.000000000000000000",
        #         "sameAddress": False,
        #         "contract": "0xAAA7A10a8ee237ea61E8AC46C50A8Db8bCC1baaa"
        #       },
        #       {
        #         "coin": "QANX",
        #         "depositDesc": null,
        #         "depositEnable": True,
        #         "minConfirm": "0",
        #         "name": "QANplatform",
        #         "network": "ERC20",
        #         "withdrawEnable": True,
        #         "withdrawFee": "2732.000000000000000000",
        #         "withdrawIntegerMultiple": null,
        #         "withdrawMax": "24000000.000000000000000000",
        #         "withdrawMin": "240.000000000000000000",
        #         "sameAddress": False,
        #         "contract": "0xAAA7A10a8ee237ea61E8AC46C50A8Db8bCC1baaa"
        #       }
        #     ]
        #   }
        #
        result: dict = {}
        for i in range(0, len(response)):
            currency = response[i]
            id = self.safe_string(currency, 'coin')
            code = self.safe_currency_code(id)
            networks: dict = {}
            chains = self.safe_value(currency, 'networkList', [])
            for j in range(0, len(chains)):
                chain = chains[j]
                networkId = self.safe_string_2(chain, 'netWork', 'network')
                network = self.network_id_to_code(networkId)
                networks[network] = {
                    'info': chain,
                    'id': networkId,
                    'network': network,
                    'active': None,
                    'deposit': self.safe_bool(chain, 'depositEnable', False),
                    'withdraw': self.safe_bool(chain, 'withdrawEnable', False),
                    'fee': self.safe_number(chain, 'withdrawFee'),
                    'precision': None,
                    'limits': {
                        'withdraw': {
                            'min': self.safe_string(chain, 'withdrawMin'),
                            'max': self.safe_string(chain, 'withdrawMax'),
                        },
                    },
                    'contract': self.safe_string(chain, 'contract'),
                }
            result[code] = self.safe_currency_structure({
                'info': currency,
                'id': id,
                'code': code,
                'name': self.safe_string(currency, 'name'),
                'active': None,
                'deposit': None,
                'withdraw': None,
                'fee': None,
                'precision': None,
                'limits': {
                    'amount': {
                        'min': None,
                        'max': None,
                    },
                },
                'type': 'crypto',
                'networks': networks,
            })
        return result

    async def fetch_markets(self, params={}) -> List[Market]:
        """
        retrieves data on all markets for mexc

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#exchange-information
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-contract-information

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        if self.options['adjustForTimeDifference']:
            await self.load_time_difference()
        spotMarketPromise = self.fetch_spot_markets(params)
        swapMarketPromise = self.fetch_swap_markets(params)
        spotMarket, swapMarket = await asyncio.gather(*[spotMarketPromise, swapMarketPromise])
        return self.array_concat(spotMarket, swapMarket)

    async def fetch_spot_markets(self, params={}):
        """
 @ignore
        retrieves data on all spot markets for mexc

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#exchange-information

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        response = await self.spotPublicGetExchangeInfo(params)
        #
        #     {
        #         "timezone": "CST",
        #         "serverTime": 1647521860402,
        #         "rateLimits": [],
        #         "exchangeFilters": [],
        #         "symbols": [
        #           {
        #                "symbol": "OGNUSDT",
        #                "status": "1",
        #                "baseAsset": "OGN",
        #                "baseAssetPrecision": "2",
        #                "quoteAsset": "USDT",
        #                "quoteAssetPrecision": "4",
        #                "orderTypes": [
        #                    "LIMIT",
        #                    "LIMIT_MAKER"
        #                ],
        #                "baseCommissionPrecision": "2",
        #                "quoteCommissionPrecision": "4",
        #                "quoteOrderQtyMarketAllowed": False,
        #                "isSpotTradingAllowed": True,
        #                "isMarginTradingAllowed": True,
        #                "permissions": [
        #                    "SPOT",
        #                    "MARGIN"
        #                ],
        #                "filters": [],
        #                "baseSizePrecision": "0.01",  # self turned out to be a minimum base amount for order
        #                "maxQuoteAmount": "5000000",
        #                "makerCommission": "0.002",
        #                "takerCommission": "0.002"
        #                "quoteAmountPrecision": "5",  # self turned out to be a minimum cost amount for order
        #                "quotePrecision": "4",  # deprecated in favor of 'quoteAssetPrecision'( https://dev.binance.vision/t/what-is-the-difference-between-quoteprecision-and-quoteassetprecision/4333 )
        #                # note, "icebergAllowed" & "ocoAllowed" fields were recently removed
        #            },
        #         ]
        #     }
        #
        # Notes:
        # - 'quoteAssetPrecision' & 'baseAssetPrecision' are not currency's real blockchain precision(to view currency's actual individual precision, refer to fetchCurrencies() method).
        #
        data = self.safe_value(response, 'symbols', [])
        result = []
        for i in range(0, len(data)):
            market = data[i]
            id = self.safe_string(market, 'symbol')
            baseId = self.safe_string(market, 'baseAsset')
            quoteId = self.safe_string(market, 'quoteAsset')
            base = self.safe_currency_code(baseId)
            quote = self.safe_currency_code(quoteId)
            status = self.safe_string(market, 'status')
            isSpotTradingAllowed = self.safe_value(market, 'isSpotTradingAllowed')
            active = False
            if (status == '1') and (isSpotTradingAllowed):
                active = True
            isMarginTradingAllowed = self.safe_value(market, 'isMarginTradingAllowed')
            makerCommission = self.safe_number(market, 'makerCommission')
            takerCommission = self.safe_number(market, 'takerCommission')
            maxQuoteAmount = self.safe_number(market, 'maxQuoteAmount')
            result.append({
                'id': id,
                'symbol': base + '/' + quote,
                'base': base,
                'quote': quote,
                'settle': None,
                'baseId': baseId,
                'quoteId': quoteId,
                'settleId': None,
                'type': 'spot',
                'spot': True,
                'margin': isMarginTradingAllowed,
                'swap': False,
                'future': False,
                'option': False,
                'active': active,
                'contract': False,
                'linear': None,
                'inverse': None,
                'taker': takerCommission,
                'maker': makerCommission,
                'contractSize': None,
                'expiry': None,
                'expiryDatetime': None,
                'strike': None,
                'optionType': None,
                'precision': {
                    'amount': self.parse_number(self.parse_precision(self.safe_string(market, 'baseAssetPrecision'))),
                    'price': self.parse_number(self.parse_precision(self.safe_string(market, 'quoteAssetPrecision'))),
                },
                'limits': {
                    'leverage': {
                        'min': None,
                        'max': None,
                    },
                    'amount': {
                        'min': self.safe_number(market, 'baseSizePrecision'),
                        'max': None,
                    },
                    'price': {
                        'min': None,
                        'max': None,
                    },
                    'cost': {
                        'min': self.safe_number(market, 'quoteAmountPrecision'),
                        'max': maxQuoteAmount,
                    },
                },
                'created': None,
                'info': market,
            })
        return result

    async def fetch_swap_markets(self, params={}):
        """
 @ignore
        retrieves data on all swap markets for mexc

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-contract-information

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        currentRl: number = self.rateLimit
        self.set_property(self, 'rateLimit', 10)  # see comment: https://github.com/ccxt/ccxt/pull/23698
        response = await self.contractPublicGetDetail(params)
        self.set_property(self, 'rateLimit', currentRl)
        #
        #     {
        #         "success":true,
        #         "code":0,
        #         "data":[
        #             {
        #                 "symbol":"BTC_USDT",
        #                 "displayName":"BTC_USDT永续",
        #                 "displayNameEn":"BTC_USDT SWAP",
        #                 "positionOpenType":3,
        #                 "baseCoin":"BTC",
        #                 "quoteCoin":"USDT",
        #                 "settleCoin":"USDT",
        #                 "contractSize":0.0001,
        #                 "minLeverage":1,
        #                 "maxLeverage":125,
        #                 "priceScale":2,  # seems useless atm,'s just how UI shows the price, i.e. 29583.50 for BTC/USDT:USDT, while price ticksize is 0.5
        #                 "volScale":0,  # probably: contract amount precision
        #                 "amountScale":4,  # probably: quote currency precision
        #                 "priceUnit":0.5,  # price tick size
        #                 "volUnit":1,  # probably: contract tick size
        #                 "minVol":1,
        #                 "maxVol":1000000,
        #                 "bidLimitPriceRate":0.1,
        #                 "askLimitPriceRate":0.1,
        #                 "takerFeeRate":0.0006,
        #                 "makerFeeRate":0.0002,
        #                 "maintenanceMarginRate":0.004,
        #                 "initialMarginRate":0.008,
        #                 "riskBaseVol":10000,
        #                 "riskIncrVol":200000,
        #                 "riskIncrMmr":0.004,
        #                 "riskIncrImr":0.004,
        #                 "riskLevelLimit":5,
        #                 "priceCoefficientVariation":0.1,
        #                 "indexOrigin":["BINANCE","GATEIO","HUOBI","MXC"],
        #                 "state":0,  # 0 enabled, 1 delivery, 2 completed, 3 offline, 4 pause
        #                 "isNew":false,
        #                 "isHot":true,
        #                 "isHidden":false
        #             },
        #         ]
        #     }
        #
        data = self.safe_value(response, 'data', [])
        result = []
        for i in range(0, len(data)):
            market = data[i]
            id = self.safe_string(market, 'symbol')
            baseId = self.safe_string(market, 'baseCoin')
            quoteId = self.safe_string(market, 'quoteCoin')
            settleId = self.safe_string(market, 'settleCoin')
            base = self.safe_currency_code(baseId)
            quote = self.safe_currency_code(quoteId)
            settle = self.safe_currency_code(settleId)
            state = self.safe_string(market, 'state')
            isLinear = quote == settle
            result.append({
                'id': id,
                'symbol': base + '/' + quote + ':' + settle,
                'base': base,
                'quote': quote,
                'settle': settle,
                'baseId': baseId,
                'quoteId': quoteId,
                'settleId': settleId,
                'type': 'swap',
                'spot': False,
                'margin': False,
                'swap': True,
                'future': False,
                'option': False,
                'active': (state == '0'),
                'contract': True,
                'linear': isLinear,
                'inverse': not isLinear,
                'taker': self.safe_number(market, 'takerFeeRate'),
                'maker': self.safe_number(market, 'makerFeeRate'),
                'contractSize': self.safe_number(market, 'contractSize'),
                'expiry': None,
                'expiryDatetime': None,
                'strike': None,
                'optionType': None,
                'precision': {
                    'amount': self.safe_number(market, 'volUnit'),
                    'price': self.safe_number(market, 'priceUnit'),
                },
                'limits': {
                    'leverage': {
                        'min': self.safe_number(market, 'minLeverage'),
                        'max': self.safe_number(market, 'maxLeverage'),
                    },
                    'amount': {
                        'min': self.safe_number(market, 'minVol'),
                        'max': self.safe_number(market, 'maxVol'),
                    },
                    'price': {
                        'min': None,
                        'max': None,
                    },
                    'cost': {
                        'min': None,
                        'max': None,
                    },
                },
                'created': None,
                'info': market,
            })
        return result

    async def fetch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#order-book
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-contract-s-depth-information

        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data
        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        if limit is not None:
            request['limit'] = limit
        orderbook = None
        if market['spot']:
            response = await self.spotPublicGetDepth(self.extend(request, params))
            #
            #     {
            #         "lastUpdateId": "744267132",
            #         "bids": [
            #             ["40838.50","0.387864"],
            #             ["40837.95","0.008400"],
            #         ],
            #         "asks": [
            #             ["40838.61","6.544908"],
            #             ["40838.88","0.498000"],
            #         ]
            #     }
            #
            spotTimestamp = self.safe_integer(response, 'timestamp')
            orderbook = self.parse_order_book(response, symbol, spotTimestamp)
            orderbook['nonce'] = self.safe_integer(response, 'lastUpdateId')
        elif market['swap']:
            response = await self.contractPublicGetDepthSymbol(self.extend(request, params))
            #
            #     {
            #         "success":true,
            #         "code":0,
            #         "data":{
            #             "asks":[
            #                 [3445.72,48379,1],
            #                 [3445.75,34994,1],
            #             ],
            #             "bids":[
            #                 [3445.55,44081,1],
            #                 [3445.51,24857,1],
            #             ],
            #             "version":2827730444,
            #             "timestamp":1634117846232
            #         }
            #     }
            #
            data = self.safe_value(response, 'data')
            timestamp = self.safe_integer(data, 'timestamp')
            orderbook = self.parse_order_book(data, symbol, timestamp)
            orderbook['nonce'] = self.safe_integer(data, 'version')
        return orderbook

    def parse_bid_ask(self, bidask, priceKey: IndexType = 0, amountKey: IndexType = 1, countOrIdKey: IndexType = 2):
        countKey = 2
        price = self.safe_number(bidask, priceKey)
        amount = self.safe_number(bidask, amountKey)
        count = self.safe_number(bidask, countKey)
        if count is not None:
            return [price, amount, count]
        return [price, amount]

    async def fetch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#recent-trades-list
        https://mexcdevelop.github.io/apidocs/spot_v3_en/#compressed-aggregate-trades-list
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-contract-transaction-data

        get the list of most recent trades for a particular symbol
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: *spot only* *since must be defined* the latest time in ms to fetch entries for
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        if limit is not None:
            request['limit'] = limit
        trades = None
        if market['spot']:
            until = self.safe_integer_n(params, ['endTime', 'until'])
            if since is not None:
                request['startTime'] = since
                if until is None:
                    raise ArgumentsRequired(self.id + ' fetchTrades() requires an until parameter when since is provided')
            if until is not None:
                if since is None:
                    raise ArgumentsRequired(self.id + ' fetchTrades() requires a since parameter when until is provided')
                request['endTime'] = until
            method = self.safe_string(self.options, 'fetchTradesMethod', 'spotPublicGetAggTrades')
            method = self.safe_string(params, 'method', method)  # AggTrades, HistoricalTrades, Trades
            params = self.omit(params, ['method'])
            if method == 'spotPublicGetAggTrades':
                trades = await self.spotPublicGetAggTrades(self.extend(request, params))
            elif method == 'spotPublicGetHistoricalTrades':
                trades = await self.spotPublicGetHistoricalTrades(self.extend(request, params))
            elif method == 'spotPublicGetTrades':
                trades = await self.spotPublicGetTrades(self.extend(request, params))
            else:
                raise NotSupported(self.id + ' fetchTrades() not support self method')
            #
            #     /trades, /historicalTrades
            #
            #     [
            #         {
            #             "id": null,
            #             "price": "40798.94",
            #             "qty": "0.000508",
            #             "quoteQty": "20.72586152",
            #             "time": "1647546934374",
            #             "isBuyerMaker": True,
            #             "isBestMatch": True
            #         },
            #     ]
            #
            #     /aggrTrades
            #
            #     [
            #         {
            #           "a": null,
            #           "f": null,
            #           "l": null,
            #           "p": "40679",
            #           "q": "0.001309",
            #           "T": 1647551328000,
            #           "m": True,
            #           "M": True
            #         },
            #     ]
            #
        elif market['swap']:
            response = await self.contractPublicGetDealsSymbol(self.extend(request, params))
            #
            #     {
            #         "success": True,
            #         "code": 0,
            #         "data": [
            #             {
            #                 "p": 31199,
            #                 "v": 18,
            #                 "T": 1,
            #                 "O": 3,
            #                 "M": 2,
            #                 "t": 1609831235985
            #             },
            #         ]
            #     }
            #
            trades = self.safe_value(response, 'data')
        return self.parse_trades(trades, market, since, limit)

    def parse_trade(self, trade: dict, market: Market = None) -> Trade:
        id = None
        timestamp = None
        orderId = None
        symbol = None
        fee = None
        type = None
        side = None
        takerOrMaker = None
        priceString = None
        amountString = None
        costString = None
        # if swap
        if 'v' in trade:
            #
            # swap: fetchTrades
            #
            #     {
            #         "p": 31199,
            #         "v": 18,
            #         "T": 1,
            #         "O": 3,
            #         "M": 2,
            #         "t": 1609831235985
            #     }
            #
            timestamp = self.safe_integer(trade, 't')
            market = self.safe_market(None, market)
            symbol = market['symbol']
            priceString = self.safe_string(trade, 'p')
            amountString = self.safe_string(trade, 'v')
            side = self.parse_order_side(self.safe_string(trade, 'T'))
            takerOrMaker = 'taker'
        else:
            #
            # spot: fetchTrades(for aggTrades)
            #
            #         {
            #             "a": null,
            #             "f": null,
            #             "l": null,
            #             "p": "40679",
            #             "q": "0.001309",
            #             "T": 1647551328000,
            #             "m": True,
            #             "M": True
            #         }
            #
            # spot: fetchMyTrades, fetchOrderTrades
            #
            #         {
            #             "symbol": "BTCUSDT",
            #             "id": "133948532984922113",
            #             "orderId": "133948532531949568",
            #             "orderListId": "-1",
            #             "price": "41995.51",
            #             "qty": "0.0002",
            #             "quoteQty": "8.399102",
            #             "commission": "0.016798204",
            #             "commissionAsset": "USDT",
            #             "time": "1647718055000",
            #             "isBuyer": True,
            #             "isMaker": False,
            #             "isBestMatch": True
            #         }
            #
            # swap: fetchMyTrades, fetchOrderTrades
            #
            #         {
            #             "id": "299444585",
            #             "symbol": "STEPN_USDT",
            #             "side": "1",
            #             "vol": "1",
            #             "price": "2.45455",
            #             "feeCurrency": "USDT",
            #             "fee": "0.00147273",
            #             "timestamp": "1648924557000",
            #             "profit": "0",
            #             "category": "1",
            #             "orderId": "265307163526610432",
            #             "positionMode": "1",
            #             "taker": True
            #         }
            #
            marketId = self.safe_string(trade, 'symbol')
            market = self.safe_market(marketId, market)
            symbol = market['symbol']
            id = self.safe_string_2(trade, 'id', 'a')
            priceString = self.safe_string_2(trade, 'price', 'p')
            orderId = self.safe_string(trade, 'orderId')
            # if swap
            if 'positionMode' in trade:
                timestamp = self.safe_integer(trade, 'timestamp')
                amountString = self.safe_string(trade, 'vol')
                side = self.parse_order_side(self.safe_string(trade, 'side'))
                fee = {
                    'cost': self.safe_string(trade, 'fee'),
                    'currency': self.safe_currency_code(self.safe_string(trade, 'feeCurrency')),
                }
                takerOrMaker = 'taker' if self.safe_value(trade, 'taker') else 'maker'
            else:
                timestamp = self.safe_integer_2(trade, 'time', 'T')
                amountString = self.safe_string_2(trade, 'qty', 'q')
                costString = self.safe_string(trade, 'quoteQty')
                isBuyer = self.safe_value(trade, 'isBuyer')
                isMaker = self.safe_value(trade, 'isMaker')
                buyerMaker = self.safe_value_2(trade, 'isBuyerMaker', 'm')
                if isMaker is not None:
                    takerOrMaker = 'maker' if isMaker else 'taker'
                if isBuyer is not None:
                    side = 'buy' if isBuyer else 'sell'
                if buyerMaker is not None:
                    side = 'sell' if buyerMaker else 'buy'
                    takerOrMaker = 'taker'
                feeAsset = self.safe_string(trade, 'commissionAsset')
                if feeAsset is not None:
                    fee = {
                        'cost': self.safe_string(trade, 'commission'),
                        'currency': self.safe_currency_code(feeAsset),
                    }
        if id is None:
            id = self.synthetic_trade_id(market, timestamp, side, amountString, priceString, type, takerOrMaker)
        return self.safe_trade({
            'id': id,
            'order': orderId,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'symbol': symbol,
            'type': type,
            'side': side,
            'takerOrMaker': takerOrMaker,
            'price': priceString,
            'amount': amountString,
            'cost': costString,
            'fee': fee,
            'info': trade,
        }, market)

    def synthetic_trade_id(self, market=None, timestamp=None, side=None, amount=None, price=None, orderType=None, takerOrMaker=None):
        # TODO: can be unified method? self approach is being used by multiple exchanges(mexc, woo-coinsbit, dydx, ...)
        id = ''
        if timestamp is not None:
            id = self.number_to_string(timestamp) + '-' + self.safe_string(market, 'id', '_')
            if side is not None:
                id += '-' + side
            if amount is not None:
                id += '-' + self.number_to_string(amount)
            if price is not None:
                id += '-' + self.number_to_string(price)
            if takerOrMaker is not None:
                id += '-' + takerOrMaker
            if orderType is not None:
                id += '-' + orderType
        return id

    async def fetch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#kline-candlestick-data
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#k-line-data

        fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: timestamp in ms of the latest candle to fetch
        :param boolean [params.paginate]: default False, when True will automatically paginate by calling self endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        market = self.market(symbol)
        maxLimit = 1000 if (market['spot']) else 2000
        paginate = False
        paginate, params = self.handle_option_and_params(params, 'fetchOHLCV', 'paginate', False)
        if paginate:
            return await self.fetch_paginated_call_deterministic('fetchOHLCV', symbol, since, limit, timeframe, params, maxLimit)
        options = self.safe_value(self.options, 'timeframes', {})
        timeframes = self.safe_value(options, market['type'], {})
        timeframeValue = self.safe_string(timeframes, timeframe)
        duration = self.parse_timeframe(timeframe) * 1000
        request: dict = {
            'symbol': market['id'],
            'interval': timeframeValue,
        }
        candles = None
        if market['spot']:
            until = self.safe_integer_n(params, ['until', 'endTime'])
            if since is not None:
                request['startTime'] = since
                if until is None:
                    # we have to calculate it assuming we can get at most 2000 entries per request
                    end = self.sum(since, maxLimit * duration)
                    now = self.milliseconds()
                    request['endTime'] = min(end, now)
            if limit is not None:
                request['limit'] = limit
            if until is not None:
                params = self.omit(params, ['until'])
                request['endTime'] = until
            response = await self.spotPublicGetKlines(self.extend(request, params))
            #
            #     [
            #       [
            #         1640804880000,
            #         "47482.36",
            #         "47482.36",
            #         "47416.57",
            #         "47436.1",
            #         "3.550717",
            #         1640804940000,
            #         "168387.3"
            #       ],
            #     ]
            #
            candles = response
        elif market['swap']:
            until = self.safe_integer_product_n(params, ['until', 'endTime'], 0.001)
            if since is not None:
                request['start'] = self.parse_to_int(since / 1000)
            if until is not None:
                params = self.omit(params, ['until'])
                request['end'] = until
            priceType = self.safe_string(params, 'price', 'default')
            params = self.omit(params, 'price')
            response = None
            if priceType == 'default':
                response = await self.contractPublicGetKlineSymbol(self.extend(request, params))
            elif priceType == 'index':
                response = await self.contractPublicGetKlineIndexPriceSymbol(self.extend(request, params))
            elif priceType == 'mark':
                response = await self.contractPublicGetKlineFairPriceSymbol(self.extend(request, params))
            else:
                raise NotSupported(self.id + ' fetchOHLCV() not support self price type, [default, index, mark]')
            #
            #     {
            #         "success":true,
            #         "code":0,
            #         "data":{
            #             "time":[1634052300,1634052360,1634052420],
            #             "open":[3492.2,3491.3,3495.65],
            #             "close":[3491.3,3495.65,3495.2],
            #             "high":[3495.85,3496.55,3499.4],
            #             "low":[3491.15,3490.9,3494.2],
            #             "vol":[1740.0,351.0,314.0],
            #             "amount":[60793.623,12260.4885,10983.1375],
            #         }
            #     }
            #
            data = self.safe_value(response, 'data')
            candles = self.convert_trading_view_to_ohlcv(data, 'time', 'open', 'high', 'low', 'close', 'vol')
        return self.parse_ohlcvs(candles, market, timeframe, since, limit)

    def parse_ohlcv(self, ohlcv, market: Market = None) -> list:
        return [
            self.safe_integer(ohlcv, 0),
            self.safe_number(ohlcv, 1),
            self.safe_number(ohlcv, 2),
            self.safe_number(ohlcv, 3),
            self.safe_number(ohlcv, 4),
            self.safe_number(ohlcv, 5),
        ]

    async def fetch_tickers(self, symbols: Strings = None, params={}) -> Tickers:
        """
        fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#24hr-ticker-price-change-statistics
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-contract-trend-data

        :param str[]|None symbols: unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `ticker structures <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        isSingularMarket = False
        if symbols is not None:
            length = len(symbols)
            isSingularMarket = length == 1
            firstSymbol = self.safe_string(symbols, 0)
            market = self.market(firstSymbol)
        marketType, query = self.handle_market_type_and_params('fetchTickers', market, params)
        tickers = None
        if isSingularMarket:
            request['symbol'] = market['id']
        if marketType == 'spot':
            tickers = await self.spotPublicGetTicker24hr(self.extend(request, query))
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "priceChange": "184.34",
            #             "priceChangePercent": "0.00400048",
            #             "prevClosePrice": "46079.37",
            #             "lastPrice": "46263.71",
            #             "lastQty": "",
            #             "bidPrice": "46260.38",
            #             "bidQty": "",
            #             "askPrice": "46260.41",
            #             "askQty": "",
            #             "openPrice": "46079.37",
            #             "highPrice": "47550.01",
            #             "lowPrice": "45555.5",
            #             "volume": "1732.461487",
            #             "quoteVolume": null,
            #             "openTime": 1641349500000,
            #             "closeTime": 1641349582808,
            #             "count": null
            #         }
            #     ]
            #
        elif marketType == 'swap':
            response = await self.contractPublicGetTicker(self.extend(request, query))
            #
            #     {
            #         "success":true,
            #         "code":0,
            #         "data":[
            #             {
            #                 "symbol":"ETH_USDT",
            #                 "lastPrice":3581.3,
            #                 "bid1":3581.25,
            #                 "ask1":3581.5,
            #                 "volume24":4045530,
            #                 "amount24":141331823.5755,
            #                 "holdVol":5832946,
            #                 "lower24Price":3413.4,
            #                 "high24Price":3588.7,
            #                 "riseFallRate":0.0275,
            #                 "riseFallValue":95.95,
            #                 "indexPrice":3580.7852,
            #                 "fairPrice":3581.08,
            #                 "fundingRate":0.000063,
            #                 "maxBidPrice":3938.85,
            #                 "minAskPrice":3222.7,
            #                 "timestamp":1634162885016
            #             },
            #         ]
            #     }
            #
            tickers = self.safe_value(response, 'data', [])
        # when it's single symbol request, the returned structure is different(singular object) for both spot & swap, thus we need to wrap inside array
        if isSingularMarket:
            tickers = [tickers]
        return self.parse_tickers(tickers, symbols)

    async def fetch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#24hr-ticker-price-change-statistics
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-contract-trend-data

        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        marketType, query = self.handle_market_type_and_params('fetchTicker', market, params)
        ticker = None
        request: dict = {
            'symbol': market['id'],
        }
        if marketType == 'spot':
            ticker = await self.spotPublicGetTicker24hr(self.extend(request, query))
            #
            #     {
            #         "symbol": "BTCUSDT",
            #         "priceChange": "184.34",
            #         "priceChangePercent": "0.00400048",
            #         "prevClosePrice": "46079.37",
            #         "lastPrice": "46263.71",
            #         "lastQty": "",
            #         "bidPrice": "46260.38",
            #         "bidQty": "",
            #         "askPrice": "46260.41",
            #         "askQty": "",
            #         "openPrice": "46079.37",
            #         "highPrice": "47550.01",
            #         "lowPrice": "45555.5",
            #         "volume": "1732.461487",
            #         "quoteVolume": null,
            #         "openTime": 1641349500000,
            #         "closeTime": 1641349582808,
            #         "count": null
            #     }
            #
        elif marketType == 'swap':
            response = await self.contractPublicGetTicker(self.extend(request, query))
            #
            #     {
            #         "success":true,
            #         "code":0,
            #         "data":{
            #             "symbol":"ETH_USDT",
            #             "lastPrice":3581.3,
            #             "bid1":3581.25,
            #             "ask1":3581.5,
            #             "volume24":4045530,
            #             "amount24":141331823.5755,
            #             "holdVol":5832946,
            #             "lower24Price":3413.4,
            #             "high24Price":3588.7,
            #             "riseFallRate":0.0275,
            #             "riseFallValue":95.95,
            #             "indexPrice":3580.7852,
            #             "fairPrice":3581.08,
            #             "fundingRate":0.000063,
            #             "maxBidPrice":3938.85,
            #             "minAskPrice":3222.7,
            #             "timestamp":1634162885016
            #         }
            #     }
            #
            ticker = self.safe_value(response, 'data', {})
        # when it's single symbol request, the returned structure is different(singular object) for both spot & swap, thus we need to wrap inside array
        return self.parse_ticker(ticker, market)

    def parse_ticker(self, ticker: dict, market: Market = None) -> Ticker:
        marketId = self.safe_string(ticker, 'symbol')
        market = self.safe_market(marketId, market)
        timestamp = None
        bid = None
        ask = None
        bidVolume = None
        askVolume = None
        baseVolume = None
        quoteVolume = None
        open = None
        high = None
        low = None
        changePcnt = None
        changeValue = None
        prevClose = None
        isSwap = self.safe_value(market, 'swap')
        # if swap
        if isSwap or ('timestamp' in ticker):
            #
            #     {
            #         "symbol": "ETH_USDT",
            #         "lastPrice": 3581.3,
            #         "bid1": 3581.25,
            #         "ask1": 3581.5,
            #         "volume24": 4045530,
            #         "amount24": 141331823.5755,
            #         "holdVol": 5832946,
            #         "lower24Price": 3413.4,
            #         "high24Price": 3588.7,
            #         "riseFallRate": 0.0275,
            #         "riseFallValue": 95.95,
            #         "indexPrice": 3580.7852,
            #         "fairPrice": 3581.08,
            #         "fundingRate": 0.000063,
            #         "maxBidPrice": 3938.85,
            #         "minAskPrice": 3222.7,
            #         "timestamp": 1634162885016
            #     }
            #
            timestamp = self.safe_integer(ticker, 'timestamp')
            bid = self.safe_string(ticker, 'bid1')
            ask = self.safe_string(ticker, 'ask1')
            baseVolume = self.safe_string(ticker, 'volume24')
            quoteVolume = self.safe_string(ticker, 'amount24')
            high = self.safe_string(ticker, 'high24Price')
            low = self.safe_string(ticker, 'lower24Price')
            changeValue = self.safe_string(ticker, 'riseFallValue')
            changePcnt = self.safe_string(ticker, 'riseFallRate')
            changePcnt = Precise.string_mul(changePcnt, '100')
        else:
            #
            #     {
            #         "symbol": "BTCUSDT",
            #         "priceChange": "184.34",
            #         "priceChangePercent": "0.00400048",
            #         "prevClosePrice": "46079.37",
            #         "lastPrice": "46263.71",
            #         "lastQty": "",
            #         "bidPrice": "46260.38",
            #         "bidQty": "",
            #         "askPrice": "46260.41",
            #         "askQty": "",
            #         "openPrice": "46079.37",
            #         "highPrice": "47550.01",
            #         "lowPrice": "45555.5",
            #         "volume": "1732.461487",
            #         "quoteVolume": null,
            #         "openTime": 1641349500000,
            #         "closeTime": 1641349582808,
            #         "count": null
            #     }
            #
            timestamp = self.safe_integer(ticker, 'closeTime')
            bid = self.safe_string(ticker, 'bidPrice')
            ask = self.safe_string(ticker, 'askPrice')
            bidVolume = self.safe_string(ticker, 'bidQty')
            askVolume = self.safe_string(ticker, 'askQty')
            if Precise.string_eq(bidVolume, '0'):
                bidVolume = None
            if Precise.string_eq(askVolume, '0'):
                askVolume = None
            baseVolume = self.safe_string(ticker, 'volume')
            quoteVolume = self.safe_string(ticker, 'quoteVolume')
            open = self.safe_string(ticker, 'openPrice')
            high = self.safe_string(ticker, 'highPrice')
            low = self.safe_string(ticker, 'lowPrice')
            prevClose = self.safe_string(ticker, 'prevClosePrice')
            changeValue = self.safe_string(ticker, 'priceChange')
            changePcnt = self.safe_string(ticker, 'priceChangePercent')
            changePcnt = Precise.string_mul(changePcnt, '100')
        return self.safe_ticker({
            'symbol': market['symbol'],
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'open': open,
            'high': high,
            'low': low,
            'close': self.safe_string(ticker, 'lastPrice'),
            'bid': bid,
            'bidVolume': bidVolume,
            'ask': ask,
            'askVolume': askVolume,
            'vwap': None,
            'previousClose': prevClose,
            'change': changeValue,
            'percentage': changePcnt,
            'average': None,
            'baseVolume': baseVolume,
            'quoteVolume': quoteVolume,
            'info': ticker,
        }, market)

    async def fetch_bids_asks(self, symbols: Strings = None, params={}):
        """
        fetches the bid and ask price and volume for multiple markets

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#symbol-order-book-ticker

        :param str[]|None symbols: unified symbols of the markets to fetch the bids and asks for, all markets are returned if not assigned
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `ticker structures <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        market = None
        isSingularMarket = False
        if symbols is not None:
            length = len(symbols)
            isSingularMarket = length == 1
            market = self.market(symbols[0])
        marketType, query = self.handle_market_type_and_params('fetchBidsAsks', market, params)
        tickers = None
        if marketType == 'spot':
            tickers = await self.spotPublicGetTickerBookTicker(query)
            #
            #     [
            #       {
            #         "symbol": "AEUSDT",
            #         "bidPrice": "0.11001",
            #         "bidQty": "115.59",
            #         "askPrice": "0.11127",
            #         "askQty": "215.48"
            #       },
            #     ]
            #
        elif marketType == 'swap':
            raise NotSupported(self.id + ' fetchBidsAsks() is not available for ' + marketType + ' markets')
        # when it's single symbol request, the returned structure is different(singular object) for both spot & swap, thus we need to wrap inside array
        if isSingularMarket:
            tickers = [tickers]
        return self.parse_tickers(tickers, symbols)

    async def create_market_buy_order_with_cost(self, symbol: str, cost: float, params={}):
        """
        create a market buy order by providing the symbol and cost

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#new-order

        :param str symbol: unified symbol of the market to create an order in
        :param float cost: how much you want to trade in units of the quote currency
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        if not market['spot']:
            raise NotSupported(self.id + ' createMarketBuyOrderWithCost() supports spot orders only')
        req = {
            'cost': cost,
        }
        return await self.create_order(symbol, 'market', 'buy', 0, None, self.extend(req, params))

    async def create_market_sell_order_with_cost(self, symbol: str, cost: float, params={}):
        """
        create a market sell order by providing the symbol and cost

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#new-order

        :param str symbol: unified symbol of the market to create an order in
        :param float cost: how much you want to trade in units of the quote currency
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        if not market['spot']:
            raise NotSupported(self.id + ' createMarketBuyOrderWithCost() supports spot orders only')
        req = {
            'cost': cost,
        }
        return await self.create_order(symbol, 'market', 'sell', 0, None, self.extend(req, params))

    async def create_order(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}):
        """
        create a trade order

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#new-order
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#order-under-maintenance
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#trigger-order-under-maintenance

        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: only 'isolated' is supported for spot-margin trading
        :param float [params.triggerPrice]: The price at which a trigger order is triggered at
        :param bool [params.postOnly]: if True, the order will only be posted if it will be a maker order
        :param bool [params.reduceOnly]: *contract only* indicates if self order is to reduce the size of a position
        :param bool [params.hedged]: *swap only* True for hedged mode, False for one way mode, default is False
        :param str [params.timeInForce]: 'IOC' or 'FOK', default is 'GTC'
 EXCHANGE SPECIFIC PARAMETERS
        :param int [params.leverage]: *contract only* leverage is necessary on isolated margin
        :param long [params.positionId]: *contract only* it is recommended to hasattr(self, fill) parameter when closing a position
        :param str [params.externalOid]: *contract only* external order ID
        :param int [params.positionMode]: *contract only*  1:hedge, 2:one-way, default: the user's current config
        :param boolean [params.test]: *spot only* whether to use the test endpoint or not, default is False
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        marginMode, query = self.handle_margin_mode_and_params('createOrder', params)
        if market['spot']:
            return await self.create_spot_order(market, type, side, amount, price, marginMode, query)
        else:
            return await self.create_swap_order(market, type, side, amount, price, marginMode, query)

    def create_spot_order_request(self, market, type, side, amount, price=None, marginMode=None, params={}):
        symbol = market['symbol']
        orderSide = side.upper()
        request: dict = {
            'symbol': market['id'],
            'side': orderSide,
            'type': type.upper(),
        }
        if type == 'market':
            cost = self.safe_number_2(params, 'cost', 'quoteOrderQty')
            params = self.omit(params, 'cost')
            if cost is not None:
                amount = cost
                request['quoteOrderQty'] = self.cost_to_precision(symbol, amount)
            else:
                if price is None:
                    request['quantity'] = self.amount_to_precision(symbol, amount)
                else:
                    amountString = self.number_to_string(amount)
                    priceString = self.number_to_string(price)
                    quoteAmount = Precise.string_mul(amountString, priceString)
                    amount = quoteAmount
                    request['quoteOrderQty'] = self.cost_to_precision(symbol, amount)
        else:
            request['quantity'] = self.amount_to_precision(symbol, amount)
        if price is not None:
            request['price'] = self.price_to_precision(symbol, price)
        clientOrderId = self.safe_string(params, 'clientOrderId')
        if clientOrderId is not None:
            request['newClientOrderId'] = clientOrderId
            params = self.omit(params, ['type', 'clientOrderId'])
        if marginMode is not None:
            if marginMode != 'isolated':
                raise BadRequest(self.id + ' createOrder() does not support marginMode ' + marginMode + ' for spot-margin trading')
        postOnly = None
        postOnly, params = self.handle_post_only(type == 'market', type == 'LIMIT_MAKER', params)
        if postOnly:
            request['type'] = 'LIMIT_MAKER'
        tif = self.safe_string(params, 'timeInForce')
        if tif is not None:
            params = self.omit(params, 'timeInForce')
            if tif == 'IOC':
                request['type'] = 'IMMEDIATE_OR_CANCEL'
            elif tif == 'FOK':
                request['type'] = 'FILL_OR_KILL'
        return self.extend(request, params)

    async def create_spot_order(self, market, type, side, amount, price=None, marginMode=None, params={}):
        """
 @ignore
        create a trade order

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#new-order

        :param str market: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param str [marginMode]: only 'isolated' is supported for spot-margin trading
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param bool [params.postOnly]: if True, the order will only be posted if it will be a maker order
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        test = self.safe_bool(params, 'test', False)
        params = self.omit(params, 'test')
        request = self.create_spot_order_request(market, type, side, amount, price, marginMode, params)
        response = None
        if test:
            response = await self.spotPrivatePostOrderTest(request)
        else:
            response = await self.spotPrivatePostOrder(request)
        #
        # spot
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "123738410679123456",
        #         "orderListId": -1
        #     }
        #
        # margin
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "762634301354414080",
        #         "clientOrderId": null,
        #         "isIsolated": True,
        #         "transactTime": 1661992652132
        #     }
        #
        order = self.parse_order(response, market)
        order['side'] = side
        order['type'] = type
        if self.safe_string(order, 'price') is None:
            order['price'] = price
        if self.safe_string(order, 'amount') is None:
            order['amount'] = amount
        return order

    async def create_swap_order(self, market, type, side, amount, price=None, marginMode=None, params={}):
        """
 @ignore
        create a trade order

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#new-order
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#order-under-maintenance
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#trigger-order-under-maintenance

        :param str market: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param str [marginMode]: only 'isolated' is supported for spot-margin trading
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param float [params.triggerPrice]: The price at which a trigger order is triggered at
        :param bool [params.postOnly]: if True, the order will only be posted if it will be a maker order
        :param bool [params.reduceOnly]: indicates if self order is to reduce the size of a position
        :param bool [params.hedged]: *swap only* True for hedged mode, False for one way mode, default is False

 EXCHANGE SPECIFIC PARAMETERS
        :param int [params.leverage]: leverage is necessary on isolated margin
        :param long [params.positionId]: it is recommended to hasattr(self, fill) parameter when closing a position
        :param str [params.externalOid]: external order ID
        :param int [params.positionMode]: 1:hedge, 2:one-way, default: the user's current config
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        symbol = market['symbol']
        unavailableContracts = self.safe_value(self.options, 'unavailableContracts', {})
        isContractUnavaiable = self.safe_bool(unavailableContracts, symbol, False)
        if isContractUnavaiable:
            raise NotSupported(self.id + ' createSwapOrder() does not support yet self symbol:' + symbol)
        openType = None
        if marginMode is not None:
            if marginMode == 'cross':
                openType = 2
            elif marginMode == 'isolated':
                openType = 1
            else:
                raise ArgumentsRequired(self.id + ' createSwapOrder() marginMode parameter should be either "cross" or "isolated"')
        else:
            openType = self.safe_integer(params, 'openType', 2)  # defaulting to cross margin
        if (type != 'limit') and (type != 'market') and (type != 1) and (type != 2) and (type != 3) and (type != 4) and (type != 5) and (type != 6):
            raise InvalidOrder(self.id + ' createSwapOrder() order type must either limit, market, or 1 for limit orders, 2 for post-only orders, 3 for IOC orders, 4 for FOK orders, 5 for market orders or 6 to convert market price to current price')
        postOnly = None
        postOnly, params = self.handle_post_only(type == 'market', type == 2, params)
        if postOnly:
            type = 2
        elif type == 'limit':
            type = 1
        elif type == 'market':
            type = 6
        request: dict = {
            'symbol': market['id'],
            # 'price': float(self.price_to_precision(symbol, price)),
            'vol': float(self.amount_to_precision(symbol, amount)),
            # 'leverage': int,  # required for isolated margin
            # 'side': side,  # 1 open long, 2 close short, 3 open short, 4 close long
            #
            # supported order types
            #
            #     1 limit
            #     2 post only maker(PO)
            #     3 transact or cancel instantly(IOC)
            #     4 transact completely or cancel completely(FOK)
            #     5 market orders
            #     6 convert market price to current price
            #
            'type': type,
            'openType': openType,  # 1 isolated, 2 cross
            # 'positionId': 1394650,  # long, hasattr(self, filling) parameter when closing a position is recommended
            # 'externalOid': clientOrderId,
            # 'triggerPrice': 10.0,  # Required for trigger order
            # 'triggerType': 1,  # Required for trigger order 1: more than or equal, 2: less than or equal
            # 'executeCycle': 1,  # Required for trigger order 1: 24 hours,2: 7 days
            # 'trend': 1,  # Required for trigger order 1: latest price, 2: fair price, 3: index price
            # 'orderType': 1,  # Required for trigger order 1: limit order,2:Post Only Maker,3: close or cancel instantly ,4: close or cancel completely,5: Market order
        }
        if (type != 5) and (type != 6) and (type != 'market'):
            request['price'] = float(self.price_to_precision(symbol, price))
        if openType == 1:
            leverage = self.safe_integer(params, 'leverage')
            if leverage is None:
                raise ArgumentsRequired(self.id + ' createSwapOrder() requires a leverage parameter for isolated margin orders')
        reduceOnly = self.safe_bool(params, 'reduceOnly', False)
        hedged = self.safe_bool(params, 'hedged', False)
        sideInteger = None
        if hedged:
            if reduceOnly:
                params = self.omit(params, 'reduceOnly')  # hedged mode does not accept self parameter
                side = 'sell' if (side == 'buy') else 'buy'
            sideInteger = 1 if (side == 'buy') else 3
            request['positionMode'] = 1
        else:
            if reduceOnly:
                sideInteger = 2 if (side == 'buy') else 4
            else:
                sideInteger = 1 if (side == 'buy') else 3
        request['side'] = sideInteger
        clientOrderId = self.safe_string_2(params, 'clientOrderId', 'externalOid')
        if clientOrderId is not None:
            request['externalOid'] = clientOrderId
        triggerPrice = self.safe_number_2(params, 'triggerPrice', 'stopPrice')
        params = self.omit(params, ['clientOrderId', 'externalOid', 'postOnly', 'stopPrice', 'triggerPrice', 'hedged'])
        response = None
        if triggerPrice:
            request['triggerPrice'] = self.price_to_precision(symbol, triggerPrice)
            request['triggerType'] = self.safe_integer(params, 'triggerType', 1)
            request['executeCycle'] = self.safe_integer(params, 'executeCycle', 1)
            request['trend'] = self.safe_integer(params, 'trend', 1)
            request['orderType'] = self.safe_integer(params, 'orderType', 1)
            response = await self.contractPrivatePostPlanorderPlace(self.extend(request, params))
        else:
            response = await self.contractPrivatePostOrderSubmit(self.extend(request, params))
        #
        # Swap
        #     {"code":200,"data":"2ff3163e8617443cb9c6fc19d42b1ca4"}
        #
        # Trigger
        #     {"success":true,"code":0,"data":259208506303929856}
        #
        data = self.safe_string(response, 'data')
        return self.safe_order({'id': data}, market)

    async def create_orders(self, orders: List[OrderRequest], params={}):
        """
        *spot only*  *all orders must have the same symbol* create a list of trade orders

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#batch-orders

        :param Array orders: list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
        :param dict [params]: extra parameters specific to api endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        ordersRequests = []
        symbol = None
        for i in range(0, len(orders)):
            rawOrder = orders[i]
            marketId = self.safe_string(rawOrder, 'symbol')
            market = self.market(marketId)
            if not market['spot']:
                raise NotSupported(self.id + ' createOrders() is only supported for spot markets')
            if symbol is None:
                symbol = marketId
            else:
                if symbol != marketId:
                    raise BadRequest(self.id + ' createOrders() requires all orders to have the same symbol')
            type = self.safe_string(rawOrder, 'type')
            side = self.safe_string(rawOrder, 'side')
            amount = self.safe_value(rawOrder, 'amount')
            price = self.safe_value(rawOrder, 'price')
            orderParams = self.safe_value(rawOrder, 'params', {})
            marginMode = None
            marginMode, params = self.handle_margin_mode_and_params('createOrder', params)
            orderRequest = self.create_spot_order_request(market, type, side, amount, price, marginMode, orderParams)
            ordersRequests.append(orderRequest)
        request: dict = {
            'batchOrders': self.json(ordersRequests),
        }
        response = await self.spotPrivatePostBatchOrders(request)
        #
        # [
        #     {
        #       "symbol": "BTCUSDT",
        #       "orderId": "1196315350023612316",
        #       "newClientOrderId": "hio8279hbdsds",
        #       "orderListId": -1
        #     },
        #     {
        #       "newClientOrderId": "123456",
        #       "msg": "The minimum transaction volume cannot be less than:0.5USDT",
        #       "code": 30002
        #     },
        #     {
        #       "symbol": "BTCUSDT",
        #       "orderId": "1196315350023612318",
        #       "orderListId": -1
        #     }
        # ]
        #
        return self.parse_orders(response)

    async def fetch_order(self, id: str, symbol: Str = None, params={}):
        """
        fetches information on an order made by the user

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#query-order
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#query-the-order-based-on-the-order-number

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: only 'isolated' is supported, for spot-margin trading
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchOrder() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        data = None
        if market['spot']:
            clientOrderId = self.safe_string(params, 'clientOrderId')
            if clientOrderId is not None:
                params = self.omit(params, 'clientOrderId')
                request['origClientOrderId'] = clientOrderId
            else:
                request['orderId'] = id
            marginMode, query = self.handle_margin_mode_and_params('fetchOrder', params)
            if marginMode is not None:
                if marginMode != 'isolated':
                    raise BadRequest(self.id + ' fetchOrder() does not support marginMode ' + marginMode + ' for spot-margin trading')
                data = await self.spotPrivateGetMarginOrder(self.extend(request, query))
            else:
                data = await self.spotPrivateGetOrder(self.extend(request, query))
            #
            # spot
            #
            #     {
            #         "symbol": "BTCUSDT",
            #         "orderId": "133734823834147272",
            #         "orderListId": "-1",
            #         "clientOrderId": null,
            #         "price": "30000",
            #         "origQty": "0.0002",
            #         "executedQty": "0",
            #         "cummulativeQuoteQty": "0",
            #         "status": "CANCELED",
            #         "timeInForce": null,
            #         "type": "LIMIT",
            #         "side": "BUY",
            #         "stopPrice": null,
            #         "icebergQty": null,
            #         "time": "1647667102000",
            #         "updateTime": "1647708567000",
            #         "isWorking": True,
            #         "origQuoteOrderQty": "6"
            #     }
            #
            # margin
            #
            #     {
            #         "symbol": "BTCUSDT",
            #         "orderId": "763307297891028992",
            #         "orderListId": "-1",
            #         "clientOrderId": null,
            #         "price": "18000",
            #         "origQty": "0.0014",
            #         "executedQty": "0",
            #         "cummulativeQuoteQty": "0",
            #         "status": "NEW",
            #         "type": "LIMIT",
            #         "side": "BUY",
            #         "isIsolated": True,
            #         "isWorking": True,
            #         "time": 1662153107000,
            #         "updateTime": 1662153107000
            #     }
            #
        elif market['swap']:
            request['order_id'] = id
            response = await self.contractPrivateGetOrderGetOrderId(self.extend(request, params))
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": {
            #             "orderId": "264995729269765120",
            #             "symbol": "STEPN_USDT",
            #             "positionId": "0",
            #             "price": "2.2",
            #             "vol": "15",
            #             "leverage": "20",
            #             "side": "1",
            #             "category": "1",
            #             "orderType": "1",
            #             "dealAvgPrice": "0",
            #             "dealVol": "0",
            #             "orderMargin": "2.2528",
            #             "takerFee": "0",
            #             "makerFee": "0",
            #             "profit": "0",
            #             "feeCurrency": "USDT",
            #             "openType": "1",
            #             "state": "2",
            #             "externalOid": "_m_0e9520c256744d64b942985189026d20",
            #             "errorCode": "0",
            #             "usedMargin": "0",
            #             "createTime": "1648850305236",
            #             "updateTime": "1648850305245",
            #             "positionMode": "1"
            #         }
            #     }
            #
            data = self.safe_value(response, 'data')
        return self.parse_order(data, market)

    async def fetch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple orders made by the user

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#all-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-of-the-user-39-s-historical-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#gets-the-trigger-order-list

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: the latest time in ms to fetch orders for
        :param str [params.marginMode]: only 'isolated' is supported, for spot-margin trading
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        until = self.safe_integer(params, 'until')
        params = self.omit(params, 'until')
        marketType, query = self.handle_market_type_and_params('fetchOrders', market, params)
        if marketType == 'spot':
            if symbol is None:
                raise ArgumentsRequired(self.id + ' fetchOrders() requires a symbol argument for spot market')
            marginMode, queryInner = self.handle_margin_mode_and_params('fetchOrders', params)
            if since is not None:
                request['startTime'] = since
            if until is not None:
                request['endTime'] = until
            if limit is not None:
                request['limit'] = limit
            response = None
            if marginMode is not None:
                if marginMode != 'isolated':
                    raise BadRequest(self.id + ' fetchOrders() does not support marginMode ' + marginMode + ' for spot-margin trading')
                response = await self.spotPrivateGetMarginAllOrders(self.extend(request, queryInner))
            else:
                response = await self.spotPrivateGetAllOrders(self.extend(request, queryInner))
            #
            # spot
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "133949373632483328",
            #             "orderListId": "-1",
            #             "clientOrderId": null,
            #             "price": "45000",
            #             "origQty": "0.0002",
            #             "executedQty": "0",
            #             "cummulativeQuoteQty": "0",
            #             "status": "NEW",
            #             "timeInForce": null,
            #             "type": "LIMIT",
            #             "side": "SELL",
            #             "stopPrice": null,
            #             "icebergQty": null,
            #             "time": "1647718255000",
            #             "updateTime": "1647718255000",
            #             "isWorking": True,
            #             "origQuoteOrderQty": "9"
            #         },
            #     ]
            #
            # margin
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "763307297891028992",
            #             "orderListId": "-1",
            #             "clientOrderId": null,
            #             "price": "18000",
            #             "origQty": "0.0014",
            #             "executedQty": "0",
            #             "cummulativeQuoteQty": "0",
            #             "status": "NEW",
            #             "type": "LIMIT",
            #             "side": "BUY",
            #             "isIsolated": True,
            #             "isWorking": True,
            #             "time": 1662153107000,
            #             "updateTime": 1662153107000
            #         }
            #     ]
            #
            return self.parse_orders(response, market, since, limit)
        else:
            if since is not None:
                request['start_time'] = since
                end = self.safe_integer(params, 'end_time', until)
                if end is None:
                    request['end_time'] = self.sum(since, self.options['maxTimeTillEnd'])
                else:
                    if (end - since) > self.options['maxTimeTillEnd']:
                        raise BadRequest(self.id + ' end is invalid, i.e. exceeds allowed 90 days.')
                    else:
                        request['end_time'] = until
            elif until is not None:
                request['start_time'] = self.sum(until, self.options['maxTimeTillEnd'] * -1)
                request['end_time'] = until
            if limit is not None:
                request['page_size'] = limit
            method = self.safe_string(self.options, 'fetchOrders', 'contractPrivateGetOrderListHistoryOrders')
            method = self.safe_string(query, 'method', method)
            ordersOfRegular = []
            ordersOfTrigger = []
            if method == 'contractPrivateGetOrderListHistoryOrders':
                response = await self.contractPrivateGetOrderListHistoryOrders(self.extend(request, query))
                #
                #     {
                #         "success": True,
                #         "code": "0",
                #         "data": [
                #             {
                #                 "orderId": "265230764677709315",
                #                 "symbol": "STEPN_USDT",
                #                 "positionId": "0",
                #                 "price": "2.1",
                #                 "vol": "102",
                #                 "leverage": "20",
                #                 "side": "1",
                #                 "category": "1",
                #                 "orderType": "1",
                #                 "dealAvgPrice": "0",
                #                 "dealVol": "0",
                #                 "orderMargin": "10.96704",
                #                 "takerFee": "0",
                #                 "makerFee": "0",
                #                 "profit": "0",
                #                 "feeCurrency": "USDT",
                #                 "openType": "1",
                #                 "state": "2",
                #                 "externalOid": "_m_7e42f8df6b324c869e4e200397e2b00f",
                #                 "errorCode": "0",
                #                 "usedMargin": "0",
                #                 "createTime": "1648906342000",
                #                 "updateTime": "1648906342000",
                #                 "positionMode": "1"
                #             },
                #          ]
                #     }
                #
                ordersOfRegular = self.safe_value(response, 'data')
            else:
                # the Planorder endpoints work not only for stop-market orders, but also for stop-limit orders that were supposed to have a separate endpoint
                response = await self.contractPrivateGetPlanorderListOrders(self.extend(request, query))
                #
                #     {
                #         "success": True,
                #         "code": "0",
                #         "data": [
                #             {
                #                 "symbol": "STEPN_USDT",
                #                 "leverage": "20",
                #                 "side": "1",
                #                 "vol": "13",
                #                 "openType": "1",
                #                 "state": "1",
                #                 "orderType": "1",
                #                 "errorCode": "0",
                #                 "createTime": "1648984276000",
                #                 "updateTime": "1648984276000",
                #                 "id": "265557643326564352",
                #                 "triggerType": "1",
                #                 "triggerPrice": "3",
                #                 "price": "2.9",  # not present in stop-market, but in stop-limit order
                #                 "executeCycle": "87600",
                #                 "trend": "1",
                #             },
                #         ]
                #     }
                #
                ordersOfTrigger = self.safe_value(response, 'data')
            merged = self.array_concat(ordersOfTrigger, ordersOfRegular)
            return self.parse_orders(merged, market, since, limit, params)

    async def fetch_orders_by_ids(self, ids, symbol: Str = None, params={}):
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        marketType, query = self.handle_market_type_and_params('fetchOrdersByIds', market, params)
        if marketType == 'spot':
            raise BadRequest(self.id + ' fetchOrdersByIds() is not supported for ' + marketType)
        else:
            request['order_ids'] = ','.join(ids)
            response = await self.contractPrivateGetOrderBatchQuery(self.extend(request, query))
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": [
            #             {
            #                 "orderId": "265230764677709315",
            #                 "symbol": "STEPN_USDT",
            #                 "positionId": "0",
            #                 "price": "2.1",
            #                 "vol": "102",
            #                 "leverage": "20",
            #                 "side": "1",
            #                 "category": "1",
            #                 "orderType": "1",
            #                 "dealAvgPrice": "0",
            #                 "dealVol": "0",
            #                 "orderMargin": "10.96704",
            #                 "takerFee": "0",
            #                 "makerFee": "0",
            #                 "profit": "0",
            #                 "feeCurrency": "USDT",
            #                 "openType": "1",
            #                 "state": "2",
            #                 "externalOid": "_m_7e42f8df6b324c869e4e200397e2b00f",
            #                 "errorCode": "0",
            #                 "usedMargin": "0",
            #                 "createTime": "1648906342000",
            #                 "updateTime": "1648906342000",
            #                 "positionMode": "1"
            #             }
            #         ]
            #     }
            #
            data = self.safe_list(response, 'data')
            return self.parse_orders(data, market)

    async def fetch_open_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#current-open-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-of-the-user-39-s-historical-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#gets-the-trigger-order-list

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of  open orders structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: only 'isolated' is supported, for spot-margin trading
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        marketType = None
        if symbol is not None:
            market = self.market(symbol)
        marketType, params = self.handle_market_type_and_params('fetchOpenOrders', market, params)
        if marketType == 'spot':
            if symbol is None:
                raise ArgumentsRequired(self.id + ' fetchOpenOrders() requires a symbol argument for spot market')
            request['symbol'] = market['id']
            marginMode, query = self.handle_margin_mode_and_params('fetchOpenOrders', params)
            response = None
            if marginMode is not None:
                if marginMode != 'isolated':
                    raise BadRequest(self.id + ' fetchOpenOrders() does not support marginMode ' + marginMode + ' for spot-margin trading')
                response = await self.spotPrivateGetMarginOpenOrders(self.extend(request, query))
            else:
                response = await self.spotPrivateGetOpenOrders(self.extend(request, query))
            #
            # spot
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "133949373632483328",
            #             "orderListId": "-1",
            #             "clientOrderId": "",
            #             "price": "45000",
            #             "origQty": "0.0002",
            #             "executedQty": "0",
            #             "cummulativeQuoteQty": "0",
            #             "status": "NEW",
            #             "timeInForce": null,
            #             "type": "LIMIT",
            #             "side": "SELL",
            #             "stopPrice": null,
            #             "icebergQty": null,
            #             "time": "1647718255199",
            #             "updateTime": null,
            #             "isWorking": True,
            #             "origQuoteOrderQty": "9"
            #         }
            #     ]
            #
            # margin
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "764547676405633024",
            #             "orderListId": "-1",
            #             "clientOrderId": null,
            #             "price": "18000",
            #             "origQty": "0.0013",
            #             "executedQty": "0",
            #             "cummulativeQuoteQty": "0",
            #             "status": "NEW",
            #             "type": "LIMIT",
            #             "side": "BUY",
            #             "isIsolated": True,
            #             "isWorking": True,
            #             "time": 1662448836000,
            #             "updateTime": 1662448836000
            #         }
            #     ]
            #
            return self.parse_orders(response, market, since, limit)
        else:
            # TO_DO: another possible way is through: open_orders/{symbol}, but have same ratelimits, and less granularity, i think historical orders are more convenient, supports more params(however, theoretically, open-orders endpoint might be sligthly fast)
            return await self.fetch_orders_by_state(2, symbol, since, limit, params)

    async def fetch_closed_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple closed orders made by the user

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#all-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-of-the-user-39-s-historical-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#gets-the-trigger-order-list

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        return await self.fetch_orders_by_state(3, symbol, since, limit, params)

    async def fetch_canceled_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetches information on multiple canceled orders made by the user

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#all-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-of-the-user-39-s-historical-orders
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#gets-the-trigger-order-list

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: timestamp in ms of the earliest order, default is None
        :param int [limit]: max number of orders to return, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        return await self.fetch_orders_by_state(4, symbol, since, limit, params)

    async def fetch_orders_by_state(self, state, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
        marketType = self.handle_market_type_and_params('fetchOrdersByState', market, params)
        if marketType == 'spot':
            raise NotSupported(self.id + ' fetchOrdersByState() is not supported for ' + marketType)
        else:
            request['states'] = state
            return await self.fetch_orders(symbol, since, limit, self.extend(request, params))

    async def cancel_order(self, id: str, symbol: Str = None, params={}):
        """
        cancels an open order

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#cancel-order
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#cancel-the-order-under-maintenance
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#cancel-the-stop-limit-trigger-order-under-maintenance

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: only 'isolated' is supported for spot-margin trading
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        marketType = None
        marketType, params = self.handle_market_type_and_params('cancelOrder', market, params)
        marginMode, query = self.handle_margin_mode_and_params('cancelOrder', params)
        data = None
        if marketType == 'spot':
            if symbol is None:
                raise ArgumentsRequired(self.id + ' cancelOrder() requires a symbol argument')
            requestInner: dict = {
                'symbol': market['id'],
            }
            clientOrderId = self.safe_string(params, 'clientOrderId')
            if clientOrderId is not None:
                params = self.omit(query, 'clientOrderId')
                requestInner['origClientOrderId'] = clientOrderId
            else:
                requestInner['orderId'] = id
            if marginMode is not None:
                if marginMode != 'isolated':
                    raise BadRequest(self.id + ' cancelOrder() does not support marginMode ' + marginMode + ' for spot-margin trading')
                data = await self.spotPrivateDeleteMarginOrder(self.extend(requestInner, query))
            else:
                data = await self.spotPrivateDeleteOrder(self.extend(requestInner, query))
            #
            # spot
            #
            #     {
            #         "symbol": "BTCUSDT",
            #         "orderId": "133734823834447872",
            #         "price": "30000",
            #         "origQty": "0.0002",
            #         "type": "LIMIT",
            #         "side": "BUY"
            #     }
            #
            # margin
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "762640232574226432",
            #             "orderListId": "-1",
            #             "clientOrderId": null,
            #             "price": "18000",
            #             "origQty": "0.00147",
            #             "executedQty": "0",
            #             "cummulativeQuoteQty": "0",
            #             "status": "NEW",
            #             "type": "LIMIT",
            #             "side": "BUY",
            #             "isIsolated": True,
            #             "isWorking": True,
            #             "time": 1661994066000,
            #             "updateTime": 1661994066000
            #         }
            #     ]
            #
        else:
            # TODO: PlanorderCancel endpoint has bug atm. waiting for fix.
            method = self.safe_string(self.options, 'cancelOrder', 'contractPrivatePostOrderCancel')  # contractPrivatePostOrderCancel, contractPrivatePostPlanorderCancel
            method = self.safe_string(query, 'method', method)
            response = None
            if method == 'contractPrivatePostOrderCancel':
                response = await self.contractPrivatePostOrderCancel([id])  # the request cannot be changed or extended. This is the only way to send.
            elif method == 'contractPrivatePostPlanorderCancel':
                response = await self.contractPrivatePostPlanorderCancel([id])  # the request cannot be changed or extended. This is the only way to send.
            else:
                raise NotSupported(self.id + ' cancelOrder() not support self method')
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": [
            #             {
            #                 "orderId": "264995729269765120",
            #                 "errorCode": "0",         # if already canceled: "2041"; if doesn't exist: "2040"
            #                 "errorMsg": "success",    # if already canceled: "order state cannot be cancelled"; if doesn't exist: "order not exist"
            #             }
            #         ]
            #     }
            #
            data = self.safe_value(response, 'data')
            order = self.safe_value(data, 0)
            errorMsg = self.safe_value(order, 'errorMsg', '')
            if errorMsg != 'success':
                raise InvalidOrder(self.id + ' cancelOrder() the order with id ' + id + ' cannot be cancelled: ' + errorMsg)
        return self.parse_order(data, market)

    async def cancel_orders(self, ids, symbol: Str = None, params={}):
        """
        cancel multiple orders

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#cancel-the-order-under-maintenance

        :param str[] ids: order ids
        :param str symbol: unified market symbol, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol) if (symbol is not None) else None
        marketType = self.handle_market_type_and_params('cancelOrders', market, params)
        if marketType == 'spot':
            raise BadRequest(self.id + ' cancelOrders() is not supported for ' + marketType)
        else:
            response = await self.contractPrivatePostOrderCancel(ids)  # the request cannot be changed or extended. The only way to send.
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": [
            #             {
            #                 "orderId": "264995729269765120",
            #                 "errorCode": "0",         # if already canceled: "2041"
            #                 "errorMsg": "success",    # if already canceled: "order state cannot be cancelled"
            #             },
            #         ]
            #     }
            #
            data = self.safe_list(response, 'data')
            return self.parse_orders(data, market)

    async def cancel_all_orders(self, symbol: Str = None, params={}):
        """
        cancel all open orders

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#cancel-all-open-orders-on-a-symbol
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#cancel-all-orders-under-a-contract-under-maintenance
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#cancel-all-trigger-orders-under-maintenance

        :param str symbol: unified market symbol, only orders in the market of self symbol are cancelled when symbol is not None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: only 'isolated' is supported for spot-margin trading
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol) if (symbol is not None) else None
        request: dict = {}
        marketType = None
        marketType, params = self.handle_market_type_and_params('cancelAllOrders', market, params)
        marginMode, query = self.handle_margin_mode_and_params('cancelAllOrders', params)
        if marketType == 'spot':
            if symbol is None:
                raise ArgumentsRequired(self.id + ' cancelAllOrders() requires a symbol argument on spot')
            request['symbol'] = market['id']
            response = None
            if marginMode is not None:
                if marginMode != 'isolated':
                    raise BadRequest(self.id + ' cancelAllOrders() does not support marginMode ' + marginMode + ' for spot-margin trading')
                response = await self.spotPrivateDeleteMarginOpenOrders(self.extend(request, query))
            else:
                response = await self.spotPrivateDeleteOpenOrders(self.extend(request, query))
            #
            # spot
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "133926492139692032",
            #             "price": "30000",
            #             "origQty": "0.0002",
            #             "type": "LIMIT",
            #             "side": "BUY"
            #         },
            #     ]
            #
            # margin
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "orderId": "762640232574226432",
            #             "orderListId": "-1",
            #             "clientOrderId": null,
            #             "price": "18000",
            #             "origQty": "0.00147",
            #             "executedQty": "0",
            #             "cummulativeQuoteQty": "0",
            #             "status": "NEW",
            #             "type": "LIMIT",
            #             "side": "BUY",
            #             "isIsolated": True,
            #             "isWorking": True,
            #             "time": 1661994066000,
            #             "updateTime": 1661994066000
            #         }
            #     ]
            #
            return self.parse_orders(response, market)
        else:
            if symbol is not None:
                request['symbol'] = market['id']
            # method can be either: contractPrivatePostOrderCancelAll or contractPrivatePostPlanorderCancelAll
            # the Planorder endpoints work not only for stop-market orders but also for stop-limit orders that are supposed to have separate endpoint
            method = self.safe_string(self.options, 'cancelAllOrders', 'contractPrivatePostOrderCancelAll')
            method = self.safe_string(query, 'method', method)
            response = None
            if method == 'contractPrivatePostOrderCancelAll':
                response = await self.contractPrivatePostOrderCancelAll(self.extend(request, query))
            elif method == 'contractPrivatePostPlanorderCancelAll':
                response = await self.contractPrivatePostPlanorderCancelAll(self.extend(request, query))
            #
            #     {
            #         "success": True,
            #         "code": "0"
            #     }
            #
            data = self.safe_list(response, 'data', [])
            return self.parse_orders(data, market)

    def parse_order(self, order: dict, market: Market = None) -> Order:
        #
        # spot
        #    createOrder
        #
        #    {
        #        "symbol": "FARTCOINUSDT",
        #        "orderId": "C02__342252993005723644225",
        #        "orderListId": "-1",
        #        "price": "1.1",
        #        "origQty": "6.3",
        #        "type": "IMMEDIATE_OR_CANCEL",
        #        "side": "SELL",
        #        "transactTime": "1745852205223"
        #    }
        #
        #    unknown endpoint on spot
        #
        #    {
        #         "symbol": "BTCUSDT",
        #         "orderId": "123738410679123456",
        #         "orderListId": -1
        #    }
        #
        # margin: createOrder
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "762634301354414080",
        #         "clientOrderId": null,
        #         "isIsolated": True,
        #         "transactTime": 1661992652132
        #     }
        #
        # spot: cancelOrder, cancelAllOrders
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "133926441921286144",
        #         "price": "30000",
        #         "origQty": "0.0002",
        #         "type": "LIMIT",
        #         "side": "BUY"
        #     }
        #
        # margin: cancelOrder, cancelAllOrders
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "762640232574226432",
        #         "orderListId": "-1",
        #         "clientOrderId": null,
        #         "price": "18000",
        #         "origQty": "0.00147",
        #         "executedQty": "0",
        #         "cummulativeQuoteQty": "0",
        #         "status": "NEW",
        #         "type": "LIMIT",
        #         "side": "BUY",
        #         "isIsolated": True,
        #         "isWorking": True,
        #         "time": 1661994066000,
        #         "updateTime": 1661994066000
        #     }
        #
        # spot: fetchOrder, fetchOpenOrders, fetchOrders
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "133734823834147272",
        #         "orderListId": "-1",
        #         "clientOrderId": null,
        #         "price": "30000",
        #         "origQty": "0.0002",
        #         "executedQty": "0",
        #         "cummulativeQuoteQty": "0",
        #         "status": "CANCELED",
        #         "timeInForce": null,
        #         "type": "LIMIT",
        #         "side": "BUY",
        #         "stopPrice": null,
        #         "icebergQty": null,
        #         "time": "1647667102000",
        #         "updateTime": "1647708567000",
        #         "isWorking": True,
        #         "origQuoteOrderQty": "6"
        #     }
        #
        # margin: fetchOrder, fetchOrders
        #
        #     {
        #         "symbol": "BTCUSDT",
        #         "orderId": "763307297891028992",
        #         "orderListId": "-1",
        #         "clientOrderId": null,
        #         "price": "18000",
        #         "origQty": "0.0014",
        #         "executedQty": "0",
        #         "cummulativeQuoteQty": "0",
        #         "status": "NEW",
        #         "type": "LIMIT",
        #         "side": "BUY",
        #         "isIsolated": True,
        #         "isWorking": True,
        #         "time": 1662153107000,
        #         "updateTime": 1662153107000
        #     }
        #
        # swap: createOrder
        #
        #     2ff3163e8617443cb9c6fc19d42b1ca4
        #
        # swap: fetchOrder, fetchOrders
        #
        #     regular
        #     {
        #         "orderId": "264995729269765120",
        #         "symbol": "STEPN_USDT",
        #         "positionId": "0",
        #         "price": "2.2",
        #         "vol": "15",
        #         "leverage": "20",
        #         "side": "1",  # TODO: not unified
        #         "category": "1",
        #         "orderType": "1",  # TODO: not unified
        #         "dealAvgPrice": "0",
        #         "dealVol": "0",
        #         "orderMargin": "2.2528",
        #         "takerFee": "0",
        #         "makerFee": "0",
        #         "profit": "0",
        #         "feeCurrency": "USDT",
        #         "openType": "1",
        #         "state": "2",  # TODO
        #         "externalOid": "_m_0e9520c256744d64b942985189026d20",
        #         "errorCode": "0",
        #         "usedMargin": "0",
        #         "createTime": "1648850305236",
        #         "updateTime": "1648850305245",
        #         "positionMode": "1"
        #     }
        #
        #     stop
        #     {
        #         "id": "265557643326564352",
        #         "triggerType": "1",
        #         "triggerPrice": "3",
        #         "price": "2.9",  # not present in stop-market, but in stop-limit order
        #         "executeCycle": "87600",
        #         "trend": "1",
        #          # below keys are same regular order structure
        #         "symbol": "STEPN_USDT",
        #         "leverage": "20",
        #         "side": "1",
        #         "vol": "13",
        #         "openType": "1",
        #         "state": "1",
        #         "orderType": "1",
        #         "errorCode": "0",
        #         "createTime": "1648984276000",
        #         "updateTime": "1648984276000",
        #     }
        #
        # createOrders error
        #
        #     {
        #         "newClientOrderId": "123456",
        #         "msg": "The minimum transaction volume cannot be less than:0.5USDT",
        #         "code": 30002
        #     }
        #
        code = self.safe_integer(order, 'code')
        if code is not None:
            # error upon placing multiple orders
            return self.safe_order({
                'info': order,
                'status': 'rejected',
                'clientOrderId': self.safe_string(order, 'newClientOrderId'),
            })
        id = None
        if isinstance(order, str):
            id = order
        else:
            id = self.safe_string_2(order, 'orderId', 'id')
        timeInForce = self.parse_order_time_in_force(self.safe_string(order, 'timeInForce'))
        typeRaw = self.safe_string(order, 'type')
        if timeInForce is None:
            timeInForce = self.get_tif_from_raw_order_type(typeRaw)
        marketId = self.safe_string(order, 'symbol')
        market = self.safe_market(marketId, market)
        timestamp = self.safe_integer_n(order, ['time', 'createTime', 'transactTime'])
        fee = None
        feeCurrency = self.safe_string(order, 'feeCurrency')
        if feeCurrency is not None:
            takerFee = self.safe_string(order, 'takerFee')
            makerFee = self.safe_string(order, 'makerFee')
            feeSum = Precise.string_add(takerFee, makerFee)
            fee = {
                'currency': feeCurrency,
                'cost': self.parse_number(feeSum),
            }
        return self.safe_order({
            'id': id,
            'clientOrderId': self.safe_string(order, 'clientOrderId'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': None,  # TODO: self might be 'updateTime' if order-status is filled, otherwise cancellation time. needs to be checked
            'status': self.parse_order_status(self.safe_string_2(order, 'status', 'state')),
            'symbol': market['symbol'],
            'type': self.parse_order_type(typeRaw),
            'timeInForce': timeInForce,
            'side': self.parse_order_side(self.safe_string(order, 'side')),
            'price': self.safe_number(order, 'price'),
            'triggerPrice': self.safe_number_2(order, 'stopPrice', 'triggerPrice'),
            'average': self.safe_number(order, 'dealAvgPrice'),
            'amount': self.safe_number_2(order, 'origQty', 'vol'),
            'cost': self.safe_number(order, 'cummulativeQuoteQty'),  # 'cummulativeQuoteQty' vs 'origQuoteOrderQty'
            'filled': self.safe_number_2(order, 'executedQty', 'dealVol'),
            'remaining': None,
            'fee': fee,
            'trades': None,
            'info': order,
        }, market)

    def parse_order_side(self, status):
        statuses: dict = {
            'BUY': 'buy',
            'SELL': 'sell',
            '1': 'buy',
            '2': 'sell',
            # contracts v1 : TODO
        }
        return self.safe_string(statuses, status, status)

    def parse_order_type(self, status):
        statuses: dict = {
            'MARKET': 'market',
            'LIMIT': 'limit',
            'LIMIT_MAKER': 'limit',
            # on spot, during submission below types are used only accepted order
            'IMMEDIATE_OR_CANCEL': 'limit',
            'FILL_OR_KILL': 'limit',
        }
        return self.safe_string(statuses, status, status)

    def parse_order_status(self, status: Str):
        statuses: dict = {
            'NEW': 'open',
            'FILLED': 'closed',
            'CANCELED': 'canceled',
            'PARTIALLY_FILLED': 'open',
            'PARTIALLY_CANCELED': 'canceled',
            # contracts v1
            # '1': 'uninformed',  # TODO: wt?
            '2': 'open',
            '3': 'closed',
            '4': 'canceled',
            # '5': 'invalid',  #  TODO: wt?
        }
        return self.safe_string(statuses, status, status)

    def parse_order_time_in_force(self, status):
        statuses: dict = {
            'GTC': 'GTC',
            'FOK': 'FOK',
            'IOC': 'IOC',
        }
        return self.safe_string(statuses, status, status)

    def get_tif_from_raw_order_type(self, orderType: Str = None):
        statuses: dict = {
            'LIMIT': 'GTC',
            'LIMIT_MAKER': 'POST_ONLY',
            'IMMEDIATE_OR_CANCEL': 'IOC',
            'FILL_OR_KILL': 'FOK',
            'MARKET': 'IOC',
        }
        return self.safe_string(statuses, orderType, orderType)

    async def fetch_account_helper(self, type, params):
        if type == 'spot':
            return await self.spotPrivateGetAccount(params)
            #
            #     {
            #         "makerCommission": "20",
            #         "takerCommission": "20",
            #         "buyerCommission": "0",
            #         "sellerCommission": "0",
            #         "canTrade": True,
            #         "canWithdraw": True,
            #         "canDeposit": True,
            #         "updateTime": null,
            #         "accountType": "SPOT",
            #         "balances": [
            #             {
            #                 "asset": "BTC",
            #                 "free": "0.002",
            #                 "locked": "0"
            #             },
            #             {
            #                 "asset": "USDT",
            #                 "free": "88.120131350620957006",
            #                 "locked": "0"
            #             },
            #         ],
            #         "permissions": [
            #             "SPOT"
            #         ]
            #     }
            #
        elif type == 'swap':
            response = await self.contractPrivateGetAccountAssets(params)
            #
            #     {
            #         "success":true,
            #         "code":0,
            #         "data":[
            #            {
            #              "currency":"BSV",
            #              "positionMargin":0,
            #              "availableBalance":0,
            #              "cashBalance":0,
            #              "frozenBalance":0,
            #              "equity":0,
            #              "unrealized":0,
            #              "bonus":0
            #           },
            #         ]
            #     }
            #
            return self.safe_value(response, 'data')
        return None

    async def fetch_accounts(self, params={}) -> List[Account]:
        """
        fetch all the accounts associated with a profile

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#account-information
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-informations-of-user-39-s-asset

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `account structures <https://docs.ccxt.com/#/?id=account-structure>` indexed by the account type
        """
        # TODO: is the below endpoints suitable for fetchAccounts?
        marketType, query = self.handle_market_type_and_params('fetchAccounts', None, params)
        await self.load_markets()
        response = await self.fetch_account_helper(marketType, query)
        data = self.safe_value(response, 'balances', [])
        result = []
        for i in range(0, len(data)):
            account = data[i]
            currencyId = self.safe_string_2(account, 'asset', 'currency')
            code = self.safe_currency_code(currencyId)
            result.append({
                'id': self.safe_string(account, 'id'),
                'type': self.safe_string(account, 'type'),
                'code': code,
                'info': account,
            })
        return result

    async def fetch_trading_fee(self, symbol: str, params={}) -> TradingFeeInterface:
        """
        fetch the trading fees for a market

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#query-mx-deduct-status

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `fee structure <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        if not market['spot']:
            raise BadRequest(self.id + ' fetchTradingFee() supports spot markets only')
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.spotPrivateGetTradeFee(self.extend(request, params))
        #
        #  {
        #      "data":{
        #        "makerCommission":0.***********0000000,
        #        "takerCommission":0.***********0000000
        #      },
        #      "code":0,
        #      "msg":"success",
        #      "timestamp":1669109672717
        #  }
        #
        data = self.safe_dict(response, 'data', {})
        return {
            'info': data,
            'symbol': symbol,
            'maker': self.safe_number(data, 'makerCommission'),
            'taker': self.safe_number(data, 'takerCommission'),
            'percentage': None,
            'tierBased': None,
        }

    def custom_parse_balance(self, response, marketType) -> Balances:
        #
        # spot
        #
        #     {
        #         "asset": "USDT",
        #         "free": "0.************",
        #         "locked": "0"
        #     }
        #
        # swap
        #
        #     {
        #         "currency": "BSV",
        #         "positionMargin": 0,
        #         "availableBalance": 0,
        #         "cashBalance": 0,
        #         "frozenBalance": 0,
        #         "equity": 0,
        #         "unrealized": 0,
        #         "bonus": 0
        #     }
        #
        # margin
        #
        #     {
        #         "baseAsset": {
        #             "asset": "BTC",
        #             "borrowEnabled": True,
        #             "borrowed": "0",
        #             "free": "0",
        #             "interest": "0",
        #             "locked": "0",
        #             "netAsset": "0",
        #             "netAssetOfBtc": "0",
        #             "repayEnabled": True,
        #             "totalAsset": "0"
        #         }
        #         "quoteAsset": {
        #             "asset": "USDT",
        #             "borrowEnabled": True,
        #             "borrowed": "0",
        #             "free": "10",
        #             "interest": "0",
        #             "locked": "0",
        #             "netAsset": "10",
        #             "netAssetOfBtc": "0",
        #             "repayEnabled": True,
        #             "totalAsset": "10"
        #         }
        #         "symbol": "BTCUSDT",
        #         "isolatedCreated": True,
        #         "enabled": True,
        #         "marginLevel": "999",
        #         "marginRatio": "9",
        #         "indexPrice": "16741.13706896**********",
        #         "liquidatePrice": "--",
        #         "liquidateRate": "--",
        #         "tradeEnabled": True
        #     }
        #
        wallet = None
        if marketType == 'margin':
            wallet = self.safe_value(response, 'assets', [])
        elif marketType == 'swap':
            wallet = self.safe_value(response, 'data', [])
        else:
            wallet = self.safe_value(response, 'balances', [])
        result = {'info': response}
        if marketType == 'margin':
            for i in range(0, len(wallet)):
                entry = wallet[i]
                marketId = self.safe_string(entry, 'symbol')
                symbol = self.safe_symbol(marketId, None)
                base = self.safe_value(entry, 'baseAsset', {})
                quote = self.safe_value(entry, 'quoteAsset', {})
                baseCode = self.safe_currency_code(self.safe_string(base, 'asset'))
                quoteCode = self.safe_currency_code(self.safe_string(quote, 'asset'))
                subResult: dict = {}
                subResult[baseCode] = self.parse_balance_helper(base)
                subResult[quoteCode] = self.parse_balance_helper(quote)
                result[symbol] = self.safe_balance(subResult)
            return result
        elif marketType == 'swap':
            for i in range(0, len(wallet)):
                entry = wallet[i]
                currencyId = self.safe_string(entry, 'currency')
                code = self.safe_currency_code(currencyId)
                account = self.account()
                account['free'] = self.safe_string(entry, 'availableBalance')
                account['used'] = self.safe_string(entry, 'frozenBalance')
                result[code] = account
            return self.safe_balance(result)
        else:
            for i in range(0, len(wallet)):
                entry = wallet[i]
                currencyId = self.safe_string(entry, 'asset')
                code = self.safe_currency_code(currencyId)
                account = self.account()
                account['free'] = self.safe_string(entry, 'free')
                account['used'] = self.safe_string(entry, 'locked')
                result[code] = account
            return self.safe_balance(result)

    def parse_balance_helper(self, entry):
        account = self.account()
        account['used'] = self.safe_string(entry, 'locked')
        account['free'] = self.safe_string(entry, 'free')
        account['total'] = self.safe_string(entry, 'totalAsset')
        debt = self.safe_string(entry, 'borrowed')
        interest = self.safe_string(entry, 'interest')
        account['debt'] = Precise.string_add(debt, interest)
        return account

    async def fetch_balance(self, params={}) -> Balances:
        """
        query for balance and get the amount of funds available for trading or funds locked in orders

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#account-information
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-informations-of-user-39-s-asset
        https://mexcdevelop.github.io/apidocs/spot_v3_en/#isolated-account

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.symbols]:  # required for margin, market id's separated by commas
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        await self.load_markets()
        marketType = None
        request: dict = {}
        marketType, params = self.handle_market_type_and_params('fetchBalance', None, params)
        marginMode = self.safe_string(params, 'marginMode')
        isMargin = self.safe_bool(params, 'margin', False)
        params = self.omit(params, ['margin', 'marginMode'])
        response = None
        if (marginMode is not None) or (isMargin) or (marketType == 'margin'):
            parsedSymbols = None
            symbol = self.safe_string(params, 'symbol')
            if symbol is None:
                symbols = self.safe_value(params, 'symbols')
                if symbols is not None:
                    parsedSymbols = ','.join(self.market_ids(symbols))
            else:
                market = self.market(symbol)
                parsedSymbols = market['id']
            self.check_required_argument('fetchBalance', parsedSymbols, 'symbol or symbols')
            marketType = 'margin'
            request['symbols'] = parsedSymbols
            params = self.omit(params, ['symbol', 'symbols'])
            response = await self.spotPrivateGetMarginIsolatedAccount(self.extend(request, params))
        elif marketType == 'spot':
            response = await self.spotPrivateGetAccount(self.extend(request, params))
        elif marketType == 'swap':
            response = await self.contractPrivateGetAccountAssets(self.extend(request, params))
        else:
            raise NotSupported(self.id + ' fetchBalance() not support self method')
        #
        # spot
        #
        #     {
        #         "makerCommission": 0,
        #         "takerCommission": 20,
        #         "buyerCommission": 0,
        #         "sellerCommission": 0,
        #         "canTrade": True,
        #         "canWithdraw": True,
        #         "canDeposit": True,
        #         "updateTime": null,
        #         "accountType": "SPOT",
        #         "balances": [
        #             {
        #                 "asset": "USDT",
        #                 "free": "0.************",
        #                 "locked": "0"
        #             },
        #         ],
        #         "permissions": ["SPOT"]
        #     }
        #
        # swap
        #
        #     {
        #         "success": True,
        #         "code": 0,
        #         "data": [
        #             {
        #                 "currency": "BSV",
        #                 "positionMargin": 0,
        #                 "availableBalance": 0,
        #                 "cashBalance": 0,
        #                 "frozenBalance": 0,
        #                 "equity": 0,
        #                 "unrealized": 0,
        #                 "bonus": 0
        #             },
        #         ]
        #     }
        #
        # margin
        #
        #     {
        #         "assets": [
        #             {
        #                 "baseAsset": {
        #                     "asset": "BTC",
        #                     "borrowEnabled": True,
        #                     "borrowed": "0",
        #                     "free": "0",
        #                     "interest": "0",
        #                     "locked": "0",
        #                     "netAsset": "0",
        #                     "netAssetOfBtc": "0",
        #                     "repayEnabled": True,
        #                     "totalAsset": "0"
        #                 },
        #                 "quoteAsset": {
        #                     "asset": "USDT",
        #                     "borrowEnabled": True,
        #                     "borrowed": "0",
        #                     "free": "10",
        #                     "interest": "0",
        #                     "locked": "0",
        #                     "netAsset": "10",
        #                     "netAssetOfBtc": "0",
        #                     "repayEnabled": True,
        #                     "totalAsset": "10"
        #                 },
        #                 "symbol": "BTCUSDT",
        #                 "isolatedCreated": True,
        #                 "enabled": True,
        #                 "marginLevel": "999",
        #                 "marginRatio": "9",
        #                 "indexPrice": "16741.13706896**********",
        #                 "liquidatePrice": "--",
        #                 "liquidateRate": "--",
        #                 "tradeEnabled": True
        #             }
        #         ]
        #     }
        #
        return self.custom_parse_balance(response, marketType)

    async def fetch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all trades made by the user

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#account-trade-list
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-all-transaction-details-of-the-user-s-order

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: the latest time in ms to fetch trades for
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchMyTrades() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        marketType: Str = None
        marketType, params = self.handle_market_type_and_params('fetchMyTrades', market, params)
        request: dict = {
            'symbol': market['id'],
        }
        trades = None
        if marketType == 'spot':
            if since is not None:
                request['startTime'] = since
            if limit is not None:
                request['limit'] = limit
            until = self.safe_integer(params, 'until')
            if until is not None:
                params = self.omit(params, 'until')
                request['endTime'] = until
            trades = await self.spotPrivateGetMyTrades(self.extend(request, params))
            #
            # spot
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "id": "133948532984922113",
            #             "orderId": "133948532531949568",
            #             "orderListId": "-1",
            #             "price": "41995.51",
            #             "qty": "0.0002",
            #             "quoteQty": "8.399102",
            #             "commission": "0.016798204",
            #             "commissionAsset": "USDT",
            #             "time": "1647718055000",
            #             "isBuyer": True,
            #             "isMaker": False,
            #             "isBestMatch": True
            #         }
            #     ]
            #
        else:
            if since is not None:
                request['start_time'] = since
                end = self.safe_integer(params, 'end_time')
                if end is None:
                    request['end_time'] = self.sum(since, self.options['maxTimeTillEnd'])
            if limit is not None:
                request['page_size'] = limit
            response = await self.contractPrivateGetOrderListOrderDeals(self.extend(request, params))
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": [
            #             {
            #                 "id": "299444585",
            #                 "symbol": "STEPN_USDT",
            #                 "side": "1",
            #                 "vol": "1",
            #                 "price": "2.45455",
            #                 "feeCurrency": "USDT",
            #                 "fee": "0.00147273",
            #                 "timestamp": "1648924557000",
            #                 "profit": "0",
            #                 "category": "1",
            #                 "orderId": "265307163526610432",
            #                 "positionMode": "1",
            #                 "taker": True
            #             }
            #         ]
            #     }
            #
            trades = self.safe_value(response, 'data')
        return self.parse_trades(trades, market, since, limit)

    async def fetch_order_trades(self, id: str, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all the trades made from a single order

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#account-trade-list
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#query-the-order-based-on-the-order-number

        :param str id: order id
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
        marketType, query = self.handle_market_type_and_params('fetchOrderTrades', market, params)
        trades = None
        if marketType == 'spot':
            if symbol is None:
                raise ArgumentsRequired(self.id + ' fetchOrderTrades() requires a symbol argument')
            request['symbol'] = market['id']
            request['orderId'] = id
            trades = await self.spotPrivateGetMyTrades(self.extend(request, query))
            #
            # spot
            #
            #     [
            #         {
            #             "symbol": "BTCUSDT",
            #             "id": "133948532984922113",
            #             "orderId": "133948532531949568",
            #             "orderListId": "-1",
            #             "price": "41995.51",
            #             "qty": "0.0002",
            #             "quoteQty": "8.399102",
            #             "commission": "0.016798204",
            #             "commissionAsset": "USDT",
            #             "time": "1647718055000",
            #             "isBuyer": True,
            #             "isMaker": False,
            #             "isBestMatch": True
            #         }
            #     ]
            #
        else:
            request['order_id'] = id
            response = await self.contractPrivateGetOrderDealDetailsOrderId(self.extend(request, query))
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": [
            #             {
            #                 "id": "299444585",
            #                 "symbol": "STEPN_USDT",
            #                 "side": "1",
            #                 "vol": "1",
            #                 "price": "2.45455",
            #                 "feeCurrency": "USDT",
            #                 "fee": "0.00147273",
            #                 "timestamp": "1648924557000",
            #                 "profit": "0",
            #                 "category": "1",
            #                 "orderId": "265307163526610432",
            #                 "positionMode": "1",
            #                 "taker": True
            #             }
            #         ]
            #     }
            #
            trades = self.safe_value(response, 'data')
        return self.parse_trades(trades, market, since, limit, query)

    async def modify_margin_helper(self, symbol: str, amount, addOrReduce, params={}):
        positionId = self.safe_integer(params, 'positionId')
        if positionId is None:
            raise ArgumentsRequired(self.id + ' modifyMarginHelper() requires a positionId parameter')
        await self.load_markets()
        request: dict = {
            'positionId': positionId,
            'amount': amount,
            'type': addOrReduce,
        }
        response = await self.contractPrivatePostPositionChangeMargin(self.extend(request, params))
        #
        #     {
        #         "success": True,
        #         "code": 0
        #     }
        return response

    async def reduce_margin(self, symbol: str, amount: float, params={}) -> MarginModification:
        """
        remove margin from a position

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#increase-or-decrease-margin

        :param str symbol: unified market symbol
        :param float amount: the amount of margin to remove
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `margin structure <https://docs.ccxt.com/#/?id=reduce-margin-structure>`
        """
        return await self.modify_margin_helper(symbol, amount, 'SUB', params)

    async def add_margin(self, symbol: str, amount: float, params={}) -> MarginModification:
        """
        add margin

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#increase-or-decrease-margin

        :param str symbol: unified market symbol
        :param float amount: amount of margin to add
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `margin structure <https://docs.ccxt.com/#/?id=add-margin-structure>`
        """
        return await self.modify_margin_helper(symbol, amount, 'ADD', params)

    async def set_leverage(self, leverage: Int, symbol: Str = None, params={}):
        """
        set the level of leverage for a market

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#switch-leverage

        :param float leverage: the rate of leverage
        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: response from the exchange
        """
        await self.load_markets()
        request: dict = {
            'leverage': leverage,
        }
        positionId = self.safe_integer(params, 'positionId')
        if positionId is None:
            openType = self.safe_number(params, 'openType')  # 1 or 2
            positionType = self.safe_number(params, 'positionType')  # 1 or 2
            market = self.market(symbol) if (symbol is not None) else None
            if (openType is None) or (positionType is None) or (market is None):
                raise ArgumentsRequired(self.id + ' setLeverage() requires a positionId parameter or a symbol argument with openType and positionType parameters, use openType 1 or 2 for isolated or cross margin respectively, use positionType 1 or 2 for long or short positions')
            else:
                request['openType'] = openType
                request['symbol'] = market['id']
                request['positionType'] = positionType
        else:
            request['positionId'] = positionId
        return await self.contractPrivatePostPositionChangeLeverage(self.extend(request, params))

    async def fetch_funding_history(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch the history of funding payments paid and received on self account

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-details-of-user-s-funding-rate

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch funding history for
        :param int [limit]: the maximum number of funding history structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `funding history structure <https://docs.ccxt.com/#/?id=funding-history-structure>`
        """
        await self.load_markets()
        market = None
        request: dict = {
            # 'symbol': market['id'],
            # 'position_id': positionId,
            # 'page_num': 1,
            # 'page_size': limit,  # default 20, max 100
        }
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        if limit is not None:
            request['page_size'] = limit
        response = await self.contractPrivateGetPositionFundingRecords(self.extend(request, params))
        #
        #     {
        #         "success": True,
        #         "code": 0,
        #         "data": {
        #             "pageSize": 20,
        #             "totalCount": 2,
        #             "totalPage": 1,
        #             "currentPage": 1,
        #             "resultList": [
        #                 {
        #                     "id": 7423910,
        #                     "symbol": "BTC_USDT",
        #                     "positionType": 1,
        #                     "positionValue": 29.30024,
        #                     "funding": 0.00076180624,
        #                     "rate": -0.000026,
        #                     "settleTime": 1643299200000
        #                 },
        #                 {
        #                     "id": 7416473,
        #                     "symbol": "BTC_USDT",
        #                     "positionType": 1,
        #                     "positionValue": 28.9188,
        #                     "funding": 0.0014748588,
        #                     "rate": -0.000051,
        #                     "settleTime": 1643270400000
        #                 }
        #             ]
        #         }
        #     }
        #
        data = self.safe_value(response, 'data', {})
        resultList = self.safe_value(data, 'resultList', [])
        result = []
        for i in range(0, len(resultList)):
            entry = resultList[i]
            timestamp = self.safe_integer(entry, 'settleTime')
            result.append({
                'info': entry,
                'symbol': symbol,
                'code': None,
                'timestamp': timestamp,
                'datetime': self.iso8601(timestamp),
                'id': self.safe_number(entry, 'id'),
                'amount': self.safe_number(entry, 'funding'),
            })
        return result

    def parse_funding_rate(self, contract, market: Market = None) -> FundingRate:
        #
        #     {
        #         "symbol": "BTC_USDT",
        #         "fundingRate": 0.000014,
        #         "maxFundingRate": 0.003,
        #         "minFundingRate": -0.003,
        #         "collectCycle": 8,
        #         "nextSettleTime": 1643241600000,
        #         "timestamp": 1643240373359
        #     }
        #
        nextFundingRate = self.safe_number(contract, 'fundingRate')
        nextFundingTimestamp = self.safe_integer(contract, 'nextSettleTime')
        marketId = self.safe_string(contract, 'symbol')
        symbol = self.safe_symbol(marketId, market, None, 'contract')
        timestamp = self.safe_integer(contract, 'timestamp')
        interval = self.safe_string(contract, 'collectCycle')
        intervalString = None
        if interval is not None:
            intervalString = interval + 'h'
        return {
            'info': contract,
            'symbol': symbol,
            'markPrice': None,
            'indexPrice': None,
            'interestRate': None,
            'estimatedSettlePrice': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'fundingRate': nextFundingRate,
            'fundingTimestamp': nextFundingTimestamp,
            'fundingDatetime': self.iso8601(nextFundingTimestamp),
            'nextFundingRate': None,
            'nextFundingTimestamp': None,
            'nextFundingDatetime': None,
            'previousFundingRate': None,
            'previousFundingTimestamp': None,
            'previousFundingDatetime': None,
            'interval': intervalString,
        }

    async def fetch_funding_interval(self, symbol: str, params={}) -> FundingRate:
        """
        fetch the current funding rate interval

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-contract-funding-rate

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `funding rate structure <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        return await self.fetch_funding_rate(symbol, params)

    async def fetch_funding_rate(self, symbol: str, params={}) -> FundingRate:
        """
        fetch the current funding rate

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-contract-funding-rate

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `funding rate structure <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.contractPublicGetFundingRateSymbol(self.extend(request, params))
        #
        #     {
        #         "success": True,
        #         "code": 0,
        #         "data": {
        #             "symbol": "BTC_USDT",
        #             "fundingRate": 0.000014,
        #             "maxFundingRate": 0.003,
        #             "minFundingRate": -0.003,
        #             "collectCycle": 8,
        #             "nextSettleTime": 1643241600000,
        #             "timestamp": 1643240373359
        #         }
        #     }
        #
        result = self.safe_value(response, 'data', {})
        return self.parse_funding_rate(result, market)

    async def fetch_funding_rate_history(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetches historical funding rate prices

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-contract-funding-rate-history

        :param str symbol: unified symbol of the market to fetch the funding rate history for
        :param int [since]: not used by mexc, but filtered internally by ccxt
        :param int [limit]: mexc limit is page_size default 20, maximum is 100
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `funding rate structures <https://docs.ccxt.com/#/?id=funding-rate-history-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchFundingRateHistory() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
            # 'page_size': limit,  # optional
            # 'page_num': 1,  # optional, current page number, default is 1
        }
        if limit is not None:
            request['page_size'] = limit
        response = await self.contractPublicGetFundingRateHistory(self.extend(request, params))
        #
        #    {
        #        "success": True,
        #        "code": 0,
        #        "data": {
        #            "pageSize": 2,
        #            "totalCount": 21,
        #            "totalPage": 11,
        #            "currentPage": 1,
        #            "resultList": [
        #                {
        #                    "symbol": "BTC_USDT",
        #                    "fundingRate": 0.000266,
        #                    "settleTime": 1609804800000
        #                },
        #                {
        #                    "symbol": "BTC_USDT",
        #                    "fundingRate": 0.00029,
        #                    "settleTime": 1609776000000
        #                }
        #            ]
        #        }
        #    }
        #
        data = self.safe_value(response, 'data')
        result = self.safe_value(data, 'resultList', [])
        rates = []
        for i in range(0, len(result)):
            entry = result[i]
            marketId = self.safe_string(entry, 'symbol')
            symbolInner = self.safe_symbol(marketId)
            timestamp = self.safe_integer(entry, 'settleTime')
            rates.append({
                'info': entry,
                'symbol': symbolInner,
                'fundingRate': self.safe_number(entry, 'fundingRate'),
                'timestamp': timestamp,
                'datetime': self.iso8601(timestamp),
            })
        sorted = self.sort_by(rates, 'timestamp')
        return self.filter_by_symbol_since_limit(sorted, market['symbol'], since, limit)

    async def fetch_leverage_tiers(self, symbols: Strings = None, params={}) -> LeverageTiers:
        """
        retrieve information on the maximum leverage, and maintenance margin for trades of varying trade sizes, if a market has a leverage tier of 0, then the leverage tiers cannot be obtained for self market

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-contract-information

        :param str[] [symbols]: list of unified market symbols
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `leverage tiers structures <https://docs.ccxt.com/#/?id=leverage-tiers-structure>`, indexed by market symbols
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols, 'swap', True, True)
        response = await self.contractPublicGetDetail(params)
        #
        #     {
        #         "success":true,
        #         "code":0,
        #         "data":[
        #             {
        #                 "symbol": "BTC_USDT",
        #                 "displayName": "BTC_USDT永续",
        #                 "displayNameEn": "BTC_USDT SWAP",
        #                 "positionOpenType": 3,
        #                 "baseCoin": "BTC",
        #                 "quoteCoin": "USDT",
        #                 "settleCoin": "USDT",
        #                 "contractSize": 0.0001,
        #                 "minLeverage": 1,
        #                 "maxLeverage": 125,
        #                 "priceScale": 2,
        #                 "volScale": 0,
        #                 "amountScale": 4,
        #                 "priceUnit": 0.5,
        #                 "volUnit": 1,
        #                 "minVol": 1,
        #                 "maxVol": 1000000,
        #                 "bidLimitPriceRate": 0.1,
        #                 "askLimitPriceRate": 0.1,
        #                 "takerFeeRate": 0.0006,
        #                 "makerFeeRate": 0.0002,
        #                 "maintenanceMarginRate": 0.004,
        #                 "initialMarginRate": 0.008,
        #                 "riskBaseVol": 10000,
        #                 "riskIncrVol": 200000,
        #                 "riskIncrMmr": 0.004,
        #                 "riskIncrImr": 0.004,
        #                 "riskLevelLimit": 5,
        #                 "priceCoefficientVariation": 0.1,
        #                 "indexOrigin": ["BINANCE","GATEIO","HUOBI","MXC"],
        #                 "state": 0,  # 0 enabled, 1 delivery, 2 completed, 3 offline, 4 pause
        #                 "isNew": False,
        #                 "isHot": True,
        #                 "isHidden": False
        #             },
        #             ...
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data')
        return self.parse_leverage_tiers(data, symbols, 'symbol')

    def parse_market_leverage_tiers(self, info, market: Market = None) -> List[LeverageTier]:
        #
        #    {
        #        "symbol": "BTC_USDT",
        #        "displayName": "BTC_USDT永续",
        #        "displayNameEn": "BTC_USDT SWAP",
        #        "positionOpenType": 3,
        #        "baseCoin": "BTC",
        #        "quoteCoin": "USDT",
        #        "settleCoin": "USDT",
        #        "contractSize": 0.0001,
        #        "minLeverage": 1,
        #        "maxLeverage": 125,
        #        "priceScale": 2,
        #        "volScale": 0,
        #        "amountScale": 4,
        #        "priceUnit": 0.5,
        #        "volUnit": 1,
        #        "minVol": 1,
        #        "maxVol": 1000000,
        #        "bidLimitPriceRate": 0.1,
        #        "askLimitPriceRate": 0.1,
        #        "takerFeeRate": 0.0006,
        #        "makerFeeRate": 0.0002,
        #        "maintenanceMarginRate": 0.004,
        #        "initialMarginRate": 0.008,
        #        "riskBaseVol": 10000,
        #        "riskIncrVol": 200000,
        #        "riskIncrMmr": 0.004,
        #        "riskIncrImr": 0.004,
        #        "riskLevelLimit": 5,
        #        "priceCoefficientVariation": 0.1,
        #        "indexOrigin": ["BINANCE","GATEIO","HUOBI","MXC"],
        #        "state": 0,  # 0 enabled, 1 delivery, 2 completed, 3 offline, 4 pause
        #        "isNew": False,
        #        "isHot": True,
        #        "isHidden": False
        #    }
        #
        marketId = self.safe_string(info, 'symbol')
        maintenanceMarginRate = self.safe_string(info, 'maintenanceMarginRate')
        initialMarginRate = self.safe_string(info, 'initialMarginRate')
        maxVol = self.safe_string(info, 'maxVol')
        riskIncrVol = self.safe_string(info, 'riskIncrVol')
        riskIncrMmr = self.safe_string(info, 'riskIncrMmr')
        riskIncrImr = self.safe_string(info, 'riskIncrImr')
        floor = '0'
        tiers = []
        quoteId = self.safe_string(info, 'quoteCoin')
        if riskIncrVol == '0':
            return [
                {
                    'tier': 0,
                    'symbol': self.safe_symbol(marketId, market, None, 'contract'),
                    'currency': self.safe_currency_code(quoteId),
                    'minNotional': None,
                    'maxNotional': None,
                    'maintenanceMarginRate': None,
                    'maxLeverage': self.safe_number(info, 'maxLeverage'),
                    'info': info,
                },
            ]
        while(Precise.string_lt(floor, maxVol)):
            cap = Precise.string_add(floor, riskIncrVol)
            tiers.append({
                'tier': self.parse_number(Precise.string_div(cap, riskIncrVol)),
                'symbol': self.safe_symbol(marketId, market, None, 'contract'),
                'currency': self.safe_currency_code(quoteId),
                'minNotional': self.parse_number(floor),
                'maxNotional': self.parse_number(cap),
                'maintenanceMarginRate': self.parse_number(maintenanceMarginRate),
                'maxLeverage': self.parse_number(Precise.string_div('1', initialMarginRate)),
                'info': info,
            })
            initialMarginRate = Precise.string_add(initialMarginRate, riskIncrImr)
            maintenanceMarginRate = Precise.string_add(maintenanceMarginRate, riskIncrMmr)
            floor = cap
        return tiers

    def parse_deposit_address(self, depositAddress, currency: Currency = None) -> DepositAddress:
        #
        #    {
        #        coin: "USDT",
        #        network: "BNB Smart Chain(BEP20)",
        #        address: "0x0d48003e0c27c5de62b97c9b4cdb31fdd29da619",
        #        memo:  null
        #    }
        #
        address = self.safe_string(depositAddress, 'address')
        currencyId = self.safe_string(depositAddress, 'coin')
        networkId = self.safe_string(depositAddress, 'netWork')
        self.check_address(address)
        return {
            'info': depositAddress,
            'currency': self.safe_currency_code(currencyId, currency),
            'network': self.network_id_to_code(networkId, currencyId),
            'address': address,
            'tag': self.safe_string(depositAddress, 'memo'),
        }

    async def fetch_deposit_addresses_by_network(self, code: str, params={}) -> List[DepositAddress]:
        """
        fetch a dictionary of addresses for a currency, indexed by network

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#deposit-address-supporting-network

        :param str code: unified currency code of the currency for the deposit address
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `address structures <https://docs.ccxt.com/#/?id=address-structure>` indexed by the network
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'coin': currency['id'],
        }
        networkCode = self.safe_string(params, 'network')
        networkId = None
        if networkCode is not None:
            # createDepositAddress and fetchDepositAddress use a different network-id compared to withdraw
            networkUnified = self.network_id_to_code(networkCode, code)
            networks = self.safe_dict(currency, 'networks', {})
            if networkUnified in networks:
                network = self.safe_dict(networks, networkUnified, {})
                networkInfo = self.safe_value(network, 'info', {})
                networkId = self.safe_string(networkInfo, 'network')
            else:
                networkId = self.network_code_to_id(networkCode, code)
        if networkId is not None:
            request['network'] = networkId
        params = self.omit(params, 'network')
        response = await self.spotPrivateGetCapitalDepositAddress(self.extend(request, params))
        #
        #    [
        #        {
        #            coin: "USDT",
        #            network: "BNB Smart Chain(BEP20)",
        #            address: "0x0d48003e0c27c5de62b97c9b4cdb31fdd29da619",
        #            memo:  null
        #        }
        #        ...
        #    ]
        #
        addressStructures = self.parse_deposit_addresses(response, None, False)
        return self.index_by(addressStructures, 'network')

    async def create_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        create a currency deposit address

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#generate-deposit-address-supporting-network

        :param str code: unified currency code of the currency for the deposit address
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.network]: the blockchain network name
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'coin': currency['id'],
        }
        networkCode = self.safe_string(params, 'network')
        if networkCode is None:
            raise ArgumentsRequired(self.id + ' createDepositAddress requires a `network` parameter')
        # createDepositAddress and fetchDepositAddress use a different network-id compared to withdraw
        networkId = None
        networkUnified = self.network_id_to_code(networkCode, code)
        networks = self.safe_dict(currency, 'networks', {})
        if networkUnified in networks:
            network = self.safe_dict(networks, networkUnified, {})
            networkInfo = self.safe_value(network, 'info', {})
            networkId = self.safe_string(networkInfo, 'network')
        else:
            networkId = self.network_code_to_id(networkCode, code)
        if networkId is not None:
            request['network'] = networkId
        params = self.omit(params, 'network')
        response = await self.spotPrivatePostCapitalDepositAddress(self.extend(request, params))
        #     {
        #        "coin": "EOS",
        #        "network": "EOS",
        #        "address": "zzqqqqqqqqqq",
        #        "memo": "MX10068"
        #     }
        return self.parse_deposit_address(response, currency)

    async def fetch_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        fetch the deposit address for a currency associated with self account

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#deposit-address-supporting-network

        :param str code: unified currency code
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.network]: the chain of currency, self only apply for multi-chain currency, and there is no need for single chain currency
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        network = self.safe_string(params, 'network')
        addressStructures = await self.fetch_deposit_addresses_by_network(code, params)
        result = None
        if network is not None:
            result = self.safe_dict(addressStructures, self.network_id_to_code(network, code))
        else:
            options = self.safe_dict(self.options, 'defaultNetworks')
            defaultNetworkForCurrency = self.safe_string(options, code)
            if defaultNetworkForCurrency is not None:
                result = self.safe_dict(addressStructures, defaultNetworkForCurrency)
            else:
                keys = list(addressStructures.keys())
                key = self.safe_string(keys, 0)
                result = self.safe_dict(addressStructures, key)
        if result is None:
            raise InvalidAddress(self.id + ' fetchDepositAddress() cannot find a deposit address for ' + code + ', and network' + network + 'consider creating one using .createDepositAddress() method or in MEXC website')
        return result

    async def fetch_deposits(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all deposits made to an account

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#deposit-history-supporting-network

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch deposits for
        :param int [limit]: the maximum number of deposits structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {
            # 'coin': currency['id'] + network example: USDT-TRX,
            # 'status': 'status',
            # 'startTime': since,  # default 90 days
            # 'endTime': self.nonce(),
            # 'limit': limit,  # default 1000, maximum 1000
        }
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['coin'] = currency['id']
            # currently mexc does not have network names unified so for certain things we might need TRX or TRC-20
            # due to that I'm applying the network parameter directly so the user can control it on its side
            rawNetwork = self.safe_string(params, 'network')
            if rawNetwork is not None:
                params = self.omit(params, 'network')
                request['coin'] = request['coin'] + '-' + rawNetwork
        if since is not None:
            request['startTime'] = since
        if limit is not None:
            if limit > 1000:
                raise ExchangeError('This exchange supports a maximum limit of 1000')
            request['limit'] = limit
        response = await self.spotPrivateGetCapitalDepositHisrec(self.extend(request, params))
        #
        # [
        #     {
        #         "amount": "10",
        #         "coin": "USDC-TRX",
        #         "network": "TRX",
        #         "status": "5",
        #         "address": "TSMcEDDvkqY9dz8RkFnrS86U59GwEZjfvh",
        #         "txId": "51a8f49e6f03f2c056e71fe3291aa65e1032880be855b65cecd0595a1b8af95b",
        #         "insertTime": "*************",
        #         "unlockConfirm": "200",
        #         "confirmTimes": "203",
        #         "memo": "xxyy1122"
        #     }
        # ]
        #
        return self.parse_transactions(response, currency, since, limit)

    async def fetch_withdrawals(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all withdrawals made from an account

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#withdraw-history-supporting-network

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawals structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {
            # 'coin': currency['id'],
            # 'status': 'status',
            # 'startTime': since,  # default 90 days
            # 'endTime': self.nonce(),
            # 'limit': limit,  # default 1000, maximum 1000
        }
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['coin'] = currency['id']
        if since is not None:
            request['startTime'] = since
        if limit is not None:
            if limit > 1000:
                raise ExchangeError('This exchange supports a maximum limit of 1000')
            request['limit'] = limit
        response = await self.spotPrivateGetCapitalWithdrawHistory(self.extend(request, params))
        #
        # [
        #     {
        #       "id": "adcd1c8322154de691b815eedcd10c42",
        #       "txId": "0xc8c918cd69b2246db493ef6225a72ffdc664f15b08da3e25c6879b271d05e9d0",
        #       "coin": "USDC-MATIC",
        #       "network": "MATIC",
        #       "address": "0xeE6C7a415995312ED52c53a0f8f03e165e0A5D62",
        #       "amount": "2",
        #       "transferType": "0",
        #       "status": "7",
        #       "transactionFee": "1",
        #       "confirmNo": null,
        #       "applyTime": "1664882739000",
        #       "remark": '',
        #       "memo": null
        #     }
        # ]
        #
        return self.parse_transactions(response, currency, since, limit)

    def parse_transaction(self, transaction: dict, currency: Currency = None) -> Transaction:
        #
        # fetchDeposits
        #
        # {
        #     "amount": "10",
        #     "coin": "USDC-TRX",
        #     "network": "TRX",
        #     "status": "5",
        #     "address": "TSMcEDDvkqY9dz8RkFnrS86U59GwEZjfvh",
        #     "txId": "51a8f49e6f03f2c056e71fe3291aa65e1032880be855b65cecd0595a1b8af95b",
        #     "insertTime": "*************",
        #     "unlockConfirm": "200",
        #     "confirmTimes": "203",
        #     "memo": "xxyy1122"
        # }
        #
        # fetchWithdrawals
        #
        # {
        #     "id": "adcd1c8322154de691b815eedcd10c42",
        #     "txId": "0xc8c918cd69b2246db493ef6225a72ffdc664f15b08da3e25c6879b271d05e9d0",
        #     "coin": "USDC-MATIC",
        #     "network": "MATIC",
        #     "address": "0xeE6C7a415995312ED52c53a0f8f03e165e0A5D62",
        #     "amount": "2",
        #     "transferType": "0",
        #     "status": "7",
        #     "transactionFee": "1",
        #     "confirmNo": null,
        #     "applyTime": "1664882739000",
        #     "remark": '',
        #     "memo": null
        #   }
        #
        # withdraw
        #
        #     {
        #         "id":"25fb2831fb6d4fc7aa4094612a26c81d"
        #     }
        #
        id = self.safe_string(transaction, 'id')
        type = 'deposit' if (id is None) else 'withdrawal'
        timestamp = self.safe_integer_2(transaction, 'insertTime', 'applyTime')
        currencyId = None
        currencyWithNetwork = self.safe_string(transaction, 'coin')
        if currencyWithNetwork is not None:
            currencyId = currencyWithNetwork.split('-')[0]
        network = None
        rawNetwork = self.safe_string(transaction, 'network')
        if rawNetwork is not None:
            network = self.network_id_to_code(rawNetwork)
        code = self.safe_currency_code(currencyId, currency)
        status = self.parse_transaction_status_by_type(self.safe_string(transaction, 'status'), type)
        amountString = self.safe_string(transaction, 'amount')
        address = self.safe_string(transaction, 'address')
        txid = self.safe_string(transaction, 'txId')
        fee = None
        feeCostString = self.safe_string(transaction, 'transactionFee')
        if feeCostString is not None:
            fee = {
                'cost': self.parse_number(feeCostString),
                'currency': code,
            }
        if type == 'withdrawal':
            # mexc withdrawal amount includes the fee
            amountString = Precise.string_sub(amountString, feeCostString)
        return {
            'info': transaction,
            'id': id,
            'txid': txid,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'network': network,
            'address': address,
            'addressTo': address,
            'addressFrom': None,
            'tag': self.safe_string(transaction, 'memo'),
            'tagTo': None,
            'tagFrom': None,
            'type': type,
            'amount': self.parse_number(amountString),
            'currency': code,
            'status': status,
            'updated': None,
            'comment': None,
            'internal': None,
            'fee': fee,
        }

    def parse_transaction_status_by_type(self, status, type=None):
        statusesByType: dict = {
            'deposit': {
                '1': 'failed',  # SMALL
                '2': 'pending',  # TIME_DELAY
                '3': 'pending',  # LARGE_DELAY
                '4': 'pending',  # PENDING
                '5': 'ok',  # SUCCESS
                '6': 'pending',  # AUDITING
                '7': 'failed',  # REJECTED
            },
            'withdrawal': {
                '1': 'pending',  # APPLY
                '2': 'pending',  # AUDITING
                '3': 'pending',  # WAIT
                '4': 'pending',  # PROCESSING
                '5': 'pending',  # WAIT_PACKAGING
                '6': 'pending',  # WAIT_CONFIRM
                '7': 'ok',  # SUCCESS
                '8': 'failed',  # FAILED
                '9': 'canceled',  # CANCEL
                '10': 'pending',  # MANUAL
            },
        }
        statuses = self.safe_value(statusesByType, type, {})
        return self.safe_string(statuses, status, status)

    async def fetch_position(self, symbol: str, params={}):
        """
        fetch data on a single open contract trade position

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-user-s-history-position-information

        :param str symbol: unified market symbol of the market the position is held in, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `position structure <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.fetch_positions(None, self.extend(request, params))
        return self.safe_value(response, 0)

    async def fetch_positions(self, symbols: Strings = None, params={}) -> List[Position]:
        """
        fetch all open positions

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-user-s-history-position-information

        :param str[]|None symbols: list of unified market symbols
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `position structure <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        response = await self.contractPrivateGetPositionOpenPositions(params)
        #
        #     {
        #         "success": True,
        #         "code": 0,
        #         "data": [
        #             {
        #                 "positionId": 1394650,
        #                 "symbol": "ETH_USDT",
        #                 "positionType": 1,
        #                 "openType": 1,
        #                 "state": 1,
        #                 "holdVol": 1,
        #                 "frozenVol": 0,
        #                 "closeVol": 0,
        #                 "holdAvgPrice": 1217.3,
        #                 "openAvgPrice": 1217.3,
        #                 "closeAvgPrice": 0,
        #                 "liquidatePrice": 1211.2,
        #                 "oim": 0.1290338,
        #                 "im": 0.1290338,
        #                 "holdFee": 0,
        #                 "realised": -0.0073,
        #                 "leverage": 100,
        #                 "createTime": 1609991676000,
        #                 "updateTime": 1609991676000,
        #                 "autoAddIm": False
        #             }
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_positions(data, symbols)

    def parse_position(self, position: dict, market: Market = None):
        #
        # fetchPositions
        #
        #     {
        #         "positionId": 1394650,
        #         "symbol": "ETH_USDT",
        #         "positionType": 1,
        #         "openType": 1,
        #         "state": 1,
        #         "holdVol": 1,
        #         "frozenVol": 0,
        #         "closeVol": 0,
        #         "holdAvgPrice": 1217.3,
        #         "openAvgPrice": 1217.3,
        #         "closeAvgPrice": 0,
        #         "liquidatePrice": 1211.2,
        #         "oim": 0.1290338,
        #         "im": 0.1290338,
        #         "holdFee": 0,
        #         "realised": -0.0073,
        #         "leverage": 100,
        #         "createTime": 1609991676000,
        #         "updateTime": 1609991676000,
        #         "autoAddIm": False
        #     }
        #
        # fetchPositionsHistory
        #
        #    {
        #        positionId: '390281084',
        #        symbol: 'RVN_USDT',
        #        positionType: '1',
        #        openType: '2',
        #        state: '3',
        #        holdVol: '0',
        #        frozenVol: '0',
        #        closeVol: '1141',
        #        holdAvgPrice: '0.03491',
        #        holdAvgPriceFullyScale: '0.03491',
        #        openAvgPrice: '0.03491',
        #        openAvgPriceFullyScale: '0.03491',
        #        closeAvgPrice: '0.03494',
        #        liquidatePrice: '0.03433',
        #        oim: '0',
        #        im: '0',
        #        holdFee: '0',
        #        realised: '0.1829',
        #        leverage: '50',
        #        createTime: '1711512408000',
        #        updateTime: '1711512553000',
        #        autoAddIm: False,
        #        version: '4',
        #        profitRatio: '0.0227',
        #        newOpenAvgPrice: '0.03491',
        #        newCloseAvgPrice: '0.03494',
        #        closeProfitLoss: '0.3423',
        #        fee: '0.1593977',
        #        positionShowStatus: 'CLOSED'
        #    }
        #
        market = self.safe_market(self.safe_string(position, 'symbol'), market)
        symbol = market['symbol']
        contracts = self.safe_string(position, 'holdVol')
        entryPrice = self.safe_number(position, 'openAvgPrice')
        initialMargin = self.safe_string(position, 'im')
        rawSide = self.safe_string(position, 'positionType')
        side = 'long' if (rawSide == '1') else 'short'
        openType = self.safe_string(position, 'margin_mode')
        marginType = 'isolated' if (openType == '1') else 'cross'
        leverage = self.safe_number(position, 'leverage')
        liquidationPrice = self.safe_number(position, 'liquidatePrice')
        timestamp = self.safe_integer(position, 'updateTime')
        return self.safe_position({
            'info': position,
            'id': None,
            'symbol': symbol,
            'contracts': self.parse_number(contracts),
            'contractSize': None,
            'entryPrice': entryPrice,
            'collateral': None,
            'side': side,
            'unrealizedPnl': None,
            'leverage': self.parse_number(leverage),
            'percentage': None,
            'marginMode': marginType,
            'notional': None,
            'markPrice': None,
            'lastPrice': None,
            'liquidationPrice': liquidationPrice,
            'initialMargin': self.parse_number(initialMargin),
            'initialMarginPercentage': None,
            'maintenanceMargin': None,
            'maintenanceMarginPercentage': None,
            'marginRatio': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'hedged': None,
            'stopLossPrice': None,
            'takeProfitPrice': None,
            'lastUpdateTimestamp': None,
        })

    async def fetch_transfer(self, id: str, code: Str = None, params={}) -> TransferEntry:
        """
        fetches a transfer

        https://mexcdevelop.github.io/apidocs/spot_v2_en/#internal-assets-transfer-order-inquiry

        :param str id: transfer id
        :param str [code]: not used by mexc fetchTransfer
        :param dict params: extra parameters specific to the exchange api endpoint
        :returns dict: a `transfer structure <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        marketType, query = self.handle_market_type_and_params('fetchTransfer', None, params)
        await self.load_markets()
        if marketType == 'spot':
            request: dict = {
                'transact_id': id,
            }
            response = await self.spot2PrivateGetAssetInternalTransferInfo(self.extend(request, query))
            #
            #     {
            #         "code": "200",
            #         "data": {
            #             "currency": "USDT",
            #             "amount": "1",
            #             "transact_id": "954877a2ef54499db9b28a7cf9ebcf41",
            #             "from": "MAIN",
            #             "to": "CONTRACT",
            #             "transact_state": "SUCCESS"
            #         }
            #     }
            #
            data = self.safe_dict(response, 'data', {})
            return self.parse_transfer(data)
        elif marketType == 'swap':
            raise BadRequest(self.id + ' fetchTransfer() is not supported for ' + marketType)
        return None

    async def fetch_transfers(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[TransferEntry]:
        """
        fetch a history of internal transfers made on an account

        https://mexcdevelop.github.io/apidocs/spot_v2_en/#get-internal-assets-transfer-records
        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-user-39-s-asset-transfer-records

        :param str code: unified currency code of the currency transferred
        :param int [since]: the earliest time in ms to fetch transfers for
        :param int [limit]: the maximum number of  transfers structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transfer structures <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        marketType, query = self.handle_market_type_and_params('fetchTransfers', None, params)
        await self.load_markets()
        request: dict = {}
        currency = None
        resultList = None
        if code is not None:
            currency = self.currency(code)
            request['currency'] = currency['id']
        if marketType == 'spot':
            if since is not None:
                request['start_time'] = since
            if limit is not None:
                if limit > 50:
                    raise ExchangeError('This exchange supports a maximum limit of 50')
                request['page-size'] = limit
            response = await self.spot2PrivateGetAssetInternalTransferRecord(self.extend(request, query))
            #
            #     {
            #         "code": "200",
            #         "data": {
            #             "total_page": "1",
            #             "total_size": "5",
            #             "result_list": [{
            #                     "currency": "USDT",
            #                     "amount": "1",
            #                     "transact_id": "954877a2ef54499db9b28a7cf9ebcf41",
            #                     "from": "MAIN",
            #                     "to": "CONTRACT",
            #                     "transact_state": "SUCCESS"
            #                 },
            #                 ...
            #             ]
            #         }
            #     }
            #
            data = self.safe_value(response, 'data', {})
            resultList = self.safe_value(data, 'result_list', [])
        elif marketType == 'swap':
            if limit is not None:
                request['page_size'] = limit
            response = await self.contractPrivateGetAccountTransferRecord(self.extend(request, query))
            data = self.safe_value(response, 'data')
            resultList = self.safe_value(data, 'resultList')
            #
            #     {
            #         "success": True,
            #         "code": "0",
            #         "data": {
            #             "pageSize": "20",
            #             "totalCount": "10",
            #             "totalPage": "1",
            #             "currentPage": "1",
            #             "resultList": [
            #                 {
            #                     "id": "2980812",
            #                     "txid": "fa8a1e7bf05940a3b7025856dc48d025",
            #                     "currency": "USDT",
            #                     "amount": "22.********",
            #                     "type": "IN",
            #                     "state": "SUCCESS",
            #                     "createTime": "*************",
            #                     "updateTime": "*************"
            #                 },
            #             ]
            #         }
            #     }
            #
        return self.parse_transfers(resultList, currency, since, limit)

    async def transfer(self, code: str, amount: float, fromAccount: str, toAccount: str, params={}) -> TransferEntry:
        """
        transfer currency internally between wallets on the same account

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#user-universal-transfer

        :param str code: unified currency code
        :param float amount: amount to transfer
        :param str fromAccount: account to transfer from
        :param str toAccount: account to transfer to
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.symbol]: market symbol required for margin account transfers eg:BTCUSDT
        :returns dict: a `transfer structure <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        accounts: dict = {
            'spot': 'SPOT',
            'swap': 'FUTURES',
            'margin': 'ISOLATED_MARGIN',
        }
        fromId = self.safe_string(accounts, fromAccount)
        toId = self.safe_string(accounts, toAccount)
        if fromId is None:
            keys = list(accounts.keys())
            raise ExchangeError(self.id + ' fromAccount must be one of ' + ', '.join(keys))
        if toId is None:
            keys = list(accounts.keys())
            raise ExchangeError(self.id + ' toAccount must be one of ' + ', '.join(keys))
        request: dict = {
            'asset': currency['id'],
            'amount': amount,
            'fromAccountType': fromId,
            'toAccountType': toId,
        }
        if (fromId == 'ISOLATED_MARGIN') or (toId == 'ISOLATED_MARGIN'):
            symbol = self.safe_string(params, 'symbol')
            params = self.omit(params, 'symbol')
            if symbol is None:
                raise ArgumentsRequired(self.id + ' transfer() requires a symbol argument for isolated margin')
            market = self.market(symbol)
            request['symbol'] = market['id']
        response = await self.spotPrivatePostCapitalTransfer(self.extend(request, params))
        #
        #     {
        #         "tranId": "ebb06123e6a64f4ab234b396c548d57e"
        #     }
        #
        transaction = self.parse_transfer(response, currency)
        return self.extend(transaction, {
            'amount': amount,
            'fromAccount': fromAccount,
            'toAccount': toAccount,
        })

    def parse_transfer(self, transfer: dict, currency: Currency = None) -> TransferEntry:
        #
        # spot: fetchTransfer
        #
        #     {
        #         "currency": "USDT",
        #         "amount": "1",
        #         "transact_id": "b60c1df8e7b24b268858003f374ecb75",
        #         "from": "MAIN",
        #         "to": "CONTRACT",
        #         "transact_state": "WAIT"
        #     }
        #
        # swap: fetchTransfer
        #
        #     {
        #         "currency": "USDT",
        #         "amount": "22.********",
        #         "txid": "fa8a1e7bf05940a3b7025856dc48d025",
        #         "id": "2980812",
        #         "type": "IN",
        #         "state": "SUCCESS",
        #         "createTime": "*************",
        #         "updateTime": "*************"
        #     }
        #
        # transfer
        #
        #     {
        #         "tranId": "ebb06123e6a64f4ab234b396c548d57e"
        #     }
        #
        currencyId = self.safe_string(transfer, 'currency')
        id = self.safe_string_n(transfer, ['transact_id', 'txid', 'tranId'])
        timestamp = self.safe_integer(transfer, 'createTime')
        datetime = self.iso8601(timestamp) if (timestamp is not None) else None
        direction = self.safe_string(transfer, 'type')
        accountFrom = None
        accountTo = None
        if direction is not None:
            accountFrom = 'MAIN' if (direction == 'IN') else 'CONTRACT'
            accountTo = 'CONTRACT' if (direction == 'IN') else 'MAIN'
        else:
            accountFrom = self.safe_string(transfer, 'from')
            accountTo = self.safe_string(transfer, 'to')
        return {
            'info': transfer,
            'id': id,
            'timestamp': timestamp,
            'datetime': datetime,
            'currency': self.safe_currency_code(currencyId, currency),
            'amount': self.safe_number(transfer, 'amount'),
            'fromAccount': self.parse_account_id(accountFrom),
            'toAccount': self.parse_account_id(accountTo),
            'status': self.parse_transfer_status(self.safe_string_2(transfer, 'transact_state', 'state')),
        }

    def parse_account_id(self, status):
        statuses: dict = {
            'MAIN': 'spot',
            'CONTRACT': 'swap',
        }
        return self.safe_string(statuses, status, status)

    def parse_transfer_status(self, status: Str) -> Str:
        statuses: dict = {
            'SUCCESS': 'ok',
            'FAILED': 'failed',
            'WAIT': 'pending',
        }
        return self.safe_string(statuses, status, status)

    async def withdraw(self, code: str, amount: float, address: str, tag=None, params={}) -> Transaction:
        """
        make a withdrawal

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#withdraw-new

        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        networks = self.safe_dict(self.options, 'networks', {})
        network = self.safe_string_2(params, 'network', 'netWork')  # self line allows the user to specify either ERC20 or ETH
        network = self.safe_string(networks, network, network)  # handle ETH > ERC-20 alias
        network = self.network_code_to_id(network, currency['code'])
        self.check_address(address)
        request: dict = {
            'coin': currency['id'],
            'address': address,
            'amount': amount,
        }
        if tag is not None:
            request['memo'] = tag
        if network is not None:
            request['netWork'] = network
            params = self.omit(params, ['network', 'netWork'])
        response = await self.spotPrivatePostCapitalWithdraw(self.extend(request, params))
        #
        #     {
        #       "id":"7213fea8e94b4a5593d507237e5a555b"
        #     }
        #
        return self.parse_transaction(response, currency)

    async def set_position_mode(self, hedged: bool, symbol: Str = None, params={}):
        """
        set hedged to True or False for a market

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#change-position-mode

        :param bool hedged: set to True to use dualSidePosition
        :param str symbol: not used by mexc setPositionMode()
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: response from the exchange
        """
        request: dict = {
            'positionMode': 1 if hedged else 2,  # 1 Hedge, 2 One-way, before changing position mode make sure that there are no active orders, planned orders, or open positions, the risk limit level will be reset to 1
        }
        response = await self.contractPrivatePostPositionChangePositionMode(self.extend(request, params))
        #
        #     {
        #         "success":true,
        #         "code":0
        #     }
        #
        return response

    async def fetch_position_mode(self, symbol: Str = None, params={}):
        """
        fetchs the position mode, hedged or one way, hedged for binance is set identically for all linear markets or all inverse markets

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-position-mode

        :param str symbol: not used by mexc fetchPositionMode
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an object detailing whether the market is in hedged or one-way mode
        """
        response = await self.contractPrivateGetPositionPositionMode(params)
        #
        #     {
        #         "success":true,
        #         "code":0,
        #         "data":2
        #     }
        #
        positionMode = self.safe_integer(response, 'data')
        return {
            'info': response,
            'hedged': (positionMode == 1),
        }

    async def fetch_transaction_fees(self, codes: Strings = None, params={}):
        """
        fetch deposit and withdrawal fees

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#query-the-currency-information

        :param str[]|None codes: returns fees for all currencies if None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        await self.load_markets()
        response = await self.spotPrivateGetCapitalConfigGetall(params)
        #
        #    [
        #       {
        #           "coin": "AGLD",
        #           "name": "Adventure Gold",
        #           "networkList": [
        #               {
        #                   "coin": "AGLD",
        #                   "depositDesc": null,
        #                   "depositEnable": True,
        #                   "minConfirm": "0",
        #                   "name": "Adventure Gold",
        #                   "network": "ERC20",
        #                   "withdrawEnable": True,
        #                   "withdrawFee": "10.000000000000000000",
        #                   "withdrawIntegerMultiple": null,
        #                   "withdrawMax": "1200000.000000000000000000",
        #                   "withdrawMin": "20.000000000000000000",
        #                   "sameAddress": False,
        #                   "contract": "0x32353a6c91143bfd6c7d363b546e62a9a2489a20",
        #                   "withdrawTips": null,
        #                   "depositTips": null
        #               }
        #               ...
        #           ]
        #       },
        #       ...
        #    ]
        #
        return self.parse_transaction_fees(response, codes)

    def parse_transaction_fees(self, response, codes=None):
        withdrawFees: dict = {}
        for i in range(0, len(response)):
            entry = response[i]
            currencyId = self.safe_string(entry, 'coin')
            currency = self.safe_currency(currencyId)
            code = self.safe_string(currency, 'code')
            if (codes is None) or (self.in_array(code, codes)):
                withdrawFees[code] = self.parse_transaction_fee(entry, currency)
        return {
            'withdraw': withdrawFees,
            'deposit': {},
            'info': response,
        }

    def parse_transaction_fee(self, transaction, currency: Currency = None):
        #
        #    {
        #        "coin": "AGLD",
        #        "name": "Adventure Gold",
        #        "networkList": [
        #            {
        #                "coin": "AGLD",
        #                "depositDesc": null,
        #                "depositEnable": True,
        #                "minConfirm": "0",
        #                "name": "Adventure Gold",
        #                "network": "ERC20",
        #                "withdrawEnable": True,
        #                "withdrawFee": "10.000000000000000000",
        #                "withdrawIntegerMultiple": null,
        #                "withdrawMax": "1200000.000000000000000000",
        #                "withdrawMin": "20.000000000000000000",
        #                "sameAddress": False,
        #                "contract": "0x32353a6c91143bfd6c7d363b546e62a9a2489a20",
        #                "withdrawTips": null,
        #                "depositTips": null
        #            }
        #            ...
        #        ]
        #    }
        #
        networkList = self.safe_value(transaction, 'networkList', [])
        result: dict = {}
        for j in range(0, len(networkList)):
            networkEntry = networkList[j]
            networkId = self.safe_string(networkEntry, 'network')
            networkCode = self.safe_string(self.options['networks'], networkId, networkId)
            fee = self.safe_number(networkEntry, 'withdrawFee')
            result[networkCode] = fee
        return result

    async def fetch_deposit_withdraw_fees(self, codes: Strings = None, params={}):
        """
        fetch deposit and withdrawal fees

        https://mexcdevelop.github.io/apidocs/spot_v3_en/#query-the-currency-information

        :param str[]|None codes: returns fees for all currencies if None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        await self.load_markets()
        response = await self.spotPrivateGetCapitalConfigGetall(params)
        #
        #    [
        #       {
        #           "coin": "AGLD",
        #           "name": "Adventure Gold",
        #           "networkList": [
        #               {
        #                   "coin": "AGLD",
        #                   "depositDesc": null,
        #                   "depositEnable": True,
        #                   "minConfirm": "0",
        #                   "name": "Adventure Gold",
        #                   "network": "ERC20",
        #                   "withdrawEnable": True,
        #                   "withdrawFee": "10.000000000000000000",
        #                   "withdrawIntegerMultiple": null,
        #                   "withdrawMax": "1200000.000000000000000000",
        #                   "withdrawMin": "20.000000000000000000",
        #                   "sameAddress": False,
        #                   "contract": "0x32353a6c91143bfd6c7d363b546e62a9a2489a20",
        #                   "withdrawTips": null,
        #                   "depositTips": null
        #               }
        #               ...
        #           ]
        #       },
        #       ...
        #    ]
        #
        return self.parse_deposit_withdraw_fees(response, codes, 'coin')

    def parse_deposit_withdraw_fee(self, fee, currency: Currency = None):
        #
        #    {
        #        "coin": "AGLD",
        #        "name": "Adventure Gold",
        #        "networkList": [
        #            {
        #                "coin": "AGLD",
        #                "depositDesc": null,
        #                "depositEnable": True,
        #                "minConfirm": "0",
        #                "name": "Adventure Gold",
        #                "network": "ERC20",
        #                "withdrawEnable": True,
        #                "withdrawFee": "10.000000000000000000",
        #                "withdrawIntegerMultiple": null,
        #                "withdrawMax": "1200000.000000000000000000",
        #                "withdrawMin": "20.000000000000000000",
        #                "sameAddress": False,
        #                "contract": "0x32353a6c91143bfd6c7d363b546e62a9a2489a20",
        #                "withdrawTips": null,
        #                "depositTips": null
        #            }
        #            ...
        #        ]
        #    }
        #
        networkList = self.safe_value(fee, 'networkList', [])
        result = self.deposit_withdraw_fee(fee)
        for j in range(0, len(networkList)):
            networkEntry = networkList[j]
            networkId = self.safe_string(networkEntry, 'network')
            networkCode = self.network_id_to_code(networkId, self.safe_string(currency, 'code'))
            result['networks'][networkCode] = {
                'withdraw': {
                    'fee': self.safe_number(networkEntry, 'withdrawFee'),
                    'percentage': None,
                },
                'deposit': {
                    'fee': None,
                    'percentage': None,
                },
            }
        return self.assign_default_deposit_withdraw_fees(result)

    async def fetch_leverage(self, symbol: str, params={}) -> Leverage:
        """
        fetch the set leverage for a market

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-leverage

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `leverage structure <https://docs.ccxt.com/#/?id=leverage-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.contractPrivateGetPositionLeverage(self.extend(request, params))
        #
        #     {
        #         "success": True,
        #         "code": 0,
        #         "data": [
        #             {
        #                 "level": 1,
        #                 "maxVol": 463300,
        #                 "mmr": 0.004,
        #                 "imr": 0.005,
        #                 "positionType": 1,
        #                 "openType": 1,
        #                 "leverage": 20,
        #                 "limitBySys": False,
        #                 "currentMmr": 0.004
        #             },
        #             {
        #                 "level": 1,
        #                 "maxVol": 463300,
        #                 "mmr": 0.004,
        #                 "imr": 0.005,
        #                 "positionType": 2,
        #                 "openType": 1,
        #                 "leverage": 20,
        #                 "limitBySys": False,
        #                 "currentMmr": 0.004
        #             }
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_leverage(data, market)

    def parse_leverage(self, leverage: dict, market: Market = None) -> Leverage:
        marginMode = None
        longLeverage = None
        shortLeverage = None
        for i in range(0, len(leverage)):
            entry = leverage[i]
            openType = self.safe_integer(entry, 'openType')
            positionType = self.safe_integer(entry, 'positionType')
            if positionType == 1:
                longLeverage = self.safe_integer(entry, 'leverage')
            elif positionType == 2:
                shortLeverage = self.safe_integer(entry, 'leverage')
            marginMode = 'isolated' if (openType == 1) else 'cross'
        return {
            'info': leverage,
            'symbol': market['symbol'],
            'marginMode': marginMode,
            'longLeverage': longLeverage,
            'shortLeverage': shortLeverage,
        }

    def handle_margin_mode_and_params(self, methodName, params={}, defaultValue=None):
        """
 @ignore
        marginMode specified by params["marginMode"], self.options["marginMode"], self.options["defaultMarginMode"], params["margin"] = True or self.options["defaultType"] = 'margin'
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param bool [params.margin]: True for trading spot-margin
        :returns Array: the marginMode in lowercase
        """
        defaultType = self.safe_string(self.options, 'defaultType')
        isMargin = self.safe_bool(params, 'margin', False)
        marginMode = None
        marginMode, params = super(mexc, self).handle_margin_mode_and_params(methodName, params, defaultValue)
        if (defaultType == 'margin') or (isMargin is True):
            marginMode = 'isolated'
        return [marginMode, params]

    async def fetch_positions_history(self, symbols: Strings = None, since: Int = None, limit: Int = None, params={}) -> List[Position]:
        """
        fetches historical positions

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#get-the-user-s-history-position-information

        :param str[] [symbols]: unified contract symbols
        :param int [since]: not used by mexc fetchPositionsHistory
        :param int [limit]: the maximum amount of candles to fetch, default=1000
        :param dict [params]: extra parameters specific to the exchange api endpoint

 EXCHANGE SPECIFIC PARAMETERS
        :param int [params.type]: position type，1: long, 2: short
        :param int [params.page_num]: current page number, default is 1
        :returns dict[]: a list of `position structures <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        request: dict = {}
        if symbols is not None:
            symbolsLength = len(symbols)
            if symbolsLength == 1:
                market = self.market(symbols[0])
                request['symbol'] = market['id']
        if limit is not None:
            request['page_size'] = limit
        response = await self.contractPrivateGetPositionListHistoryPositions(self.extend(request, params))
        #
        #    {
        #        success: True,
        #        code: '0',
        #        data: [
        #            {
        #                positionId: '390281084',
        #                symbol: 'RVN_USDT',
        #                positionType: '1',
        #                openType: '2',
        #                state: '3',
        #                holdVol: '0',
        #                frozenVol: '0',
        #                closeVol: '1141',
        #                holdAvgPrice: '0.03491',
        #                holdAvgPriceFullyScale: '0.03491',
        #                openAvgPrice: '0.03491',
        #                openAvgPriceFullyScale: '0.03491',
        #                closeAvgPrice: '0.03494',
        #                liquidatePrice: '0.03433',
        #                oim: '0',
        #                im: '0',
        #                holdFee: '0',
        #                realised: '0.1829',
        #                leverage: '50',
        #                createTime: '1711512408000',
        #                updateTime: '1711512553000',
        #                autoAddIm: False,
        #                version: '4',
        #                profitRatio: '0.0227',
        #                newOpenAvgPrice: '0.03491',
        #                newCloseAvgPrice: '0.03494',
        #                closeProfitLoss: '0.3423',
        #                fee: '0.1593977',
        #                positionShowStatus: 'CLOSED'
        #            },
        #            ...
        #        ]
        #    }
        #
        data = self.safe_list(response, 'data')
        positions = self.parse_positions(data, symbols, params)
        return self.filter_by_since_limit(positions, since, limit)

    async def set_margin_mode(self, marginMode: str, symbol: Str = None, params={}):
        """
        set margin mode to 'cross' or 'isolated'

        https://mexcdevelop.github.io/apidocs/contract_v1_en/#switch-leverage

        :param str marginMode: 'cross' or 'isolated'
        :param str [symbol]: required when there is no position, else provide params["positionId"]
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.positionId]: required when a position is set
        :param str [params.direction]: "long" or "short" required when there is no position
        :returns dict: response from the exchange
        """
        await self.load_markets()
        market = self.market(symbol)
        if market['spot']:
            raise BadSymbol(self.id + ' setMarginMode() supports contract markets only')
        marginMode = marginMode.lower()
        if marginMode != 'isolated' and marginMode != 'cross':
            raise BadRequest(self.id + ' setMarginMode() marginMode argument should be isolated or cross')
        leverage = self.safe_integer(params, 'leverage')
        if leverage is None:
            raise ArgumentsRequired(self.id + ' setMarginMode() requires a leverage parameter')
        direction = self.safe_string_lower_2(params, 'direction', 'positionId')
        request: dict = {
            'leverage': leverage,
            'openType': 1 if (marginMode == 'isolated') else 2,
        }
        if symbol is not None:
            request['symbol'] = market['id']
        if direction is not None:
            request['positionType'] = 2 if (direction == 'short') else 1
        params = self.omit(params, 'direction')
        response = await self.contractPrivatePostPositionChangeLeverage(self.extend(request, params))
        #
        # {success: True, code: '0'}
        #
        return self.parse_leverage(response, market)

    def nonce(self):
        return self.milliseconds() - self.safe_integer(self.options, 'timeDifference', 0)

    def sign(self, path, api='public', method='GET', params={}, headers=None, body=None):
        section = self.safe_string(api, 0)
        access = self.safe_string(api, 1)
        path, params = self.resolve_path(path, params)
        url = None
        if section == 'spot' or section == 'broker':
            if section == 'broker':
                url = self.urls['api'][section][access] + '/' + path
            else:
                url = self.urls['api'][section][access] + '/api/' + self.version + '/' + path
            urlParams = params
            if access == 'private':
                if section == 'broker' and ((method == 'POST') or (method == 'PUT') or (method == 'DELETE')):
                    urlParams = {
                        'timestamp': self.nonce(),
                        'recvWindow': self.safe_integer(self.options, 'recvWindow', 5000),
                    }
                    body = self.json(params)
                else:
                    urlParams['timestamp'] = self.nonce()
                    urlParams['recvWindow'] = self.safe_integer(self.options, 'recvWindow', 5000)
            paramsEncoded = ''
            if urlParams:
                paramsEncoded = self.urlencode(urlParams)
                url += '?' + paramsEncoded
            if access == 'private':
                self.check_required_credentials()
                signature = self.hmac(self.encode(paramsEncoded), self.encode(self.secret), hashlib.sha256)
                url += '&' + 'signature=' + signature
                headers = {
                    'X-MEXC-APIKEY': self.apiKey,
                    'source': self.safe_string(self.options, 'broker', 'CCXT'),
                }
            if (method == 'POST') or (method == 'PUT') or (method == 'DELETE'):
                headers['Content-Type'] = 'application/json'
        elif section == 'contract' or section == 'spot2':
            url = self.urls['api'][section][access] + '/' + self.implode_params(path, params)
            params = self.omit(params, self.extract_params(path))
            if access == 'public':
                if params:
                    url += '?' + self.urlencode(params)
            else:
                self.check_required_credentials()
                timestamp = str(self.nonce())
                auth = ''
                headers = {
                    'ApiKey': self.apiKey,
                    'Request-Time': timestamp,
                    'Content-Type': 'application/json',
                    'source': self.safe_string(self.options, 'broker', 'CCXT'),
                }
                if method == 'POST':
                    auth = self.json(params)
                    body = auth
                else:
                    params = self.keysort(params)
                    if params:
                        auth += self.urlencode(params)
                        url += '?' + auth
                auth = self.apiKey + timestamp + auth
                signature = self.hmac(self.encode(auth), self.encode(self.secret), hashlib.sha256)
                headers['Signature'] = signature
        return {'url': url, 'method': method, 'body': body, 'headers': headers}

    def handle_errors(self, code: int, reason: str, url: str, method: str, headers: dict, body: str, response, requestHeaders, requestBody):
        if response is None:
            return None
        # spot
        #     {"code":-1128,"msg":"Combination of optional parameters invalid.","_extend":null}
        #     {"success":false,"code":123456,"message":"Order quantity error...."}
        #
        # contract
        #
        #     {"code":10232,"msg":"The currency not exist"}
        #     {"code":10216,"msg":"No available deposit address"}
        #     {"success":true, "code":0, "data":1634095541710}
        #
        success = self.safe_bool(response, 'success', False)  # v1
        if success is True:
            return None
        responseCode = self.safe_string(response, 'code', None)
        if (responseCode is not None) and (responseCode != '200') and (responseCode != '0'):
            feedback = self.id + ' ' + body
            self.throw_broadly_matched_exception(self.exceptions['broad'], body, feedback)
            self.throw_exactly_matched_exception(self.exceptions['exact'], responseCode, feedback)
            raise ExchangeError(feedback)
        return None
