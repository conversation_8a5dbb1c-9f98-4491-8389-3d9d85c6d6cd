from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_market_all = publicGetMarketAll = Entry('market/all', 'public', 'GET', {'cost': 2})
    public_get_candles_timeframe = publicGetCandlesTimeframe = Entry('candles/{timeframe}', 'public', 'GET', {'cost': 2})
    public_get_candles_timeframe_unit = publicGetCandlesTimeframeUnit = Entry('candles/{timeframe}/{unit}', 'public', 'GET', {'cost': 2})
    public_get_candles_seconds = publicGetCandlesSeconds = Entry('candles/seconds', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_unit = publicGetCandlesMinutesUnit = Entry('candles/minutes/{unit}', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_1 = publicGetCandlesMinutes1 = Entry('candles/minutes/1', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_3 = publicGetCandlesMinutes3 = Entry('candles/minutes/3', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_5 = publicGetCandlesMinutes5 = Entry('candles/minutes/5', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_10 = publicGetCandlesMinutes10 = Entry('candles/minutes/10', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_15 = publicGetCandlesMinutes15 = Entry('candles/minutes/15', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_30 = publicGetCandlesMinutes30 = Entry('candles/minutes/30', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_60 = publicGetCandlesMinutes60 = Entry('candles/minutes/60', 'public', 'GET', {'cost': 2})
    public_get_candles_minutes_240 = publicGetCandlesMinutes240 = Entry('candles/minutes/240', 'public', 'GET', {'cost': 2})
    public_get_candles_days = publicGetCandlesDays = Entry('candles/days', 'public', 'GET', {'cost': 2})
    public_get_candles_weeks = publicGetCandlesWeeks = Entry('candles/weeks', 'public', 'GET', {'cost': 2})
    public_get_candles_months = publicGetCandlesMonths = Entry('candles/months', 'public', 'GET', {'cost': 2})
    public_get_candles_years = publicGetCandlesYears = Entry('candles/years', 'public', 'GET', {'cost': 2})
    public_get_trades_ticks = publicGetTradesTicks = Entry('trades/ticks', 'public', 'GET', {'cost': 2})
    public_get_ticker = publicGetTicker = Entry('ticker', 'public', 'GET', {'cost': 2})
    public_get_ticker_all = publicGetTickerAll = Entry('ticker/all', 'public', 'GET', {'cost': 2})
    public_get_orderbook = publicGetOrderbook = Entry('orderbook', 'public', 'GET', {'cost': 2})
    public_get_orderbook_supported_levels = publicGetOrderbookSupportedLevels = Entry('orderbook/supported_levels', 'public', 'GET', {'cost': 2})
    private_get_accounts = privateGetAccounts = Entry('accounts', 'private', 'GET', {'cost': 0.67})
    private_get_orders_chance = privateGetOrdersChance = Entry('orders/chance', 'private', 'GET', {'cost': 0.67})
    private_get_order = privateGetOrder = Entry('order', 'private', 'GET', {'cost': 0.67})
    private_get_orders_closed = privateGetOrdersClosed = Entry('orders/closed', 'private', 'GET', {'cost': 0.67})
    private_get_orders_open = privateGetOrdersOpen = Entry('orders/open', 'private', 'GET', {'cost': 0.67})
    private_get_orders_uuids = privateGetOrdersUuids = Entry('orders/uuids', 'private', 'GET', {'cost': 0.67})
    private_get_withdraws = privateGetWithdraws = Entry('withdraws', 'private', 'GET', {'cost': 0.67})
    private_get_withdraw = privateGetWithdraw = Entry('withdraw', 'private', 'GET', {'cost': 0.67})
    private_get_withdraws_chance = privateGetWithdrawsChance = Entry('withdraws/chance', 'private', 'GET', {'cost': 0.67})
    private_get_withdraws_coin_addresses = privateGetWithdrawsCoinAddresses = Entry('withdraws/coin_addresses', 'private', 'GET', {'cost': 0.67})
    private_get_deposits = privateGetDeposits = Entry('deposits', 'private', 'GET', {'cost': 0.67})
    private_get_deposits_chance_coin = privateGetDepositsChanceCoin = Entry('deposits/chance/coin', 'private', 'GET', {'cost': 0.67})
    private_get_deposit = privateGetDeposit = Entry('deposit', 'private', 'GET', {'cost': 0.67})
    private_get_deposits_coin_addresses = privateGetDepositsCoinAddresses = Entry('deposits/coin_addresses', 'private', 'GET', {'cost': 0.67})
    private_get_deposits_coin_address = privateGetDepositsCoinAddress = Entry('deposits/coin_address', 'private', 'GET', {'cost': 0.67})
    private_get_travel_rule_vasps = privateGetTravelRuleVasps = Entry('travel_rule/vasps', 'private', 'GET', {'cost': 0.67})
    private_get_status_wallet = privateGetStatusWallet = Entry('status/wallet', 'private', 'GET', {'cost': 0.67})
    private_get_api_keys = privateGetApiKeys = Entry('api_keys', 'private', 'GET', {'cost': 0.67})
    private_post_orders = privatePostOrders = Entry('orders', 'private', 'POST', {'cost': 2.5})
    private_post_orders_cancel_and_new = privatePostOrdersCancelAndNew = Entry('orders/cancel_and_new', 'private', 'POST', {'cost': 2.5})
    private_post_withdraws_coin = privatePostWithdrawsCoin = Entry('withdraws/coin', 'private', 'POST', {'cost': 0.67})
    private_post_withdraws_krw = privatePostWithdrawsKrw = Entry('withdraws/krw', 'private', 'POST', {'cost': 0.67})
    private_post_deposits_krw = privatePostDepositsKrw = Entry('deposits/krw', 'private', 'POST', {'cost': 0.67})
    private_post_deposits_generate_coin_address = privatePostDepositsGenerateCoinAddress = Entry('deposits/generate_coin_address', 'private', 'POST', {'cost': 0.67})
    private_post_travel_rule_deposit_uuid = privatePostTravelRuleDepositUuid = Entry('travel_rule/deposit/uuid', 'private', 'POST', {'cost': 0.67})
    private_post_travel_rule_deposit_txid = privatePostTravelRuleDepositTxid = Entry('travel_rule/deposit/txid', 'private', 'POST', {'cost': 0.67})
    private_delete_order = privateDeleteOrder = Entry('order', 'private', 'DELETE', {'cost': 0.67})
    private_delete_orders_open = privateDeleteOrdersOpen = Entry('orders/open', 'private', 'DELETE', {'cost': 40})
    private_delete_orders_uuids = privateDeleteOrdersUuids = Entry('orders/uuids', 'private', 'DELETE', {'cost': 0.67})
