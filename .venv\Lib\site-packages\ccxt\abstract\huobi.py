from ccxt.base.types import Entry


class ImplicitAPI:
    v2public_get_reference_currencies = v2PublicGetReferenceCurrencies = Entry('reference/currencies', 'v2Public', 'GET', {'cost': 1})
    v2public_get_market_status = v2PublicGetMarketStatus = Entry('market-status', 'v2Public', 'GET', {'cost': 1})
    v2private_get_account_ledger = v2PrivateGetAccountLedger = Entry('account/ledger', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_withdraw_quota = v2PrivateGetAccountWithdrawQuota = Entry('account/withdraw/quota', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_withdraw_address = v2PrivateGetAccountWithdrawAddress = Entry('account/withdraw/address', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_deposit_address = v2PrivateGetAccountDepositAddress = Entry('account/deposit/address', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_repayment = v2PrivateGetAccountRepayment = Entry('account/repayment', 'v2Private', 'GET', {'cost': 5})
    v2private_get_reference_transact_fee_rate = v2PrivateGetReferenceTransactFeeRate = Entry('reference/transact-fee-rate', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_asset_valuation = v2PrivateGetAccountAssetValuation = Entry('account/asset-valuation', 'v2Private', 'GET', {'cost': 0.2})
    v2private_get_point_account = v2PrivateGetPointAccount = Entry('point/account', 'v2Private', 'GET', {'cost': 5})
    v2private_get_sub_user_user_list = v2PrivateGetSubUserUserList = Entry('sub-user/user-list', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_user_state = v2PrivateGetSubUserUserState = Entry('sub-user/user-state', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_account_list = v2PrivateGetSubUserAccountList = Entry('sub-user/account-list', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_deposit_address = v2PrivateGetSubUserDepositAddress = Entry('sub-user/deposit-address', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_query_deposit = v2PrivateGetSubUserQueryDeposit = Entry('sub-user/query-deposit', 'v2Private', 'GET', {'cost': 1})
    v2private_get_user_api_key = v2PrivateGetUserApiKey = Entry('user/api-key', 'v2Private', 'GET', {'cost': 1})
    v2private_get_user_uid = v2PrivateGetUserUid = Entry('user/uid', 'v2Private', 'GET', {'cost': 1})
    v2private_get_algo_orders_opening = v2PrivateGetAlgoOrdersOpening = Entry('algo-orders/opening', 'v2Private', 'GET', {'cost': 1})
    v2private_get_algo_orders_history = v2PrivateGetAlgoOrdersHistory = Entry('algo-orders/history', 'v2Private', 'GET', {'cost': 1})
    v2private_get_algo_orders_specific = v2PrivateGetAlgoOrdersSpecific = Entry('algo-orders/specific', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_offers = v2PrivateGetC2cOffers = Entry('c2c/offers', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_offer = v2PrivateGetC2cOffer = Entry('c2c/offer', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_transactions = v2PrivateGetC2cTransactions = Entry('c2c/transactions', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_repayment = v2PrivateGetC2cRepayment = Entry('c2c/repayment', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_account = v2PrivateGetC2cAccount = Entry('c2c/account', 'v2Private', 'GET', {'cost': 1})
    v2private_get_etp_reference = v2PrivateGetEtpReference = Entry('etp/reference', 'v2Private', 'GET', {'cost': 1})
    v2private_get_etp_transactions = v2PrivateGetEtpTransactions = Entry('etp/transactions', 'v2Private', 'GET', {'cost': 5})
    v2private_get_etp_transaction = v2PrivateGetEtpTransaction = Entry('etp/transaction', 'v2Private', 'GET', {'cost': 5})
    v2private_get_etp_rebalance = v2PrivateGetEtpRebalance = Entry('etp/rebalance', 'v2Private', 'GET', {'cost': 1})
    v2private_get_etp_limit = v2PrivateGetEtpLimit = Entry('etp/limit', 'v2Private', 'GET', {'cost': 1})
    v2private_post_account_transfer = v2PrivatePostAccountTransfer = Entry('account/transfer', 'v2Private', 'POST', {'cost': 1})
    v2private_post_account_repayment = v2PrivatePostAccountRepayment = Entry('account/repayment', 'v2Private', 'POST', {'cost': 5})
    v2private_post_point_transfer = v2PrivatePostPointTransfer = Entry('point/transfer', 'v2Private', 'POST', {'cost': 5})
    v2private_post_sub_user_management = v2PrivatePostSubUserManagement = Entry('sub-user/management', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_creation = v2PrivatePostSubUserCreation = Entry('sub-user/creation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_tradable_market = v2PrivatePostSubUserTradableMarket = Entry('sub-user/tradable-market', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_transferability = v2PrivatePostSubUserTransferability = Entry('sub-user/transferability', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_api_key_generation = v2PrivatePostSubUserApiKeyGeneration = Entry('sub-user/api-key-generation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_api_key_modification = v2PrivatePostSubUserApiKeyModification = Entry('sub-user/api-key-modification', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_api_key_deletion = v2PrivatePostSubUserApiKeyDeletion = Entry('sub-user/api-key-deletion', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_deduct_mode = v2PrivatePostSubUserDeductMode = Entry('sub-user/deduct-mode', 'v2Private', 'POST', {'cost': 1})
    v2private_post_algo_orders = v2PrivatePostAlgoOrders = Entry('algo-orders', 'v2Private', 'POST', {'cost': 1})
    v2private_post_algo_orders_cancel_all_after = v2PrivatePostAlgoOrdersCancelAllAfter = Entry('algo-orders/cancel-all-after', 'v2Private', 'POST', {'cost': 1})
    v2private_post_algo_orders_cancellation = v2PrivatePostAlgoOrdersCancellation = Entry('algo-orders/cancellation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_offer = v2PrivatePostC2cOffer = Entry('c2c/offer', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_cancellation = v2PrivatePostC2cCancellation = Entry('c2c/cancellation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_cancel_all = v2PrivatePostC2cCancelAll = Entry('c2c/cancel-all', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_repayment = v2PrivatePostC2cRepayment = Entry('c2c/repayment', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_transfer = v2PrivatePostC2cTransfer = Entry('c2c/transfer', 'v2Private', 'POST', {'cost': 1})
    v2private_post_etp_creation = v2PrivatePostEtpCreation = Entry('etp/creation', 'v2Private', 'POST', {'cost': 5})
    v2private_post_etp_redemption = v2PrivatePostEtpRedemption = Entry('etp/redemption', 'v2Private', 'POST', {'cost': 5})
    v2private_post_etp_transactid_cancel = v2PrivatePostEtpTransactIdCancel = Entry('etp/{transactId}/cancel', 'v2Private', 'POST', {'cost': 10})
    v2private_post_etp_batch_cancel = v2PrivatePostEtpBatchCancel = Entry('etp/batch-cancel', 'v2Private', 'POST', {'cost': 50})
    public_get_common_symbols = publicGetCommonSymbols = Entry('common/symbols', 'public', 'GET', {'cost': 1})
    public_get_common_currencys = publicGetCommonCurrencys = Entry('common/currencys', 'public', 'GET', {'cost': 1})
    public_get_common_timestamp = publicGetCommonTimestamp = Entry('common/timestamp', 'public', 'GET', {'cost': 1})
    public_get_common_exchange = publicGetCommonExchange = Entry('common/exchange', 'public', 'GET', {'cost': 1})
    public_get_settings_currencys = publicGetSettingsCurrencys = Entry('settings/currencys', 'public', 'GET', {'cost': 1})
    private_get_account_accounts = privateGetAccountAccounts = Entry('account/accounts', 'private', 'GET', {'cost': 0.2})
    private_get_account_accounts_id_balance = privateGetAccountAccountsIdBalance = Entry('account/accounts/{id}/balance', 'private', 'GET', {'cost': 0.2})
    private_get_account_accounts_sub_uid = privateGetAccountAccountsSubUid = Entry('account/accounts/{sub-uid}', 'private', 'GET', {'cost': 1})
    private_get_account_history = privateGetAccountHistory = Entry('account/history', 'private', 'GET', {'cost': 4})
    private_get_cross_margin_loan_info = privateGetCrossMarginLoanInfo = Entry('cross-margin/loan-info', 'private', 'GET', {'cost': 1})
    private_get_margin_loan_info = privateGetMarginLoanInfo = Entry('margin/loan-info', 'private', 'GET', {'cost': 1})
    private_get_fee_fee_rate_get = privateGetFeeFeeRateGet = Entry('fee/fee-rate/get', 'private', 'GET', {'cost': 1})
    private_get_order_openorders = privateGetOrderOpenOrders = Entry('order/openOrders', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders = privateGetOrderOrders = Entry('order/orders', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders_id = privateGetOrderOrdersId = Entry('order/orders/{id}', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders_id_matchresults = privateGetOrderOrdersIdMatchresults = Entry('order/orders/{id}/matchresults', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders_getclientorder = privateGetOrderOrdersGetClientOrder = Entry('order/orders/getClientOrder', 'private', 'GET', {'cost': 0.4})
    private_get_order_history = privateGetOrderHistory = Entry('order/history', 'private', 'GET', {'cost': 1})
    private_get_order_matchresults = privateGetOrderMatchresults = Entry('order/matchresults', 'private', 'GET', {'cost': 1})
    private_get_query_deposit_withdraw = privateGetQueryDepositWithdraw = Entry('query/deposit-withdraw', 'private', 'GET', {'cost': 1})
    private_get_margin_loan_orders = privateGetMarginLoanOrders = Entry('margin/loan-orders', 'private', 'GET', {'cost': 0.2})
    private_get_margin_accounts_balance = privateGetMarginAccountsBalance = Entry('margin/accounts/balance', 'private', 'GET', {'cost': 0.2})
    private_get_cross_margin_loan_orders = privateGetCrossMarginLoanOrders = Entry('cross-margin/loan-orders', 'private', 'GET', {'cost': 1})
    private_get_cross_margin_accounts_balance = privateGetCrossMarginAccountsBalance = Entry('cross-margin/accounts/balance', 'private', 'GET', {'cost': 1})
    private_get_points_actions = privateGetPointsActions = Entry('points/actions', 'private', 'GET', {'cost': 1})
    private_get_points_orders = privateGetPointsOrders = Entry('points/orders', 'private', 'GET', {'cost': 1})
    private_get_subuser_aggregate_balance = privateGetSubuserAggregateBalance = Entry('subuser/aggregate-balance', 'private', 'GET', {'cost': 10})
    private_get_stable_coin_exchange_rate = privateGetStableCoinExchangeRate = Entry('stable-coin/exchange_rate', 'private', 'GET', {'cost': 1})
    private_get_stable_coin_quote = privateGetStableCoinQuote = Entry('stable-coin/quote', 'private', 'GET', {'cost': 1})
    private_post_account_transfer = privatePostAccountTransfer = Entry('account/transfer', 'private', 'POST', {'cost': 1})
    private_post_futures_transfer = privatePostFuturesTransfer = Entry('futures/transfer', 'private', 'POST', {'cost': 1})
    private_post_order_batch_orders = privatePostOrderBatchOrders = Entry('order/batch-orders', 'private', 'POST', {'cost': 0.4})
    private_post_order_orders_place = privatePostOrderOrdersPlace = Entry('order/orders/place', 'private', 'POST', {'cost': 0.2})
    private_post_order_orders_submitcancelclientorder = privatePostOrderOrdersSubmitCancelClientOrder = Entry('order/orders/submitCancelClientOrder', 'private', 'POST', {'cost': 0.2})
    private_post_order_orders_batchcancelopenorders = privatePostOrderOrdersBatchCancelOpenOrders = Entry('order/orders/batchCancelOpenOrders', 'private', 'POST', {'cost': 0.4})
    private_post_order_orders_id_submitcancel = privatePostOrderOrdersIdSubmitcancel = Entry('order/orders/{id}/submitcancel', 'private', 'POST', {'cost': 0.2})
    private_post_order_orders_batchcancel = privatePostOrderOrdersBatchcancel = Entry('order/orders/batchcancel', 'private', 'POST', {'cost': 0.4})
    private_post_dw_withdraw_api_create = privatePostDwWithdrawApiCreate = Entry('dw/withdraw/api/create', 'private', 'POST', {'cost': 1})
    private_post_dw_withdraw_virtual_id_cancel = privatePostDwWithdrawVirtualIdCancel = Entry('dw/withdraw-virtual/{id}/cancel', 'private', 'POST', {'cost': 1})
    private_post_dw_transfer_in_margin = privatePostDwTransferInMargin = Entry('dw/transfer-in/margin', 'private', 'POST', {'cost': 10})
    private_post_dw_transfer_out_margin = privatePostDwTransferOutMargin = Entry('dw/transfer-out/margin', 'private', 'POST', {'cost': 10})
    private_post_margin_orders = privatePostMarginOrders = Entry('margin/orders', 'private', 'POST', {'cost': 10})
    private_post_margin_orders_id_repay = privatePostMarginOrdersIdRepay = Entry('margin/orders/{id}/repay', 'private', 'POST', {'cost': 10})
    private_post_cross_margin_transfer_in = privatePostCrossMarginTransferIn = Entry('cross-margin/transfer-in', 'private', 'POST', {'cost': 1})
    private_post_cross_margin_transfer_out = privatePostCrossMarginTransferOut = Entry('cross-margin/transfer-out', 'private', 'POST', {'cost': 1})
    private_post_cross_margin_orders = privatePostCrossMarginOrders = Entry('cross-margin/orders', 'private', 'POST', {'cost': 1})
    private_post_cross_margin_orders_id_repay = privatePostCrossMarginOrdersIdRepay = Entry('cross-margin/orders/{id}/repay', 'private', 'POST', {'cost': 1})
    private_post_stable_coin_exchange = privatePostStableCoinExchange = Entry('stable-coin/exchange', 'private', 'POST', {'cost': 1})
    private_post_subuser_transfer = privatePostSubuserTransfer = Entry('subuser/transfer', 'private', 'POST', {'cost': 10})
    status_public_spot_get_api_v2_summary_json = statusPublicSpotGetApiV2SummaryJson = Entry('api/v2/summary.json', ['status', 'public', 'spot'], 'GET', {'cost': 1})
    status_public_future_inverse_get_api_v2_summary_json = statusPublicFutureInverseGetApiV2SummaryJson = Entry('api/v2/summary.json', ['status', 'public', 'future', 'inverse'], 'GET', {'cost': 1})
    status_public_future_linear_get_api_v2_summary_json = statusPublicFutureLinearGetApiV2SummaryJson = Entry('api/v2/summary.json', ['status', 'public', 'future', 'linear'], 'GET', {'cost': 1})
    status_public_swap_inverse_get_api_v2_summary_json = statusPublicSwapInverseGetApiV2SummaryJson = Entry('api/v2/summary.json', ['status', 'public', 'swap', 'inverse'], 'GET', {'cost': 1})
    status_public_swap_linear_get_api_v2_summary_json = statusPublicSwapLinearGetApiV2SummaryJson = Entry('api/v2/summary.json', ['status', 'public', 'swap', 'linear'], 'GET', {'cost': 1})
    spot_public_get_v2_market_status = spotPublicGetV2MarketStatus = Entry('v2/market-status', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_common_symbols = spotPublicGetV1CommonSymbols = Entry('v1/common/symbols', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_common_currencys = spotPublicGetV1CommonCurrencys = Entry('v1/common/currencys', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v2_settings_common_currencies = spotPublicGetV2SettingsCommonCurrencies = Entry('v2/settings/common/currencies', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v2_reference_currencies = spotPublicGetV2ReferenceCurrencies = Entry('v2/reference/currencies', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_common_timestamp = spotPublicGetV1CommonTimestamp = Entry('v1/common/timestamp', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_common_exchange = spotPublicGetV1CommonExchange = Entry('v1/common/exchange', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_settings_common_chains = spotPublicGetV1SettingsCommonChains = Entry('v1/settings/common/chains', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_settings_common_currencys = spotPublicGetV1SettingsCommonCurrencys = Entry('v1/settings/common/currencys', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_settings_common_symbols = spotPublicGetV1SettingsCommonSymbols = Entry('v1/settings/common/symbols', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v2_settings_common_symbols = spotPublicGetV2SettingsCommonSymbols = Entry('v2/settings/common/symbols', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v1_settings_common_market_symbols = spotPublicGetV1SettingsCommonMarketSymbols = Entry('v1/settings/common/market-symbols', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_history_candles = spotPublicGetMarketHistoryCandles = Entry('market/history/candles', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_history_kline = spotPublicGetMarketHistoryKline = Entry('market/history/kline', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_detail_merged = spotPublicGetMarketDetailMerged = Entry('market/detail/merged', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_tickers = spotPublicGetMarketTickers = Entry('market/tickers', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_detail = spotPublicGetMarketDetail = Entry('market/detail', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_depth = spotPublicGetMarketDepth = Entry('market/depth', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_trade = spotPublicGetMarketTrade = Entry('market/trade', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_history_trade = spotPublicGetMarketHistoryTrade = Entry('market/history/trade', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_market_etp = spotPublicGetMarketEtp = Entry('market/etp', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v2_etp_reference = spotPublicGetV2EtpReference = Entry('v2/etp/reference', ['spot', 'public'], 'GET', {'cost': 1})
    spot_public_get_v2_etp_rebalance = spotPublicGetV2EtpRebalance = Entry('v2/etp/rebalance', ['spot', 'public'], 'GET', {'cost': 1})
    spot_private_get_v1_account_accounts = spotPrivateGetV1AccountAccounts = Entry('v1/account/accounts', ['spot', 'private'], 'GET', {'cost': 0.2})
    spot_private_get_v1_account_accounts_account_id_balance = spotPrivateGetV1AccountAccountsAccountIdBalance = Entry('v1/account/accounts/{account-id}/balance', ['spot', 'private'], 'GET', {'cost': 0.2})
    spot_private_get_v2_account_valuation = spotPrivateGetV2AccountValuation = Entry('v2/account/valuation', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_account_asset_valuation = spotPrivateGetV2AccountAssetValuation = Entry('v2/account/asset-valuation', ['spot', 'private'], 'GET', {'cost': 0.2})
    spot_private_get_v1_account_history = spotPrivateGetV1AccountHistory = Entry('v1/account/history', ['spot', 'private'], 'GET', {'cost': 4})
    spot_private_get_v2_account_ledger = spotPrivateGetV2AccountLedger = Entry('v2/account/ledger', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_point_account = spotPrivateGetV2PointAccount = Entry('v2/point/account', ['spot', 'private'], 'GET', {'cost': 5})
    spot_private_get_v2_account_deposit_address = spotPrivateGetV2AccountDepositAddress = Entry('v2/account/deposit/address', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_account_withdraw_quota = spotPrivateGetV2AccountWithdrawQuota = Entry('v2/account/withdraw/quota', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_account_withdraw_address = spotPrivateGetV2AccountWithdrawAddress = Entry('v2/account/withdraw/address', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_reference_currencies = spotPrivateGetV2ReferenceCurrencies = Entry('v2/reference/currencies', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_query_deposit_withdraw = spotPrivateGetV1QueryDepositWithdraw = Entry('v1/query/deposit-withdraw', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_query_withdraw_client_order_id = spotPrivateGetV1QueryWithdrawClientOrderId = Entry('v1/query/withdraw/client-order-id', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_user_api_key = spotPrivateGetV2UserApiKey = Entry('v2/user/api-key', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_user_uid = spotPrivateGetV2UserUid = Entry('v2/user/uid', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_sub_user_user_list = spotPrivateGetV2SubUserUserList = Entry('v2/sub-user/user-list', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_sub_user_user_state = spotPrivateGetV2SubUserUserState = Entry('v2/sub-user/user-state', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_sub_user_account_list = spotPrivateGetV2SubUserAccountList = Entry('v2/sub-user/account-list', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_sub_user_deposit_address = spotPrivateGetV2SubUserDepositAddress = Entry('v2/sub-user/deposit-address', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_sub_user_query_deposit = spotPrivateGetV2SubUserQueryDeposit = Entry('v2/sub-user/query-deposit', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_subuser_aggregate_balance = spotPrivateGetV1SubuserAggregateBalance = Entry('v1/subuser/aggregate-balance', ['spot', 'private'], 'GET', {'cost': 10})
    spot_private_get_v1_account_accounts_sub_uid = spotPrivateGetV1AccountAccountsSubUid = Entry('v1/account/accounts/{sub-uid}', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_order_openorders = spotPrivateGetV1OrderOpenOrders = Entry('v1/order/openOrders', ['spot', 'private'], 'GET', {'cost': 0.4})
    spot_private_get_v1_order_orders_order_id = spotPrivateGetV1OrderOrdersOrderId = Entry('v1/order/orders/{order-id}', ['spot', 'private'], 'GET', {'cost': 0.4})
    spot_private_get_v1_order_orders_getclientorder = spotPrivateGetV1OrderOrdersGetClientOrder = Entry('v1/order/orders/getClientOrder', ['spot', 'private'], 'GET', {'cost': 0.4})
    spot_private_get_v1_order_orders_order_id_matchresult = spotPrivateGetV1OrderOrdersOrderIdMatchresult = Entry('v1/order/orders/{order-id}/matchresult', ['spot', 'private'], 'GET', {'cost': 0.4})
    spot_private_get_v1_order_orders_order_id_matchresults = spotPrivateGetV1OrderOrdersOrderIdMatchresults = Entry('v1/order/orders/{order-id}/matchresults', ['spot', 'private'], 'GET', {'cost': 0.4})
    spot_private_get_v1_order_orders = spotPrivateGetV1OrderOrders = Entry('v1/order/orders', ['spot', 'private'], 'GET', {'cost': 0.4})
    spot_private_get_v1_order_history = spotPrivateGetV1OrderHistory = Entry('v1/order/history', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_order_matchresults = spotPrivateGetV1OrderMatchresults = Entry('v1/order/matchresults', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_reference_transact_fee_rate = spotPrivateGetV2ReferenceTransactFeeRate = Entry('v2/reference/transact-fee-rate', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_algo_orders_opening = spotPrivateGetV2AlgoOrdersOpening = Entry('v2/algo-orders/opening', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_algo_orders_history = spotPrivateGetV2AlgoOrdersHistory = Entry('v2/algo-orders/history', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_algo_orders_specific = spotPrivateGetV2AlgoOrdersSpecific = Entry('v2/algo-orders/specific', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_margin_loan_info = spotPrivateGetV1MarginLoanInfo = Entry('v1/margin/loan-info', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_margin_loan_orders = spotPrivateGetV1MarginLoanOrders = Entry('v1/margin/loan-orders', ['spot', 'private'], 'GET', {'cost': 0.2})
    spot_private_get_v1_margin_accounts_balance = spotPrivateGetV1MarginAccountsBalance = Entry('v1/margin/accounts/balance', ['spot', 'private'], 'GET', {'cost': 0.2})
    spot_private_get_v1_cross_margin_loan_info = spotPrivateGetV1CrossMarginLoanInfo = Entry('v1/cross-margin/loan-info', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_cross_margin_loan_orders = spotPrivateGetV1CrossMarginLoanOrders = Entry('v1/cross-margin/loan-orders', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_cross_margin_accounts_balance = spotPrivateGetV1CrossMarginAccountsBalance = Entry('v1/cross-margin/accounts/balance', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_account_repayment = spotPrivateGetV2AccountRepayment = Entry('v2/account/repayment', ['spot', 'private'], 'GET', {'cost': 5})
    spot_private_get_v1_stable_coin_quote = spotPrivateGetV1StableCoinQuote = Entry('v1/stable-coin/quote', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v1_stable_coin_exchange_rate = spotPrivateGetV1StableCoinExchangeRate = Entry('v1/stable_coin/exchange_rate', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_get_v2_etp_transactions = spotPrivateGetV2EtpTransactions = Entry('v2/etp/transactions', ['spot', 'private'], 'GET', {'cost': 5})
    spot_private_get_v2_etp_transaction = spotPrivateGetV2EtpTransaction = Entry('v2/etp/transaction', ['spot', 'private'], 'GET', {'cost': 5})
    spot_private_get_v2_etp_limit = spotPrivateGetV2EtpLimit = Entry('v2/etp/limit', ['spot', 'private'], 'GET', {'cost': 1})
    spot_private_post_v1_account_transfer = spotPrivatePostV1AccountTransfer = Entry('v1/account/transfer', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_futures_transfer = spotPrivatePostV1FuturesTransfer = Entry('v1/futures/transfer', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_point_transfer = spotPrivatePostV2PointTransfer = Entry('v2/point/transfer', ['spot', 'private'], 'POST', {'cost': 5})
    spot_private_post_v2_account_transfer = spotPrivatePostV2AccountTransfer = Entry('v2/account/transfer', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_dw_withdraw_api_create = spotPrivatePostV1DwWithdrawApiCreate = Entry('v1/dw/withdraw/api/create', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_dw_withdraw_virtual_withdraw_id_cancel = spotPrivatePostV1DwWithdrawVirtualWithdrawIdCancel = Entry('v1/dw/withdraw-virtual/{withdraw-id}/cancel', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_deduct_mode = spotPrivatePostV2SubUserDeductMode = Entry('v2/sub-user/deduct-mode', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_creation = spotPrivatePostV2SubUserCreation = Entry('v2/sub-user/creation', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_management = spotPrivatePostV2SubUserManagement = Entry('v2/sub-user/management', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_tradable_market = spotPrivatePostV2SubUserTradableMarket = Entry('v2/sub-user/tradable-market', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_transferability = spotPrivatePostV2SubUserTransferability = Entry('v2/sub-user/transferability', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_api_key_generation = spotPrivatePostV2SubUserApiKeyGeneration = Entry('v2/sub-user/api-key-generation', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_api_key_modification = spotPrivatePostV2SubUserApiKeyModification = Entry('v2/sub-user/api-key-modification', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_sub_user_api_key_deletion = spotPrivatePostV2SubUserApiKeyDeletion = Entry('v2/sub-user/api-key-deletion', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_subuser_transfer = spotPrivatePostV1SubuserTransfer = Entry('v1/subuser/transfer', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v1_trust_user_active_credit = spotPrivatePostV1TrustUserActiveCredit = Entry('v1/trust/user/active/credit', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v1_order_orders_place = spotPrivatePostV1OrderOrdersPlace = Entry('v1/order/orders/place', ['spot', 'private'], 'POST', {'cost': 0.2})
    spot_private_post_v1_order_batch_orders = spotPrivatePostV1OrderBatchOrders = Entry('v1/order/batch-orders', ['spot', 'private'], 'POST', {'cost': 0.4})
    spot_private_post_v1_order_auto_place = spotPrivatePostV1OrderAutoPlace = Entry('v1/order/auto/place', ['spot', 'private'], 'POST', {'cost': 0.2})
    spot_private_post_v1_order_orders_order_id_submitcancel = spotPrivatePostV1OrderOrdersOrderIdSubmitcancel = Entry('v1/order/orders/{order-id}/submitcancel', ['spot', 'private'], 'POST', {'cost': 0.2})
    spot_private_post_v1_order_orders_submitcancelclientorder = spotPrivatePostV1OrderOrdersSubmitCancelClientOrder = Entry('v1/order/orders/submitCancelClientOrder', ['spot', 'private'], 'POST', {'cost': 0.2})
    spot_private_post_v1_order_orders_batchcancelopenorders = spotPrivatePostV1OrderOrdersBatchCancelOpenOrders = Entry('v1/order/orders/batchCancelOpenOrders', ['spot', 'private'], 'POST', {'cost': 0.4})
    spot_private_post_v1_order_orders_batchcancel = spotPrivatePostV1OrderOrdersBatchcancel = Entry('v1/order/orders/batchcancel', ['spot', 'private'], 'POST', {'cost': 0.4})
    spot_private_post_v2_algo_orders_cancel_all_after = spotPrivatePostV2AlgoOrdersCancelAllAfter = Entry('v2/algo-orders/cancel-all-after', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_algo_orders = spotPrivatePostV2AlgoOrders = Entry('v2/algo-orders', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_algo_orders_cancellation = spotPrivatePostV2AlgoOrdersCancellation = Entry('v2/algo-orders/cancellation', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_account_repayment = spotPrivatePostV2AccountRepayment = Entry('v2/account/repayment', ['spot', 'private'], 'POST', {'cost': 5})
    spot_private_post_v1_dw_transfer_in_margin = spotPrivatePostV1DwTransferInMargin = Entry('v1/dw/transfer-in/margin', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v1_dw_transfer_out_margin = spotPrivatePostV1DwTransferOutMargin = Entry('v1/dw/transfer-out/margin', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v1_margin_orders = spotPrivatePostV1MarginOrders = Entry('v1/margin/orders', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v1_margin_orders_order_id_repay = spotPrivatePostV1MarginOrdersOrderIdRepay = Entry('v1/margin/orders/{order-id}/repay', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v1_cross_margin_transfer_in = spotPrivatePostV1CrossMarginTransferIn = Entry('v1/cross-margin/transfer-in', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_cross_margin_transfer_out = spotPrivatePostV1CrossMarginTransferOut = Entry('v1/cross-margin/transfer-out', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_cross_margin_orders = spotPrivatePostV1CrossMarginOrders = Entry('v1/cross-margin/orders', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_cross_margin_orders_order_id_repay = spotPrivatePostV1CrossMarginOrdersOrderIdRepay = Entry('v1/cross-margin/orders/{order-id}/repay', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v1_stable_coin_exchange = spotPrivatePostV1StableCoinExchange = Entry('v1/stable-coin/exchange', ['spot', 'private'], 'POST', {'cost': 1})
    spot_private_post_v2_etp_creation = spotPrivatePostV2EtpCreation = Entry('v2/etp/creation', ['spot', 'private'], 'POST', {'cost': 5})
    spot_private_post_v2_etp_redemption = spotPrivatePostV2EtpRedemption = Entry('v2/etp/redemption', ['spot', 'private'], 'POST', {'cost': 5})
    spot_private_post_v2_etp_transactid_cancel = spotPrivatePostV2EtpTransactIdCancel = Entry('v2/etp/{transactId}/cancel', ['spot', 'private'], 'POST', {'cost': 10})
    spot_private_post_v2_etp_batch_cancel = spotPrivatePostV2EtpBatchCancel = Entry('v2/etp/batch-cancel', ['spot', 'private'], 'POST', {'cost': 50})
    contract_public_get_api_v1_timestamp = contractPublicGetApiV1Timestamp = Entry('api/v1/timestamp', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_heartbeat = contractPublicGetHeartbeat = Entry('heartbeat/', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_contract_info = contractPublicGetApiV1ContractContractInfo = Entry('api/v1/contract_contract_info', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_index = contractPublicGetApiV1ContractIndex = Entry('api/v1/contract_index', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_query_elements = contractPublicGetApiV1ContractQueryElements = Entry('api/v1/contract_query_elements', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_price_limit = contractPublicGetApiV1ContractPriceLimit = Entry('api/v1/contract_price_limit', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_open_interest = contractPublicGetApiV1ContractOpenInterest = Entry('api/v1/contract_open_interest', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_delivery_price = contractPublicGetApiV1ContractDeliveryPrice = Entry('api/v1/contract_delivery_price', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_depth = contractPublicGetMarketDepth = Entry('market/depth', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_bbo = contractPublicGetMarketBbo = Entry('market/bbo', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_history_kline = contractPublicGetMarketHistoryKline = Entry('market/history/kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_mark_price_kline = contractPublicGetIndexMarketHistoryMarkPriceKline = Entry('index/market/history/mark_price_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_detail_merged = contractPublicGetMarketDetailMerged = Entry('market/detail/merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_detail_batch_merged = contractPublicGetMarketDetailBatchMerged = Entry('market/detail/batch_merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_v2_market_detail_batch_merged = contractPublicGetV2MarketDetailBatchMerged = Entry('v2/market/detail/batch_merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_trade = contractPublicGetMarketTrade = Entry('market/trade', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_market_history_trade = contractPublicGetMarketHistoryTrade = Entry('market/history/trade', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_risk_info = contractPublicGetApiV1ContractRiskInfo = Entry('api/v1/contract_risk_info', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_insurance_fund = contractPublicGetApiV1ContractInsuranceFund = Entry('api/v1/contract_insurance_fund', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_adjustfactor = contractPublicGetApiV1ContractAdjustfactor = Entry('api/v1/contract_adjustfactor', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_his_open_interest = contractPublicGetApiV1ContractHisOpenInterest = Entry('api/v1/contract_his_open_interest', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_ladder_margin = contractPublicGetApiV1ContractLadderMargin = Entry('api/v1/contract_ladder_margin', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_api_state = contractPublicGetApiV1ContractApiState = Entry('api/v1/contract_api_state', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_elite_account_ratio = contractPublicGetApiV1ContractEliteAccountRatio = Entry('api/v1/contract_elite_account_ratio', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_elite_position_ratio = contractPublicGetApiV1ContractElitePositionRatio = Entry('api/v1/contract_elite_position_ratio', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_liquidation_orders = contractPublicGetApiV1ContractLiquidationOrders = Entry('api/v1/contract_liquidation_orders', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_settlement_records = contractPublicGetApiV1ContractSettlementRecords = Entry('api/v1/contract_settlement_records', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_index = contractPublicGetIndexMarketHistoryIndex = Entry('index/market/history/index', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_basis = contractPublicGetIndexMarketHistoryBasis = Entry('index/market/history/basis', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v1_contract_estimated_settlement_price = contractPublicGetApiV1ContractEstimatedSettlementPrice = Entry('api/v1/contract_estimated_settlement_price', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_api_v3_contract_liquidation_orders = contractPublicGetApiV3ContractLiquidationOrders = Entry('api/v3/contract_liquidation_orders', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_contract_info = contractPublicGetSwapApiV1SwapContractInfo = Entry('swap-api/v1/swap_contract_info', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_index = contractPublicGetSwapApiV1SwapIndex = Entry('swap-api/v1/swap_index', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_query_elements = contractPublicGetSwapApiV1SwapQueryElements = Entry('swap-api/v1/swap_query_elements', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_price_limit = contractPublicGetSwapApiV1SwapPriceLimit = Entry('swap-api/v1/swap_price_limit', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_open_interest = contractPublicGetSwapApiV1SwapOpenInterest = Entry('swap-api/v1/swap_open_interest', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_depth = contractPublicGetSwapExMarketDepth = Entry('swap-ex/market/depth', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_bbo = contractPublicGetSwapExMarketBbo = Entry('swap-ex/market/bbo', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_history_kline = contractPublicGetSwapExMarketHistoryKline = Entry('swap-ex/market/history/kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_swap_mark_price_kline = contractPublicGetIndexMarketHistorySwapMarkPriceKline = Entry('index/market/history/swap_mark_price_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_detail_merged = contractPublicGetSwapExMarketDetailMerged = Entry('swap-ex/market/detail/merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_v2_swap_ex_market_detail_batch_merged = contractPublicGetV2SwapExMarketDetailBatchMerged = Entry('v2/swap-ex/market/detail/batch_merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_swap_premium_index_kline = contractPublicGetIndexMarketHistorySwapPremiumIndexKline = Entry('index/market/history/swap_premium_index_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_detail_batch_merged = contractPublicGetSwapExMarketDetailBatchMerged = Entry('swap-ex/market/detail/batch_merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_trade = contractPublicGetSwapExMarketTrade = Entry('swap-ex/market/trade', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_ex_market_history_trade = contractPublicGetSwapExMarketHistoryTrade = Entry('swap-ex/market/history/trade', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_risk_info = contractPublicGetSwapApiV1SwapRiskInfo = Entry('swap-api/v1/swap_risk_info', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_insurance_fund = contractPublicGetSwapApiV1SwapInsuranceFund = Entry('swap-api/v1/swap_insurance_fund', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_adjustfactor = contractPublicGetSwapApiV1SwapAdjustfactor = Entry('swap-api/v1/swap_adjustfactor', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_his_open_interest = contractPublicGetSwapApiV1SwapHisOpenInterest = Entry('swap-api/v1/swap_his_open_interest', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_ladder_margin = contractPublicGetSwapApiV1SwapLadderMargin = Entry('swap-api/v1/swap_ladder_margin', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_api_state = contractPublicGetSwapApiV1SwapApiState = Entry('swap-api/v1/swap_api_state', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_elite_account_ratio = contractPublicGetSwapApiV1SwapEliteAccountRatio = Entry('swap-api/v1/swap_elite_account_ratio', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_elite_position_ratio = contractPublicGetSwapApiV1SwapElitePositionRatio = Entry('swap-api/v1/swap_elite_position_ratio', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_estimated_settlement_price = contractPublicGetSwapApiV1SwapEstimatedSettlementPrice = Entry('swap-api/v1/swap_estimated_settlement_price', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_liquidation_orders = contractPublicGetSwapApiV1SwapLiquidationOrders = Entry('swap-api/v1/swap_liquidation_orders', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_settlement_records = contractPublicGetSwapApiV1SwapSettlementRecords = Entry('swap-api/v1/swap_settlement_records', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_funding_rate = contractPublicGetSwapApiV1SwapFundingRate = Entry('swap-api/v1/swap_funding_rate', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_batch_funding_rate = contractPublicGetSwapApiV1SwapBatchFundingRate = Entry('swap-api/v1/swap_batch_funding_rate', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_swap_historical_funding_rate = contractPublicGetSwapApiV1SwapHistoricalFundingRate = Entry('swap-api/v1/swap_historical_funding_rate', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v3_swap_liquidation_orders = contractPublicGetSwapApiV3SwapLiquidationOrders = Entry('swap-api/v3/swap_liquidation_orders', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_swap_estimated_rate_kline = contractPublicGetIndexMarketHistorySwapEstimatedRateKline = Entry('index/market/history/swap_estimated_rate_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_swap_basis = contractPublicGetIndexMarketHistorySwapBasis = Entry('index/market/history/swap_basis', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_contract_info = contractPublicGetLinearSwapApiV1SwapContractInfo = Entry('linear-swap-api/v1/swap_contract_info', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_index = contractPublicGetLinearSwapApiV1SwapIndex = Entry('linear-swap-api/v1/swap_index', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_query_elements = contractPublicGetLinearSwapApiV1SwapQueryElements = Entry('linear-swap-api/v1/swap_query_elements', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_price_limit = contractPublicGetLinearSwapApiV1SwapPriceLimit = Entry('linear-swap-api/v1/swap_price_limit', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_open_interest = contractPublicGetLinearSwapApiV1SwapOpenInterest = Entry('linear-swap-api/v1/swap_open_interest', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_depth = contractPublicGetLinearSwapExMarketDepth = Entry('linear-swap-ex/market/depth', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_bbo = contractPublicGetLinearSwapExMarketBbo = Entry('linear-swap-ex/market/bbo', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_history_kline = contractPublicGetLinearSwapExMarketHistoryKline = Entry('linear-swap-ex/market/history/kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_linear_swap_mark_price_kline = contractPublicGetIndexMarketHistoryLinearSwapMarkPriceKline = Entry('index/market/history/linear_swap_mark_price_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_detail_merged = contractPublicGetLinearSwapExMarketDetailMerged = Entry('linear-swap-ex/market/detail/merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_detail_batch_merged = contractPublicGetLinearSwapExMarketDetailBatchMerged = Entry('linear-swap-ex/market/detail/batch_merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_v2_linear_swap_ex_market_detail_batch_merged = contractPublicGetV2LinearSwapExMarketDetailBatchMerged = Entry('v2/linear-swap-ex/market/detail/batch_merged', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_trade = contractPublicGetLinearSwapExMarketTrade = Entry('linear-swap-ex/market/trade', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_ex_market_history_trade = contractPublicGetLinearSwapExMarketHistoryTrade = Entry('linear-swap-ex/market/history/trade', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_risk_info = contractPublicGetLinearSwapApiV1SwapRiskInfo = Entry('linear-swap-api/v1/swap_risk_info', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_swap_api_v1_linear_swap_api_v1_swap_insurance_fund = contractPublicGetSwapApiV1LinearSwapApiV1SwapInsuranceFund = Entry('swap-api/v1/linear-swap-api/v1/swap_insurance_fund', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_adjustfactor = contractPublicGetLinearSwapApiV1SwapAdjustfactor = Entry('linear-swap-api/v1/swap_adjustfactor', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_cross_adjustfactor = contractPublicGetLinearSwapApiV1SwapCrossAdjustfactor = Entry('linear-swap-api/v1/swap_cross_adjustfactor', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_his_open_interest = contractPublicGetLinearSwapApiV1SwapHisOpenInterest = Entry('linear-swap-api/v1/swap_his_open_interest', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_ladder_margin = contractPublicGetLinearSwapApiV1SwapLadderMargin = Entry('linear-swap-api/v1/swap_ladder_margin', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_cross_ladder_margin = contractPublicGetLinearSwapApiV1SwapCrossLadderMargin = Entry('linear-swap-api/v1/swap_cross_ladder_margin', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_api_state = contractPublicGetLinearSwapApiV1SwapApiState = Entry('linear-swap-api/v1/swap_api_state', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_cross_transfer_state = contractPublicGetLinearSwapApiV1SwapCrossTransferState = Entry('linear-swap-api/v1/swap_cross_transfer_state', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_cross_trade_state = contractPublicGetLinearSwapApiV1SwapCrossTradeState = Entry('linear-swap-api/v1/swap_cross_trade_state', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_elite_account_ratio = contractPublicGetLinearSwapApiV1SwapEliteAccountRatio = Entry('linear-swap-api/v1/swap_elite_account_ratio', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_elite_position_ratio = contractPublicGetLinearSwapApiV1SwapElitePositionRatio = Entry('linear-swap-api/v1/swap_elite_position_ratio', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_liquidation_orders = contractPublicGetLinearSwapApiV1SwapLiquidationOrders = Entry('linear-swap-api/v1/swap_liquidation_orders', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_settlement_records = contractPublicGetLinearSwapApiV1SwapSettlementRecords = Entry('linear-swap-api/v1/swap_settlement_records', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_funding_rate = contractPublicGetLinearSwapApiV1SwapFundingRate = Entry('linear-swap-api/v1/swap_funding_rate', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_batch_funding_rate = contractPublicGetLinearSwapApiV1SwapBatchFundingRate = Entry('linear-swap-api/v1/swap_batch_funding_rate', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_historical_funding_rate = contractPublicGetLinearSwapApiV1SwapHistoricalFundingRate = Entry('linear-swap-api/v1/swap_historical_funding_rate', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v3_swap_liquidation_orders = contractPublicGetLinearSwapApiV3SwapLiquidationOrders = Entry('linear-swap-api/v3/swap_liquidation_orders', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_linear_swap_premium_index_kline = contractPublicGetIndexMarketHistoryLinearSwapPremiumIndexKline = Entry('index/market/history/linear_swap_premium_index_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_linear_swap_estimated_rate_kline = contractPublicGetIndexMarketHistoryLinearSwapEstimatedRateKline = Entry('index/market/history/linear_swap_estimated_rate_kline', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_index_market_history_linear_swap_basis = contractPublicGetIndexMarketHistoryLinearSwapBasis = Entry('index/market/history/linear_swap_basis', ['contract', 'public'], 'GET', {'cost': 1})
    contract_public_get_linear_swap_api_v1_swap_estimated_settlement_price = contractPublicGetLinearSwapApiV1SwapEstimatedSettlementPrice = Entry('linear-swap-api/v1/swap_estimated_settlement_price', ['contract', 'public'], 'GET', {'cost': 1})
    contract_private_get_api_v1_contract_sub_auth_list = contractPrivateGetApiV1ContractSubAuthList = Entry('api/v1/contract_sub_auth_list', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_api_v1_contract_api_trading_status = contractPrivateGetApiV1ContractApiTradingStatus = Entry('api/v1/contract_api_trading_status', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_swap_api_v1_swap_sub_auth_list = contractPrivateGetSwapApiV1SwapSubAuthList = Entry('swap-api/v1/swap_sub_auth_list', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_swap_api_v1_swap_api_trading_status = contractPrivateGetSwapApiV1SwapApiTradingStatus = Entry('swap-api/v1/swap_api_trading_status', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v1_swap_sub_auth_list = contractPrivateGetLinearSwapApiV1SwapSubAuthList = Entry('linear-swap-api/v1/swap_sub_auth_list', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v1_swap_api_trading_status = contractPrivateGetLinearSwapApiV1SwapApiTradingStatus = Entry('linear-swap-api/v1/swap_api_trading_status', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v1_swap_cross_position_side = contractPrivateGetLinearSwapApiV1SwapCrossPositionSide = Entry('linear-swap-api/v1/swap_cross_position_side', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v1_swap_position_side = contractPrivateGetLinearSwapApiV1SwapPositionSide = Entry('linear-swap-api/v1/swap_position_side', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v3_unified_account_info = contractPrivateGetLinearSwapApiV3UnifiedAccountInfo = Entry('linear-swap-api/v3/unified_account_info', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v3_fix_position_margin_change_record = contractPrivateGetLinearSwapApiV3FixPositionMarginChangeRecord = Entry('linear-swap-api/v3/fix_position_margin_change_record', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v3_swap_unified_account_type = contractPrivateGetLinearSwapApiV3SwapUnifiedAccountType = Entry('linear-swap-api/v3/swap_unified_account_type', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_get_linear_swap_api_v3_linear_swap_overview_account_info = contractPrivateGetLinearSwapApiV3LinearSwapOverviewAccountInfo = Entry('linear-swap-api/v3/linear_swap_overview_account_info', ['contract', 'private'], 'GET', {'cost': 1})
    contract_private_post_api_v1_contract_balance_valuation = contractPrivatePostApiV1ContractBalanceValuation = Entry('api/v1/contract_balance_valuation', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_account_info = contractPrivatePostApiV1ContractAccountInfo = Entry('api/v1/contract_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_position_info = contractPrivatePostApiV1ContractPositionInfo = Entry('api/v1/contract_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_sub_auth = contractPrivatePostApiV1ContractSubAuth = Entry('api/v1/contract_sub_auth', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_sub_account_list = contractPrivatePostApiV1ContractSubAccountList = Entry('api/v1/contract_sub_account_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_sub_account_info_list = contractPrivatePostApiV1ContractSubAccountInfoList = Entry('api/v1/contract_sub_account_info_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_sub_account_info = contractPrivatePostApiV1ContractSubAccountInfo = Entry('api/v1/contract_sub_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_sub_position_info = contractPrivatePostApiV1ContractSubPositionInfo = Entry('api/v1/contract_sub_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_financial_record = contractPrivatePostApiV1ContractFinancialRecord = Entry('api/v1/contract_financial_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_financial_record_exact = contractPrivatePostApiV1ContractFinancialRecordExact = Entry('api/v1/contract_financial_record_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_user_settlement_records = contractPrivatePostApiV1ContractUserSettlementRecords = Entry('api/v1/contract_user_settlement_records', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_order_limit = contractPrivatePostApiV1ContractOrderLimit = Entry('api/v1/contract_order_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_fee = contractPrivatePostApiV1ContractFee = Entry('api/v1/contract_fee', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_transfer_limit = contractPrivatePostApiV1ContractTransferLimit = Entry('api/v1/contract_transfer_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_position_limit = contractPrivatePostApiV1ContractPositionLimit = Entry('api/v1/contract_position_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_account_position_info = contractPrivatePostApiV1ContractAccountPositionInfo = Entry('api/v1/contract_account_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_master_sub_transfer = contractPrivatePostApiV1ContractMasterSubTransfer = Entry('api/v1/contract_master_sub_transfer', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_master_sub_transfer_record = contractPrivatePostApiV1ContractMasterSubTransferRecord = Entry('api/v1/contract_master_sub_transfer_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_available_level_rate = contractPrivatePostApiV1ContractAvailableLevelRate = Entry('api/v1/contract_available_level_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v3_contract_financial_record = contractPrivatePostApiV3ContractFinancialRecord = Entry('api/v3/contract_financial_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v3_contract_financial_record_exact = contractPrivatePostApiV3ContractFinancialRecordExact = Entry('api/v3/contract_financial_record_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_cancel_after = contractPrivatePostApiV1ContractCancelAfter = Entry('api/v1/contract-cancel-after', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_order = contractPrivatePostApiV1ContractOrder = Entry('api/v1/contract_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_batchorder = contractPrivatePostApiV1ContractBatchorder = Entry('api/v1/contract_batchorder', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_cancel = contractPrivatePostApiV1ContractCancel = Entry('api/v1/contract_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_cancelall = contractPrivatePostApiV1ContractCancelall = Entry('api/v1/contract_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_switch_lever_rate = contractPrivatePostApiV1ContractSwitchLeverRate = Entry('api/v1/contract_switch_lever_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_lightning_close_position = contractPrivatePostApiV1LightningClosePosition = Entry('api/v1/lightning_close_position', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_order_info = contractPrivatePostApiV1ContractOrderInfo = Entry('api/v1/contract_order_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_order_detail = contractPrivatePostApiV1ContractOrderDetail = Entry('api/v1/contract_order_detail', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_openorders = contractPrivatePostApiV1ContractOpenorders = Entry('api/v1/contract_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_hisorders = contractPrivatePostApiV1ContractHisorders = Entry('api/v1/contract_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_hisorders_exact = contractPrivatePostApiV1ContractHisordersExact = Entry('api/v1/contract_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_matchresults = contractPrivatePostApiV1ContractMatchresults = Entry('api/v1/contract_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_matchresults_exact = contractPrivatePostApiV1ContractMatchresultsExact = Entry('api/v1/contract_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v3_contract_hisorders = contractPrivatePostApiV3ContractHisorders = Entry('api/v3/contract_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v3_contract_hisorders_exact = contractPrivatePostApiV3ContractHisordersExact = Entry('api/v3/contract_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v3_contract_matchresults = contractPrivatePostApiV3ContractMatchresults = Entry('api/v3/contract_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v3_contract_matchresults_exact = contractPrivatePostApiV3ContractMatchresultsExact = Entry('api/v3/contract_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_trigger_order = contractPrivatePostApiV1ContractTriggerOrder = Entry('api/v1/contract_trigger_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_trigger_cancel = contractPrivatePostApiV1ContractTriggerCancel = Entry('api/v1/contract_trigger_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_trigger_cancelall = contractPrivatePostApiV1ContractTriggerCancelall = Entry('api/v1/contract_trigger_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_trigger_openorders = contractPrivatePostApiV1ContractTriggerOpenorders = Entry('api/v1/contract_trigger_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_trigger_hisorders = contractPrivatePostApiV1ContractTriggerHisorders = Entry('api/v1/contract_trigger_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_tpsl_order = contractPrivatePostApiV1ContractTpslOrder = Entry('api/v1/contract_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_tpsl_cancel = contractPrivatePostApiV1ContractTpslCancel = Entry('api/v1/contract_tpsl_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_tpsl_cancelall = contractPrivatePostApiV1ContractTpslCancelall = Entry('api/v1/contract_tpsl_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_tpsl_openorders = contractPrivatePostApiV1ContractTpslOpenorders = Entry('api/v1/contract_tpsl_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_tpsl_hisorders = contractPrivatePostApiV1ContractTpslHisorders = Entry('api/v1/contract_tpsl_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_relation_tpsl_order = contractPrivatePostApiV1ContractRelationTpslOrder = Entry('api/v1/contract_relation_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_track_order = contractPrivatePostApiV1ContractTrackOrder = Entry('api/v1/contract_track_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_track_cancel = contractPrivatePostApiV1ContractTrackCancel = Entry('api/v1/contract_track_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_track_cancelall = contractPrivatePostApiV1ContractTrackCancelall = Entry('api/v1/contract_track_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_track_openorders = contractPrivatePostApiV1ContractTrackOpenorders = Entry('api/v1/contract_track_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_api_v1_contract_track_hisorders = contractPrivatePostApiV1ContractTrackHisorders = Entry('api/v1/contract_track_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_balance_valuation = contractPrivatePostSwapApiV1SwapBalanceValuation = Entry('swap-api/v1/swap_balance_valuation', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_account_info = contractPrivatePostSwapApiV1SwapAccountInfo = Entry('swap-api/v1/swap_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_position_info = contractPrivatePostSwapApiV1SwapPositionInfo = Entry('swap-api/v1/swap_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_account_position_info = contractPrivatePostSwapApiV1SwapAccountPositionInfo = Entry('swap-api/v1/swap_account_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_sub_auth = contractPrivatePostSwapApiV1SwapSubAuth = Entry('swap-api/v1/swap_sub_auth', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_sub_account_list = contractPrivatePostSwapApiV1SwapSubAccountList = Entry('swap-api/v1/swap_sub_account_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_sub_account_info_list = contractPrivatePostSwapApiV1SwapSubAccountInfoList = Entry('swap-api/v1/swap_sub_account_info_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_sub_account_info = contractPrivatePostSwapApiV1SwapSubAccountInfo = Entry('swap-api/v1/swap_sub_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_sub_position_info = contractPrivatePostSwapApiV1SwapSubPositionInfo = Entry('swap-api/v1/swap_sub_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_financial_record = contractPrivatePostSwapApiV1SwapFinancialRecord = Entry('swap-api/v1/swap_financial_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_financial_record_exact = contractPrivatePostSwapApiV1SwapFinancialRecordExact = Entry('swap-api/v1/swap_financial_record_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_user_settlement_records = contractPrivatePostSwapApiV1SwapUserSettlementRecords = Entry('swap-api/v1/swap_user_settlement_records', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_available_level_rate = contractPrivatePostSwapApiV1SwapAvailableLevelRate = Entry('swap-api/v1/swap_available_level_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_order_limit = contractPrivatePostSwapApiV1SwapOrderLimit = Entry('swap-api/v1/swap_order_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_fee = contractPrivatePostSwapApiV1SwapFee = Entry('swap-api/v1/swap_fee', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_transfer_limit = contractPrivatePostSwapApiV1SwapTransferLimit = Entry('swap-api/v1/swap_transfer_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_position_limit = contractPrivatePostSwapApiV1SwapPositionLimit = Entry('swap-api/v1/swap_position_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_master_sub_transfer = contractPrivatePostSwapApiV1SwapMasterSubTransfer = Entry('swap-api/v1/swap_master_sub_transfer', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_master_sub_transfer_record = contractPrivatePostSwapApiV1SwapMasterSubTransferRecord = Entry('swap-api/v1/swap_master_sub_transfer_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v3_swap_financial_record = contractPrivatePostSwapApiV3SwapFinancialRecord = Entry('swap-api/v3/swap_financial_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v3_swap_financial_record_exact = contractPrivatePostSwapApiV3SwapFinancialRecordExact = Entry('swap-api/v3/swap_financial_record_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_cancel_after = contractPrivatePostSwapApiV1SwapCancelAfter = Entry('swap-api/v1/swap-cancel-after', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_order = contractPrivatePostSwapApiV1SwapOrder = Entry('swap-api/v1/swap_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_batchorder = contractPrivatePostSwapApiV1SwapBatchorder = Entry('swap-api/v1/swap_batchorder', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_cancel = contractPrivatePostSwapApiV1SwapCancel = Entry('swap-api/v1/swap_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_cancelall = contractPrivatePostSwapApiV1SwapCancelall = Entry('swap-api/v1/swap_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_lightning_close_position = contractPrivatePostSwapApiV1SwapLightningClosePosition = Entry('swap-api/v1/swap_lightning_close_position', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_switch_lever_rate = contractPrivatePostSwapApiV1SwapSwitchLeverRate = Entry('swap-api/v1/swap_switch_lever_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_order_info = contractPrivatePostSwapApiV1SwapOrderInfo = Entry('swap-api/v1/swap_order_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_order_detail = contractPrivatePostSwapApiV1SwapOrderDetail = Entry('swap-api/v1/swap_order_detail', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_openorders = contractPrivatePostSwapApiV1SwapOpenorders = Entry('swap-api/v1/swap_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_hisorders = contractPrivatePostSwapApiV1SwapHisorders = Entry('swap-api/v1/swap_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_hisorders_exact = contractPrivatePostSwapApiV1SwapHisordersExact = Entry('swap-api/v1/swap_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_matchresults = contractPrivatePostSwapApiV1SwapMatchresults = Entry('swap-api/v1/swap_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_matchresults_exact = contractPrivatePostSwapApiV1SwapMatchresultsExact = Entry('swap-api/v1/swap_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v3_swap_matchresults = contractPrivatePostSwapApiV3SwapMatchresults = Entry('swap-api/v3/swap_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v3_swap_matchresults_exact = contractPrivatePostSwapApiV3SwapMatchresultsExact = Entry('swap-api/v3/swap_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v3_swap_hisorders = contractPrivatePostSwapApiV3SwapHisorders = Entry('swap-api/v3/swap_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v3_swap_hisorders_exact = contractPrivatePostSwapApiV3SwapHisordersExact = Entry('swap-api/v3/swap_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_trigger_order = contractPrivatePostSwapApiV1SwapTriggerOrder = Entry('swap-api/v1/swap_trigger_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_trigger_cancel = contractPrivatePostSwapApiV1SwapTriggerCancel = Entry('swap-api/v1/swap_trigger_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_trigger_cancelall = contractPrivatePostSwapApiV1SwapTriggerCancelall = Entry('swap-api/v1/swap_trigger_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_trigger_openorders = contractPrivatePostSwapApiV1SwapTriggerOpenorders = Entry('swap-api/v1/swap_trigger_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_trigger_hisorders = contractPrivatePostSwapApiV1SwapTriggerHisorders = Entry('swap-api/v1/swap_trigger_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_tpsl_order = contractPrivatePostSwapApiV1SwapTpslOrder = Entry('swap-api/v1/swap_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_tpsl_cancel = contractPrivatePostSwapApiV1SwapTpslCancel = Entry('swap-api/v1/swap_tpsl_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_tpsl_cancelall = contractPrivatePostSwapApiV1SwapTpslCancelall = Entry('swap-api/v1/swap_tpsl_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_tpsl_openorders = contractPrivatePostSwapApiV1SwapTpslOpenorders = Entry('swap-api/v1/swap_tpsl_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_tpsl_hisorders = contractPrivatePostSwapApiV1SwapTpslHisorders = Entry('swap-api/v1/swap_tpsl_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_relation_tpsl_order = contractPrivatePostSwapApiV1SwapRelationTpslOrder = Entry('swap-api/v1/swap_relation_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_track_order = contractPrivatePostSwapApiV1SwapTrackOrder = Entry('swap-api/v1/swap_track_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_track_cancel = contractPrivatePostSwapApiV1SwapTrackCancel = Entry('swap-api/v1/swap_track_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_track_cancelall = contractPrivatePostSwapApiV1SwapTrackCancelall = Entry('swap-api/v1/swap_track_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_track_openorders = contractPrivatePostSwapApiV1SwapTrackOpenorders = Entry('swap-api/v1/swap_track_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_swap_api_v1_swap_track_hisorders = contractPrivatePostSwapApiV1SwapTrackHisorders = Entry('swap-api/v1/swap_track_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_lever_position_limit = contractPrivatePostLinearSwapApiV1SwapLeverPositionLimit = Entry('linear-swap-api/v1/swap_lever_position_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_lever_position_limit = contractPrivatePostLinearSwapApiV1SwapCrossLeverPositionLimit = Entry('linear-swap-api/v1/swap_cross_lever_position_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_balance_valuation = contractPrivatePostLinearSwapApiV1SwapBalanceValuation = Entry('linear-swap-api/v1/swap_balance_valuation', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_account_info = contractPrivatePostLinearSwapApiV1SwapAccountInfo = Entry('linear-swap-api/v1/swap_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_account_info = contractPrivatePostLinearSwapApiV1SwapCrossAccountInfo = Entry('linear-swap-api/v1/swap_cross_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_position_info = contractPrivatePostLinearSwapApiV1SwapPositionInfo = Entry('linear-swap-api/v1/swap_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_position_info = contractPrivatePostLinearSwapApiV1SwapCrossPositionInfo = Entry('linear-swap-api/v1/swap_cross_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_account_position_info = contractPrivatePostLinearSwapApiV1SwapAccountPositionInfo = Entry('linear-swap-api/v1/swap_account_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_account_position_info = contractPrivatePostLinearSwapApiV1SwapCrossAccountPositionInfo = Entry('linear-swap-api/v1/swap_cross_account_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_sub_auth = contractPrivatePostLinearSwapApiV1SwapSubAuth = Entry('linear-swap-api/v1/swap_sub_auth', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_sub_account_list = contractPrivatePostLinearSwapApiV1SwapSubAccountList = Entry('linear-swap-api/v1/swap_sub_account_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_sub_account_list = contractPrivatePostLinearSwapApiV1SwapCrossSubAccountList = Entry('linear-swap-api/v1/swap_cross_sub_account_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_sub_account_info_list = contractPrivatePostLinearSwapApiV1SwapSubAccountInfoList = Entry('linear-swap-api/v1/swap_sub_account_info_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_sub_account_info_list = contractPrivatePostLinearSwapApiV1SwapCrossSubAccountInfoList = Entry('linear-swap-api/v1/swap_cross_sub_account_info_list', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_sub_account_info = contractPrivatePostLinearSwapApiV1SwapSubAccountInfo = Entry('linear-swap-api/v1/swap_sub_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_sub_account_info = contractPrivatePostLinearSwapApiV1SwapCrossSubAccountInfo = Entry('linear-swap-api/v1/swap_cross_sub_account_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_sub_position_info = contractPrivatePostLinearSwapApiV1SwapSubPositionInfo = Entry('linear-swap-api/v1/swap_sub_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_sub_position_info = contractPrivatePostLinearSwapApiV1SwapCrossSubPositionInfo = Entry('linear-swap-api/v1/swap_cross_sub_position_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_financial_record = contractPrivatePostLinearSwapApiV1SwapFinancialRecord = Entry('linear-swap-api/v1/swap_financial_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_financial_record_exact = contractPrivatePostLinearSwapApiV1SwapFinancialRecordExact = Entry('linear-swap-api/v1/swap_financial_record_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_user_settlement_records = contractPrivatePostLinearSwapApiV1SwapUserSettlementRecords = Entry('linear-swap-api/v1/swap_user_settlement_records', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_user_settlement_records = contractPrivatePostLinearSwapApiV1SwapCrossUserSettlementRecords = Entry('linear-swap-api/v1/swap_cross_user_settlement_records', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_available_level_rate = contractPrivatePostLinearSwapApiV1SwapAvailableLevelRate = Entry('linear-swap-api/v1/swap_available_level_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_available_level_rate = contractPrivatePostLinearSwapApiV1SwapCrossAvailableLevelRate = Entry('linear-swap-api/v1/swap_cross_available_level_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_order_limit = contractPrivatePostLinearSwapApiV1SwapOrderLimit = Entry('linear-swap-api/v1/swap_order_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_fee = contractPrivatePostLinearSwapApiV1SwapFee = Entry('linear-swap-api/v1/swap_fee', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_transfer_limit = contractPrivatePostLinearSwapApiV1SwapTransferLimit = Entry('linear-swap-api/v1/swap_transfer_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_transfer_limit = contractPrivatePostLinearSwapApiV1SwapCrossTransferLimit = Entry('linear-swap-api/v1/swap_cross_transfer_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_position_limit = contractPrivatePostLinearSwapApiV1SwapPositionLimit = Entry('linear-swap-api/v1/swap_position_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_position_limit = contractPrivatePostLinearSwapApiV1SwapCrossPositionLimit = Entry('linear-swap-api/v1/swap_cross_position_limit', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_master_sub_transfer = contractPrivatePostLinearSwapApiV1SwapMasterSubTransfer = Entry('linear-swap-api/v1/swap_master_sub_transfer', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_master_sub_transfer_record = contractPrivatePostLinearSwapApiV1SwapMasterSubTransferRecord = Entry('linear-swap-api/v1/swap_master_sub_transfer_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_transfer_inner = contractPrivatePostLinearSwapApiV1SwapTransferInner = Entry('linear-swap-api/v1/swap_transfer_inner', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_financial_record = contractPrivatePostLinearSwapApiV3SwapFinancialRecord = Entry('linear-swap-api/v3/swap_financial_record', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_financial_record_exact = contractPrivatePostLinearSwapApiV3SwapFinancialRecordExact = Entry('linear-swap-api/v3/swap_financial_record_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_order = contractPrivatePostLinearSwapApiV1SwapOrder = Entry('linear-swap-api/v1/swap_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_order = contractPrivatePostLinearSwapApiV1SwapCrossOrder = Entry('linear-swap-api/v1/swap_cross_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_batchorder = contractPrivatePostLinearSwapApiV1SwapBatchorder = Entry('linear-swap-api/v1/swap_batchorder', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_batchorder = contractPrivatePostLinearSwapApiV1SwapCrossBatchorder = Entry('linear-swap-api/v1/swap_cross_batchorder', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cancel = contractPrivatePostLinearSwapApiV1SwapCancel = Entry('linear-swap-api/v1/swap_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_cancel = contractPrivatePostLinearSwapApiV1SwapCrossCancel = Entry('linear-swap-api/v1/swap_cross_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cancelall = contractPrivatePostLinearSwapApiV1SwapCancelall = Entry('linear-swap-api/v1/swap_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_cancelall = contractPrivatePostLinearSwapApiV1SwapCrossCancelall = Entry('linear-swap-api/v1/swap_cross_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_switch_lever_rate = contractPrivatePostLinearSwapApiV1SwapSwitchLeverRate = Entry('linear-swap-api/v1/swap_switch_lever_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_switch_lever_rate = contractPrivatePostLinearSwapApiV1SwapCrossSwitchLeverRate = Entry('linear-swap-api/v1/swap_cross_switch_lever_rate', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_lightning_close_position = contractPrivatePostLinearSwapApiV1SwapLightningClosePosition = Entry('linear-swap-api/v1/swap_lightning_close_position', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_lightning_close_position = contractPrivatePostLinearSwapApiV1SwapCrossLightningClosePosition = Entry('linear-swap-api/v1/swap_cross_lightning_close_position', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_order_info = contractPrivatePostLinearSwapApiV1SwapOrderInfo = Entry('linear-swap-api/v1/swap_order_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_order_info = contractPrivatePostLinearSwapApiV1SwapCrossOrderInfo = Entry('linear-swap-api/v1/swap_cross_order_info', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_order_detail = contractPrivatePostLinearSwapApiV1SwapOrderDetail = Entry('linear-swap-api/v1/swap_order_detail', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_order_detail = contractPrivatePostLinearSwapApiV1SwapCrossOrderDetail = Entry('linear-swap-api/v1/swap_cross_order_detail', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_openorders = contractPrivatePostLinearSwapApiV1SwapOpenorders = Entry('linear-swap-api/v1/swap_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_openorders = contractPrivatePostLinearSwapApiV1SwapCrossOpenorders = Entry('linear-swap-api/v1/swap_cross_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_hisorders = contractPrivatePostLinearSwapApiV1SwapHisorders = Entry('linear-swap-api/v1/swap_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_hisorders = contractPrivatePostLinearSwapApiV1SwapCrossHisorders = Entry('linear-swap-api/v1/swap_cross_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_hisorders_exact = contractPrivatePostLinearSwapApiV1SwapHisordersExact = Entry('linear-swap-api/v1/swap_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_hisorders_exact = contractPrivatePostLinearSwapApiV1SwapCrossHisordersExact = Entry('linear-swap-api/v1/swap_cross_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_matchresults = contractPrivatePostLinearSwapApiV1SwapMatchresults = Entry('linear-swap-api/v1/swap_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_matchresults = contractPrivatePostLinearSwapApiV1SwapCrossMatchresults = Entry('linear-swap-api/v1/swap_cross_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_matchresults_exact = contractPrivatePostLinearSwapApiV1SwapMatchresultsExact = Entry('linear-swap-api/v1/swap_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_matchresults_exact = contractPrivatePostLinearSwapApiV1SwapCrossMatchresultsExact = Entry('linear-swap-api/v1/swap_cross_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_linear_cancel_after = contractPrivatePostLinearSwapApiV1LinearCancelAfter = Entry('linear-swap-api/v1/linear-cancel-after', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_switch_position_mode = contractPrivatePostLinearSwapApiV1SwapSwitchPositionMode = Entry('linear-swap-api/v1/swap_switch_position_mode', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_switch_position_mode = contractPrivatePostLinearSwapApiV1SwapCrossSwitchPositionMode = Entry('linear-swap-api/v1/swap_cross_switch_position_mode', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_matchresults = contractPrivatePostLinearSwapApiV3SwapMatchresults = Entry('linear-swap-api/v3/swap_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_cross_matchresults = contractPrivatePostLinearSwapApiV3SwapCrossMatchresults = Entry('linear-swap-api/v3/swap_cross_matchresults', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_matchresults_exact = contractPrivatePostLinearSwapApiV3SwapMatchresultsExact = Entry('linear-swap-api/v3/swap_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_cross_matchresults_exact = contractPrivatePostLinearSwapApiV3SwapCrossMatchresultsExact = Entry('linear-swap-api/v3/swap_cross_matchresults_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_hisorders = contractPrivatePostLinearSwapApiV3SwapHisorders = Entry('linear-swap-api/v3/swap_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_cross_hisorders = contractPrivatePostLinearSwapApiV3SwapCrossHisorders = Entry('linear-swap-api/v3/swap_cross_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_hisorders_exact = contractPrivatePostLinearSwapApiV3SwapHisordersExact = Entry('linear-swap-api/v3/swap_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_cross_hisorders_exact = contractPrivatePostLinearSwapApiV3SwapCrossHisordersExact = Entry('linear-swap-api/v3/swap_cross_hisorders_exact', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_fix_position_margin_change = contractPrivatePostLinearSwapApiV3FixPositionMarginChange = Entry('linear-swap-api/v3/fix_position_margin_change', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_swap_switch_account_type = contractPrivatePostLinearSwapApiV3SwapSwitchAccountType = Entry('linear-swap-api/v3/swap_switch_account_type', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v3_linear_swap_fee_switch = contractPrivatePostLinearSwapApiV3LinearSwapFeeSwitch = Entry('linear-swap-api/v3/linear_swap_fee_switch', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_trigger_order = contractPrivatePostLinearSwapApiV1SwapTriggerOrder = Entry('linear-swap-api/v1/swap_trigger_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_trigger_order = contractPrivatePostLinearSwapApiV1SwapCrossTriggerOrder = Entry('linear-swap-api/v1/swap_cross_trigger_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_trigger_cancel = contractPrivatePostLinearSwapApiV1SwapTriggerCancel = Entry('linear-swap-api/v1/swap_trigger_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_trigger_cancel = contractPrivatePostLinearSwapApiV1SwapCrossTriggerCancel = Entry('linear-swap-api/v1/swap_cross_trigger_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_trigger_cancelall = contractPrivatePostLinearSwapApiV1SwapTriggerCancelall = Entry('linear-swap-api/v1/swap_trigger_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_trigger_cancelall = contractPrivatePostLinearSwapApiV1SwapCrossTriggerCancelall = Entry('linear-swap-api/v1/swap_cross_trigger_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_trigger_openorders = contractPrivatePostLinearSwapApiV1SwapTriggerOpenorders = Entry('linear-swap-api/v1/swap_trigger_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_trigger_openorders = contractPrivatePostLinearSwapApiV1SwapCrossTriggerOpenorders = Entry('linear-swap-api/v1/swap_cross_trigger_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_trigger_hisorders = contractPrivatePostLinearSwapApiV1SwapTriggerHisorders = Entry('linear-swap-api/v1/swap_trigger_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_trigger_hisorders = contractPrivatePostLinearSwapApiV1SwapCrossTriggerHisorders = Entry('linear-swap-api/v1/swap_cross_trigger_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_tpsl_order = contractPrivatePostLinearSwapApiV1SwapTpslOrder = Entry('linear-swap-api/v1/swap_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_tpsl_order = contractPrivatePostLinearSwapApiV1SwapCrossTpslOrder = Entry('linear-swap-api/v1/swap_cross_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_tpsl_cancel = contractPrivatePostLinearSwapApiV1SwapTpslCancel = Entry('linear-swap-api/v1/swap_tpsl_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_tpsl_cancel = contractPrivatePostLinearSwapApiV1SwapCrossTpslCancel = Entry('linear-swap-api/v1/swap_cross_tpsl_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_tpsl_cancelall = contractPrivatePostLinearSwapApiV1SwapTpslCancelall = Entry('linear-swap-api/v1/swap_tpsl_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_tpsl_cancelall = contractPrivatePostLinearSwapApiV1SwapCrossTpslCancelall = Entry('linear-swap-api/v1/swap_cross_tpsl_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_tpsl_openorders = contractPrivatePostLinearSwapApiV1SwapTpslOpenorders = Entry('linear-swap-api/v1/swap_tpsl_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_tpsl_openorders = contractPrivatePostLinearSwapApiV1SwapCrossTpslOpenorders = Entry('linear-swap-api/v1/swap_cross_tpsl_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_tpsl_hisorders = contractPrivatePostLinearSwapApiV1SwapTpslHisorders = Entry('linear-swap-api/v1/swap_tpsl_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_tpsl_hisorders = contractPrivatePostLinearSwapApiV1SwapCrossTpslHisorders = Entry('linear-swap-api/v1/swap_cross_tpsl_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_relation_tpsl_order = contractPrivatePostLinearSwapApiV1SwapRelationTpslOrder = Entry('linear-swap-api/v1/swap_relation_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_relation_tpsl_order = contractPrivatePostLinearSwapApiV1SwapCrossRelationTpslOrder = Entry('linear-swap-api/v1/swap_cross_relation_tpsl_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_track_order = contractPrivatePostLinearSwapApiV1SwapTrackOrder = Entry('linear-swap-api/v1/swap_track_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_track_order = contractPrivatePostLinearSwapApiV1SwapCrossTrackOrder = Entry('linear-swap-api/v1/swap_cross_track_order', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_track_cancel = contractPrivatePostLinearSwapApiV1SwapTrackCancel = Entry('linear-swap-api/v1/swap_track_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_track_cancel = contractPrivatePostLinearSwapApiV1SwapCrossTrackCancel = Entry('linear-swap-api/v1/swap_cross_track_cancel', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_track_cancelall = contractPrivatePostLinearSwapApiV1SwapTrackCancelall = Entry('linear-swap-api/v1/swap_track_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_track_cancelall = contractPrivatePostLinearSwapApiV1SwapCrossTrackCancelall = Entry('linear-swap-api/v1/swap_cross_track_cancelall', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_track_openorders = contractPrivatePostLinearSwapApiV1SwapTrackOpenorders = Entry('linear-swap-api/v1/swap_track_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_track_openorders = contractPrivatePostLinearSwapApiV1SwapCrossTrackOpenorders = Entry('linear-swap-api/v1/swap_cross_track_openorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_track_hisorders = contractPrivatePostLinearSwapApiV1SwapTrackHisorders = Entry('linear-swap-api/v1/swap_track_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
    contract_private_post_linear_swap_api_v1_swap_cross_track_hisorders = contractPrivatePostLinearSwapApiV1SwapCrossTrackHisorders = Entry('linear-swap-api/v1/swap_cross_track_hisorders', ['contract', 'private'], 'POST', {'cost': 1})
