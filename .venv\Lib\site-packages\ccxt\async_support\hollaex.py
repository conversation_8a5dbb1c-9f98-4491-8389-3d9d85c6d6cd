# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.base.exchange import Exchange
from ccxt.abstract.hollaex import ImplicitAPI
import hashlib
from ccxt.base.types import Any, Balances, Currencies, Currency, DepositAddress, Int, Market, Num, Order, OrderBook, OrderSide, OrderType, Str, Strings, Ticker, Tickers, OrderBooks, Trade, TradingFees, Transaction
from typing import List
from ccxt.base.errors import AuthenticationError
from ccxt.base.errors import ArgumentsRequired
from ccxt.base.errors import BadRequest
from ccxt.base.errors import InsufficientFunds
from ccxt.base.errors import OrderNotFound
from ccxt.base.errors import OrderImmediatelyFillable
from ccxt.base.errors import NetworkError
from ccxt.base.decimal_to_precision import TICK_SIZE
from ccxt.base.precise import Precise


class hollaex(Exchange, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(hollaex, self).describe(), {
            'id': 'hollaex',
            'name': 'HollaEx',
            'countries': ['KR'],
            # 4 requests per second => 1000ms / 4 = 250 ms between requests
            'rateLimit': 250,
            'version': 'v2',
            'pro': True,
            'has': {
                'CORS': None,
                'spot': True,
                'margin': None,
                'swap': False,
                'future': False,
                'option': False,
                'addMargin': False,
                'cancelAllOrders': True,
                'cancelOrder': True,
                'createLimitBuyOrder': True,
                'createLimitSellOrder': True,
                'createMarketBuyOrder': True,
                'createMarketSellOrder': True,
                'createOrder': True,
                'createPostOnlyOrder': True,
                'createReduceOnlyOrder': False,
                'createStopLimitOrder': True,
                'createStopMarketOrder': True,
                'createStopOrder': True,
                'fetchBalance': True,
                'fetchBorrowRateHistories': False,
                'fetchBorrowRateHistory': False,
                'fetchClosedOrders': True,
                'fetchCrossBorrowRate': False,
                'fetchCrossBorrowRates': False,
                'fetchCurrencies': True,
                'fetchDepositAddress': 'emulated',
                'fetchDepositAddresses': True,
                'fetchDepositAddressesByNetwork': False,
                'fetchDeposits': True,
                'fetchFundingHistory': False,
                'fetchFundingRate': False,
                'fetchFundingRateHistory': False,
                'fetchFundingRates': False,
                'fetchIndexOHLCV': False,
                'fetchIsolatedBorrowRate': False,
                'fetchIsolatedBorrowRates': False,
                'fetchLeverage': False,
                'fetchMarginMode': False,
                'fetchMarkets': True,
                'fetchMarkOHLCV': False,
                'fetchMyTrades': True,
                'fetchOHLCV': True,
                'fetchOpenInterestHistory': False,
                'fetchOpenOrder': True,
                'fetchOpenOrders': True,
                'fetchOrder': True,
                'fetchOrderBook': True,
                'fetchOrderBooks': True,
                'fetchOrders': True,
                'fetchPosition': False,
                'fetchPositionMode': False,
                'fetchPositions': False,
                'fetchPositionsRisk': False,
                'fetchPremiumIndexOHLCV': False,
                'fetchTicker': True,
                'fetchTickers': True,
                'fetchTrades': True,
                'fetchTradingFee': False,
                'fetchTradingFees': True,
                'fetchTransactions': False,
                'fetchTransfer': False,
                'fetchTransfers': False,
                'fetchWithdrawal': True,
                'fetchWithdrawals': True,
                'reduceMargin': False,
                'sandbox': True,
                'setLeverage': False,
                'setMarginMode': False,
                'setPositionMode': False,
                'transfer': False,
                'withdraw': True,
            },
            'timeframes': {
                '1m': '1m',
                '5m': '5m',
                '15m': '15m',
                '1h': '1h',
                '4h': '4h',
                '1d': '1d',
                '1w': '1w',
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/1294454/75841031-ca375180-5ddd-11ea-8417-b975674c23cb.jpg',
                'test': {
                    'rest': 'https://api.sandbox.hollaex.com',
                },
                'api': {
                    'rest': 'https://api.hollaex.com',
                },
                'www': 'https://hollaex.com',
                'doc': 'https://apidocs.hollaex.com',
                'referral': 'https://pro.hollaex.com/signup?affiliation_code=QSWA6G',
            },
            'precisionMode': TICK_SIZE,
            'requiredCredentials': {
                'apiKey': True,
                'secret': True,
            },
            'api': {
                'public': {
                    'get': {
                        'health': 1,
                        'constants': 1,
                        'kit': 1,
                        'tiers': 1,
                        'ticker': 1,
                        'tickers': 1,
                        'orderbook': 1,
                        'orderbooks': 1,
                        'trades': 1,
                        'chart': 1,
                        'charts': 1,
                        'minicharts': 1,
                        'oracle/prices': 1,
                        'quick-trade': 1,
                        # TradingView
                        'udf/config': 1,
                        'udf/history': 1,
                        'udf/symbols': 1,
                    },
                },
                'private': {
                    'get': {
                        'user': 1,
                        'user/balance': 1,
                        'user/deposits': 1,
                        'user/withdrawals': 1,
                        'user/withdrawal/fee': 1,
                        'user/trades': 1,
                        'orders': 1,
                        'order': 1,
                    },
                    'post': {
                        'user/withdrawal': 1,
                        'order': 1,
                    },
                    'delete': {
                        'order/all': 1,
                        'order': 1,
                    },
                },
            },
            'features': {
                'spot': {
                    'sandbox': True,
                    'createOrder': {
                        'marginMode': False,
                        'triggerPrice': True,
                        'triggerPriceType': None,
                        'triggerDirection': False,
                        'stopLossPrice': False,  # todo
                        'takeProfitPrice': False,  # todo
                        'attachedStopLossTakeProfit': None,
                        'timeInForce': {
                            'IOC': False,
                            'FOK': False,
                            'PO': True,
                            'GTD': False,
                        },
                        'hedged': False,
                        'selfTradePrevention': False,
                        'trailing': False,
                        'leverage': False,
                        'marketBuyByCost': False,
                        'marketBuyRequiresPrice': False,
                        'iceberg': False,
                    },
                    'createOrders': None,
                    'fetchMyTrades': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 100000,
                        'untilDays': 100000,  # todo implement
                        'symbolRequired': False,
                    },
                    'fetchOrder': {
                        'marginMode': False,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOpenOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 100000,  # todo
                        'untilDays': 100000,  # todo
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchClosedOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 100000,  # todo
                        'daysBackCanceled': 1,  # todo
                        'untilDays': 100000,  # todo
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOHLCV': {
                        'limit': 1000,  # todo: no limit in request
                    },
                },
                'swap': {
                    'linear': None,
                    'inverse': None,
                },
                'future': {
                    'linear': None,
                    'inverse': None,
                },
            },
            'fees': {
                'trading': {
                    'tierBased': True,
                    'percentage': True,
                    'taker': 0.001,
                    'maker': 0.001,
                },
            },
            'exceptions': {
                'broad': {
                    'Invalid token': AuthenticationError,
                    'Order not found': OrderNotFound,
                    'Insufficient balance': InsufficientFunds,
                    'Error 1001 - Order rejected. Order could not be submitted order was set to a post only order.': OrderImmediatelyFillable,
                },
                'exact': {
                    '400': BadRequest,
                    '403': AuthenticationError,
                    '404': BadRequest,
                    '405': BadRequest,
                    '410': BadRequest,
                    '429': BadRequest,
                    '500': NetworkError,
                    '503': NetworkError,
                },
            },
            'options': {
                # how many seconds before the authenticated request expires
                'api-expires': self.parse_to_int(self.timeout / 1000),
                'networks': {
                    'BTC': 'btc',
                    'ETH': 'eth',
                    'ERC20': 'eth',
                    'TRX': 'trx',
                    'TRC20': 'trx',
                    'XRP': 'xrp',
                    'XLM': 'xlm',
                    'BNB': 'bnb',
                    'MATIC': 'matic',
                },
                'networksById': {
                    'eth': 'ERC20',
                    'ETH': 'ERC20',
                    'ERC20': 'ERC20',
                    'trx': 'TRC20',
                    'TRX': 'TRC20',
                    'TRC20': 'TRC20',
                },
            },
        })

    async def fetch_markets(self, params={}) -> List[Market]:
        """
        retrieves data on all markets for hollaex

        https://apidocs.hollaex.com/#constants

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        response = await self.publicGetConstants(params)
        #
        #     {
        #         "coins": {
        #             "xmr": {
        #                 "id": 7,
        #                 "fullname": "Monero",
        #                 "symbol": "xmr",
        #                 "active": True,
        #                 "allow_deposit": True,
        #                 "allow_withdrawal": True,
        #                 "withdrawal_fee": 0.02,
        #                 "min": 0.001,
        #                 "max": 100000,
        #                 "increment_unit": 0.001,
        #                 "deposit_limits": {'1': 0, '2': 0, '3': 0, '4': 0, "5": 0, "6": 0},
        #                 "withdrawal_limits": {'1': 10, '2': 15, '3': 100, '4': 100, '5': 200, '6': 300, '7': 350, '8': 400, "9": 500, "10": -1},
        #                 "created_at": "2019-12-09T07:14:02.720Z",
        #                 "updated_at": "2020-01-16T12:12:53.162Z"
        #             },
        #             # ...
        #         },
        #         "pairs": {
        #             "btc-usdt": {
        #                 "id": 2,
        #                 "name": "btc-usdt",
        #                 "pair_base": "btc",
        #                 "pair_2": "usdt",
        #                 "taker_fees": {'1': 0.3, '2': 0.25, '3': 0.2, '4': 0.18, '5': 0.1, '6': 0.09, '7': 0.08, '8': 0.06, "9": 0.04, "10": 0},
        #                 "maker_fees": {'1': 0.1, '2': 0.08, '3': 0.05, '4': 0.03, '5': 0, '6': 0, '7': 0, '8': 0, "9": 0, "10": 0},
        #                 "min_size": 0.0001,
        #                 "max_size": 1000,
        #                 "min_price": 100,
        #                 "max_price": 100000,
        #                 "increment_size": 0.0001,
        #                 "increment_price": 0.05,
        #                 "active": True,
        #                 "created_at": "2019-12-09T07:15:54.537Z",
        #                 "updated_at": "2019-12-09T07:15:54.537Z"
        #             },
        #         },
        #         "config": {tiers: 10},
        #         "status": True
        #     }
        #
        pairs = self.safe_value(response, 'pairs', {})
        keys = list(pairs.keys())
        result = []
        for i in range(0, len(keys)):
            key = keys[i]
            market = pairs[key]
            baseId = self.safe_string(market, 'pair_base')
            quoteId = self.safe_string(market, 'pair_2')
            base = self.common_currency_code(baseId.upper())
            quote = self.common_currency_code(quoteId.upper())
            result.append({
                'id': self.safe_string(market, 'name'),
                'symbol': base + '/' + quote,
                'base': base,
                'quote': quote,
                'settle': None,
                'baseId': baseId,
                'quoteId': quoteId,
                'settleId': None,
                'type': 'spot',
                'spot': True,
                'margin': False,
                'swap': False,
                'future': False,
                'option': False,
                'active': self.safe_value(market, 'active'),
                'contract': False,
                'linear': None,
                'inverse': None,
                'contractSize': None,
                'expiry': None,
                'expiryDatetime': None,
                'strike': None,
                'optionType': None,
                'precision': {
                    'amount': self.safe_number(market, 'increment_size'),
                    'price': self.safe_number(market, 'increment_price'),
                },
                'limits': {
                    'leverage': {
                        'min': None,
                        'max': None,
                    },
                    'amount': {
                        'min': self.safe_number(market, 'min_size'),
                        'max': self.safe_number(market, 'max_size'),
                    },
                    'price': {
                        'min': self.safe_number(market, 'min_price'),
                        'max': self.safe_number(market, 'max_price'),
                    },
                    'cost': {
                        'min': None,
                        'max': None,
                    },
                },
                'created': self.parse8601(self.safe_string(market, 'created_at')),
                'info': market,
            })
        return result

    async def fetch_currencies(self, params={}) -> Currencies:
        """
        fetches all available currencies on an exchange

        https://apidocs.hollaex.com/#constants

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an associative dictionary of currencies
        """
        response = await self.publicGetConstants(params)
        #
        #    {
        #        "coins": {
        #            "usdt": {
        #                "id": "6",
        #                "fullname": "USD Tether",
        #                "symbol": "usdt",
        #                "active": True,
        #                "verified": True,
        #                "allow_deposit": True,
        #                "allow_withdrawal": True,
        #                "withdrawal_fee": "20",
        #                "min": "1",
        #                "max": "10000000",
        #                "increment_unit": "0.0001",
        #                "logo": "https://hollaex-resources.s3.ap-southeast-1.amazonaws.com/icons/usdt.svg",
        #                "code": "usdt",
        #                "is_public": True,
        #                "meta": {
        #                    "color": "#27a17a",
        #                    "website": "https://tether.to",
        #                    "explorer": "https://blockchair.com/tether",
        #                    "decimal_points": "6"
        #                },
        #                "estimated_price": "1",
        #                "description": "<p>Tether(USDT) is a stablecoin pegged 1:1 to the US dollar. It is a digital currency that aims to maintain its value while allowing for fast and secure transfer of funds. It was the first stablecoin, and is the most widely used due stablecoin due to its stability and low volatility compared to other cryptocurrencies. It was launched in 2014 by Tether Limited.</p>",
        #                "type": "blockchain",
        #                "network": "eth,trx,bnb,matic",
        #                "standard": "",
        #                "issuer": "HollaEx",
        #                "withdrawal_fees": {
        #                    "bnb": {
        #                        "value": "0.8",
        #                        "active": True,
        #                        "symbol": "usdt"
        #                    },
        #                    "eth": {
        #                        "value": "1.5",
        #                        "active": True,
        #                        "symbol": "usdt"
        #                    },
        #                    "trx": {
        #                        "value": "4",
        #                        "active": True,
        #                        "symbol": "usdt"
        #                    },
        #                    "matic": {
        #                        "value": "0.3",
        #                        "active": True,
        #                        "symbol": "usdt"
        #                    }
        #                },
        #                "display_name": null,
        #                "deposit_fees": null,
        #                "is_risky": False,
        #                "market_cap": "144568098696.29",
        #                "category": "stable",
        #                "created_at": "2019-08-09T10:45:43.367Z",
        #                "updated_at": "2025-03-25T17:12:37.970Z",
        #                "created_by": "168",
        #                "owner_id": "1"
        #            },
        #         },
        #         "network":"https://api.hollaex.network"
        #     }
        #
        coins = self.safe_dict(response, 'coins', {})
        keys = list(coins.keys())
        result: dict = {}
        for i in range(0, len(keys)):
            key = keys[i]
            currency = coins[key]
            id = self.safe_string(currency, 'symbol')
            code = self.safe_currency_code(id)
            withdrawalLimits = self.safe_list(currency, 'withdrawal_limits', [])
            rawType = self.safe_string(currency, 'type')
            type = 'crypto' if (rawType == 'blockchain') else 'other'
            rawNetworks = self.safe_dict(currency, 'withdrawal_fees', {})
            networks = {}
            networkIds = list(rawNetworks.keys())
            for j in range(0, len(networkIds)):
                networkId = networkIds[j]
                networkEntry = self.safe_dict(rawNetworks, networkId)
                networkCode = self.network_id_to_code(networkId)
                networks[networkCode] = {
                    'id': networkId,
                    'network': networkCode,
                    'active': self.safe_bool(networkEntry, 'active'),
                    'deposit': None,
                    'withdraw': None,
                    'fee': self.safe_number(networkEntry, 'value'),
                    'precision': None,
                    'limits': {
                        'withdraw': {
                            'min': None,
                            'max': None,
                        },
                    },
                    'info': networkEntry,
                }
            result[code] = self.safe_currency_structure({
                'id': id,
                'numericId': self.safe_integer(currency, 'id'),
                'code': code,
                'info': currency,
                'name': self.safe_string(currency, 'fullname'),
                'active': self.safe_bool(currency, 'active'),
                'deposit': self.safe_bool(currency, 'allow_deposit'),
                'withdraw': self.safe_bool(currency, 'allow_withdrawal'),
                'fee': self.safe_number(currency, 'withdrawal_fee'),
                'precision': self.safe_number(currency, 'increment_unit'),
                'limits': {
                    'amount': {
                        'min': self.safe_number(currency, 'min'),
                        'max': self.safe_number(currency, 'max'),
                    },
                    'withdraw': {
                        'min': None,
                        'max': self.safe_value(withdrawalLimits, 0),
                    },
                },
                'networks': networks,
                'type': type,
            })
        return result

    async def fetch_order_books(self, symbols: Strings = None, limit: Int = None, params={}) -> OrderBooks:
        """
        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data for multiple markets

        https://apidocs.hollaex.com/#orderbooks

        :param str[]|None symbols: not used by hollaex fetchOrderBooks()
        :param int [limit]: not used by hollaex fetchOrderBooks()
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbol
        """
        await self.load_markets()
        response = await self.publicGetOrderbooks(params)
        result: dict = {}
        marketIds = list(response.keys())
        for i in range(0, len(marketIds)):
            marketId = marketIds[i]
            orderbook = response[marketId]
            symbol = self.safe_symbol(marketId, None, '-')
            timestamp = self.parse8601(self.safe_string(orderbook, 'timestamp'))
            result[symbol] = self.parse_order_book(response[marketId], symbol, timestamp)
        return result

    async def fetch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data

        https://apidocs.hollaex.com/#orderbook

        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.publicGetOrderbook(self.extend(request, params))
        #
        #     {
        #         "btc-usdt": {
        #             "bids": [
        #                 [8836.4, 1.022],
        #                 [8800, 0.0668],
        #                 [8797.75, 0.2398],
        #             ],
        #             "asks": [
        #                 [8839.35, 1.5334],
        #                 [8852.6, 0.0579],
        #                 [8860.45, 0.1815],
        #             ],
        #             "timestamp": "2020-03-03T02:27:25.147Z"
        #         },
        #         "eth-usdt": {},
        #         # ...
        #     }
        #
        orderbook = self.safe_value(response, market['id'])
        timestamp = self.parse8601(self.safe_string(orderbook, 'timestamp'))
        return self.parse_order_book(orderbook, market['symbol'], timestamp)

    async def fetch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market

        https://apidocs.hollaex.com/#ticker

        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.publicGetTicker(self.extend(request, params))
        #
        #     {
        #         "open": 8615.55,
        #         "close": 8841.05,
        #         "high": 8921.1,
        #         "low": 8607,
        #         "last": 8841.05,
        #         "volume": 20.2802,
        #         "timestamp": "2020-03-03T03:11:18.964Z"
        #     }
        #
        return self.parse_ticker(response, market)

    async def fetch_tickers(self, symbols: Strings = None, params={}) -> Tickers:
        """
        fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market

        https://apidocs.hollaex.com/#tickers

        :param str[]|None symbols: unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `ticker structures <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols)
        response = await self.publicGetTickers(params)
        #
        #     {
        #         "bch-usdt": {
        #             "time": "2020-03-02T04:29:45.011Z",
        #             "open": 341.65,
        #             "close":337.9,
        #             "high":341.65,
        #             "low":337.3,
        #             "last":337.9,
        #             "volume":0.054,
        #             "symbol":"bch-usdt"
        #         },
        #         # ...
        #     }
        #
        return self.parse_tickers(response, symbols)

    def parse_tickers(self, tickers, symbols: Strings = None, params={}) -> Tickers:
        result: dict = {}
        keys = list(tickers.keys())
        for i in range(0, len(keys)):
            key = keys[i]
            ticker = tickers[key]
            marketId = self.safe_string(ticker, 'symbol', key)
            market = self.safe_market(marketId, None, '-')
            symbol = market['symbol']
            result[symbol] = self.extend(self.parse_ticker(ticker, market), params)
        return self.filter_by_array_tickers(result, 'symbol', symbols)

    def parse_ticker(self, ticker: dict, market: Market = None) -> Ticker:
        #
        # fetchTicker
        #
        #     {
        #         "open": 8615.55,
        #         "close": 8841.05,
        #         "high": 8921.1,
        #         "low": 8607,
        #         "last": 8841.05,
        #         "volume": 20.2802,
        #         "timestamp": "2020-03-03T03:11:18.964Z",
        #     }
        #
        # fetchTickers
        #
        #     {
        #         "time": "2020-03-02T04:29:45.011Z",
        #         "open": 341.65,
        #         "close": 337.9,
        #         "high": 341.65,
        #         "low": 337.3,
        #         "last": 337.9,
        #         "volume": 0.054,
        #         "symbol": "bch-usdt"
        #     }
        #
        marketId = self.safe_string(ticker, 'symbol')
        market = self.safe_market(marketId, market, '-')
        symbol = market['symbol']
        timestamp = self.parse8601(self.safe_string_2(ticker, 'time', 'timestamp'))
        close = self.safe_string(ticker, 'close')
        return self.safe_ticker({
            'symbol': symbol,
            'info': ticker,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'high': self.safe_string(ticker, 'high'),
            'low': self.safe_string(ticker, 'low'),
            'bid': None,
            'bidVolume': None,
            'ask': None,
            'askVolume': None,
            'vwap': None,
            'open': self.safe_string(ticker, 'open'),
            'close': close,
            'last': self.safe_string(ticker, 'last', close),
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': self.safe_string(ticker, 'volume'),
            'quoteVolume': None,
        }, market)

    async def fetch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        get the list of most recent trades for a particular symbol

        https://apidocs.hollaex.com/#trades

        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
        }
        response = await self.publicGetTrades(self.extend(request, params))
        #
        #     {
        #         "btc-usdt": [
        #             {
        #                 "size": 0.5,
        #                 "price": 8830,
        #                 "side": "buy",
        #                 "timestamp": "2020-03-03T04:44:33.034Z"
        #             },
        #             # ...
        #         ]
        #     }
        #
        trades = self.safe_list(response, market['id'], [])
        return self.parse_trades(trades, market, since, limit)

    def parse_trade(self, trade: dict, market: Market = None) -> Trade:
        #
        # fetchTrades(public)
        #
        #     {
        #         "size": 0.5,
        #         "price": 8830,
        #         "side": "buy",
        #         "timestamp": "2020-03-03T04:44:33.034Z"
        #     }
        #
        # fetchMyTrades(private)
        #  {
        #      "side":"sell",
        #      "symbol":"doge-usdt",
        #      "size":70,
        #      "price":0.147411,
        #      "timestamp":"2022-01-26T17:53:34.650Z",
        #      "order_id":"cba78ecb-4187-4da2-9d2f-c259aa693b5a",
        #      "fee":0.01031877,"fee_coin":"usdt"
        #  }
        #
        marketId = self.safe_string(trade, 'symbol')
        market = self.safe_market(marketId, market, '-')
        symbol = market['symbol']
        datetime = self.safe_string(trade, 'timestamp')
        timestamp = self.parse8601(datetime)
        side = self.safe_string(trade, 'side')
        orderId = self.safe_string(trade, 'order_id')
        priceString = self.safe_string(trade, 'price')
        amountString = self.safe_string(trade, 'size')
        feeCostString = self.safe_string(trade, 'fee')
        fee = None
        if feeCostString is not None:
            fee = {
                'cost': feeCostString,
                'currency': market['quote'],
            }
        return self.safe_trade({
            'info': trade,
            'id': None,
            'timestamp': timestamp,
            'datetime': datetime,
            'symbol': symbol,
            'order': orderId,
            'type': None,
            'side': side,
            'takerOrMaker': None,
            'price': priceString,
            'amount': amountString,
            'cost': None,
            'fee': fee,
        }, market)

    async def fetch_trading_fees(self, params={}) -> TradingFees:
        """
        fetch the trading fees for multiple markets

        https://apidocs.hollaex.com/#tiers

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>` indexed by market symbols
        """
        await self.load_markets()
        response = await self.publicGetTiers(params)
        #
        #     {
        #         "1": {
        #             "id": "1",
        #             "name": "Silver",
        #             "icon": '',
        #             "description": "Your crypto journey starts here! Make your first deposit to start trading, and verify your account to level up!",
        #             "deposit_limit": "0",
        #             "withdrawal_limit": "1000",
        #             "fees": {
        #                 "maker": {
        #                     'eth-btc': "0.1",
        #                     'ada-usdt': "0.1",
        #                     ...
        #                 },
        #                 "taker": {
        #                     'eth-btc': "0.1",
        #                     'ada-usdt': "0.1",
        #                     ...
        #                 }
        #             },
        #             "note": "<ul>\n<li>Login and verify email</li>\n</ul>\n",
        #             "created_at": "2021-03-22T03:51:39.129Z",
        #             "updated_at": "2021-11-01T02:51:56.214Z"
        #         },
        #         ...
        #     }
        #
        firstTier = self.safe_value(response, '1', {})
        fees = self.safe_value(firstTier, 'fees', {})
        makerFees = self.safe_value(fees, 'maker', {})
        takerFees = self.safe_value(fees, 'taker', {})
        result: dict = {}
        for i in range(0, len(self.symbols)):
            symbol = self.symbols[i]
            market = self.market(symbol)
            makerString = self.safe_string(makerFees, market['id'])
            takerString = self.safe_string(takerFees, market['id'])
            result[symbol] = {
                'info': fees,
                'symbol': symbol,
                'maker': self.parse_number(Precise.string_div(makerString, '100')),
                'taker': self.parse_number(Precise.string_div(takerString, '100')),
                'percentage': True,
                'tierBased': True,
            }
        return result

    async def fetch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        hollaex has large gaps between candles, so it's recommended to specify since

        https://apidocs.hollaex.com/#chart

        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: timestamp in ms of the latest candle to fetch
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'symbol': market['id'],
            'resolution': self.safe_string(self.timeframes, timeframe, timeframe),
        }
        until = self.safe_integer(params, 'until')
        end = self.seconds()
        if until is not None:
            end = self.parse_to_int(until / 1000)
        defaultSpan = 2592000  # 30 days
        if since is not None:
            request['from'] = self.parse_to_int(since / 1000)
        else:
            request['from'] = end - defaultSpan
        request['to'] = end
        params = self.omit(params, 'until')
        response = await self.publicGetChart(self.extend(request, params))
        #
        #     [
        #         {
        #             "time":"2020-03-02T20:00:00.000Z",
        #             "close":8872.1,
        #             "high":8872.1,
        #             "low":8858.6,
        #             "open":8858.6,
        #             "symbol":"btc-usdt",
        #             "volume":1.2922
        #         },
        #     ]
        #
        return self.parse_ohlcvs(response, market, timeframe, since, limit)

    def parse_ohlcv(self, ohlcv, market: Market = None) -> list:
        #
        #     {
        #         "time":"2020-03-02T20:00:00.000Z",
        #         "close":8872.1,
        #         "high":8872.1,
        #         "low":8858.6,
        #         "open":8858.6,
        #         "symbol":"btc-usdt",
        #         "volume":1.2922
        #     }
        #
        return [
            self.parse8601(self.safe_string(ohlcv, 'time')),
            self.safe_number(ohlcv, 'open'),
            self.safe_number(ohlcv, 'high'),
            self.safe_number(ohlcv, 'low'),
            self.safe_number(ohlcv, 'close'),
            self.safe_number(ohlcv, 'volume'),
        ]

    def parse_balance(self, response) -> Balances:
        timestamp = self.parse8601(self.safe_string(response, 'updated_at'))
        result: dict = {
            'info': response,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
        }
        currencyIds = list(self.currencies_by_id.keys())
        for i in range(0, len(currencyIds)):
            currencyId = currencyIds[i]
            code = self.safe_currency_code(currencyId)
            account = self.account()
            account['free'] = self.safe_string(response, currencyId + '_available')
            account['total'] = self.safe_string(response, currencyId + '_balance')
            result[code] = account
        return self.safe_balance(result)

    async def fetch_balance(self, params={}) -> Balances:
        """
        query for balance and get the amount of funds available for trading or funds locked in orders

        https://apidocs.hollaex.com/#get-balance

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        await self.load_markets()
        response = await self.privateGetUserBalance(params)
        #
        #     {
        #         "updated_at": "2020-03-02T22:27:38.428Z",
        #         "btc_balance": 0,
        #         "btc_pending": 0,
        #         "btc_available": 0,
        #         "eth_balance": 0,
        #         "eth_pending": 0,
        #         "eth_available": 0,
        #         # ...
        #     }
        #
        return self.parse_balance(response)

    async def fetch_open_order(self, id: str, symbol: Str = None, params={}):
        """
        fetch an open order by it's id

        https://apidocs.hollaex.com/#get-order

        :param str id: order id
        :param str symbol: not used by hollaex fetchOpenOrder()
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {
            'order_id': id,
        }
        response = await self.privateGetOrder(self.extend(request, params))
        #
        #     {
        #         "id": "string",
        #         "side": "sell",
        #         "symbol": "xht-usdt",
        #         "size": 0.1,
        #         "filled": 0,
        #         "stop": null,
        #         "fee": 0,
        #         "fee_coin": "usdt",
        #         "type": "limit",
        #         "price": 1.09,
        #         "status": "new",
        #         "created_by": 116,
        #         "created_at": "2021-02-17T02:32:38.910Z",
        #         "updated_at": "2021-02-17T02:32:38.910Z",
        #         "User": {
        #             "id": 116,
        #             "email": "<EMAIL>",
        #             "username": "narrator",
        #             "exchange_id": 176
        #         }
        #     }
        #
        return self.parse_order(response)

    async def fetch_open_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders

        https://apidocs.hollaex.com/#get-all-orders

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of  open orders structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        request: dict = {
            'open': True,
        }
        return await self.fetch_orders(symbol, since, limit, self.extend(request, params))

    async def fetch_closed_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple closed orders made by the user

        https://apidocs.hollaex.com/#get-all-orders

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        request: dict = {
            'open': False,
        }
        return await self.fetch_orders(symbol, since, limit, self.extend(request, params))

    async def fetch_order(self, id: str, symbol: Str = None, params={}):
        """
        fetches information on an order made by the user

        https://apidocs.hollaex.com/#get-order

        :param str id:
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {
            'order_id': id,
        }
        response = await self.privateGetOrder(self.extend(request, params))
        #             {
        #                 "id": "string",
        #                 "side": "sell",
        #                 "symbol": "xht-usdt",
        #                 "size": 0.1,
        #                 "filled": 0,
        #                 "stop": null,
        #                 "fee": 0,
        #                 "fee_coin": "usdt",
        #                 "type": "limit",
        #                 "price": 1.09,
        #                 "status": "new",
        #                 "created_by": 116,
        #                 "created_at": "2021-02-17T02:32:38.910Z",
        #                 "updated_at": "2021-02-17T02:32:38.910Z",
        #                 "User": {
        #                     "id": 116,
        #                     "email": "<EMAIL>",
        #                     "username": "narrator",
        #                     "exchange_id": 176
        #                 }
        #             }
        order = response
        if order is None:
            raise OrderNotFound(self.id + ' fetchOrder() could not find order id ' + id)
        return self.parse_order(order)

    async def fetch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple orders made by the user

        https://apidocs.hollaex.com/#get-all-orders

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = None
        request: dict = {
            # 'symbol': market['id'],
            # 'side': 'buy',  # 'sell'
            # 'status': 'new',  # 'filled', 'pfilled', 'canceled'
            # 'open': True,
            # 'limit': limit,  # default 50, max 100
            # 'page': 1,
            # 'order_by': 'created_at',  # id, ...
            # 'order': 'asc',  # 'desc'
            # 'start_date': self.iso8601(since),
            # 'end_date': self.iso8601(self.milliseconds()),
        }
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        if since is not None:
            request['start_date'] = self.iso8601(since)
        if limit is not None:
            request['limit'] = limit  # default 50, max 100
        response = await self.privateGetOrders(self.extend(request, params))
        #
        #     {
        #         "count": 1,
        #         "data": [
        #             {
        #                 "id": "string",
        #                 "side": "sell",
        #                 "symbol": "xht-usdt",
        #                 "size": 0.1,
        #                 "filled": 0,
        #                 "stop": null,
        #                 "fee": 0,
        #                 "fee_coin": "usdt",
        #                 "type": "limit",
        #                 "price": 1.09,
        #                 "status": "new",
        #                 "created_by": 116,
        #                 "created_at": "2021-02-17T02:32:38.910Z",
        #                 "updated_at": "2021-02-17T02:32:38.910Z",
        #                 "User": {
        #                     "id": 116,
        #                     "email": "<EMAIL>",
        #                     "username": "narrator",
        #                     "exchange_id": 176
        #                 }
        #             }
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_orders(data, market, since, limit)

    def parse_order_status(self, status: Str):
        statuses: dict = {
            'new': 'open',
            'pfilled': 'open',
            'filled': 'closed',
            'canceled': 'canceled',
        }
        return self.safe_string(statuses, status, status)

    def parse_order(self, order: dict, market: Market = None) -> Order:
        #
        # createOrder, fetchOpenOrder, fetchOpenOrders
        #
        #     {
        #          "id":"10644b7e-3c90-4ba9-bc3b-188f3a4e9cfd",
        #          "created_by":140093,
        #          "exchange_id":22,
        #          "side":"buy",
        #          "symbol":"doge-usdt",
        #          "type":"limit",
        #          "price":0.05,
        #          "size":10,
        #          "stop":null,
        #          "filled":0,
        #          "status":"canceled",
        #          "fee":0,
        #          "fee_coin":"doge",
        #          "meta": {                # optional field only returned for postOnly orders
        #              "post_only":true
        #          },
        #          "fee_structure": {
        #              "maker":0.1,
        #              "taker":0.1
        #          },
        #          "created_at":"2022-05-31T08:14:14.747Z",
        #          "updated_at":"2022-05-31T08:14:23.727Z"
        #      }
        #
        marketId = self.safe_string(order, 'symbol')
        symbol = self.safe_symbol(marketId, market, '-')
        id = self.safe_string(order, 'id')
        timestamp = self.parse8601(self.safe_string(order, 'created_at'))
        type = self.safe_string(order, 'type')
        side = self.safe_string(order, 'side')
        price = self.safe_string(order, 'price')
        amount = self.safe_string(order, 'size')
        filled = self.safe_string(order, 'filled')
        status = self.parse_order_status(self.safe_string(order, 'status'))
        meta = self.safe_value(order, 'meta', {})
        postOnly = self.safe_bool(meta, 'post_only', False)
        return self.safe_order({
            'id': id,
            'clientOrderId': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': None,
            'status': status,
            'symbol': symbol,
            'type': type,
            'timeInForce': None,
            'postOnly': postOnly,
            'side': side,
            'price': price,
            'triggerPrice': self.safe_string(order, 'stop'),
            'amount': amount,
            'filled': filled,
            'remaining': None,
            'cost': None,
            'trades': None,
            'fee': None,
            'info': order,
            'average': None,
        }, market)

    async def create_order(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}):
        """
        create a trade order

        https://apidocs.hollaex.com/#create-order

        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param float [params.triggerPrice]: the price at which a trigger order is triggered at
        :param bool [params.postOnly]: if True, the order will only be posted to the order book and not executed immediately
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        convertedAmount = float(self.amount_to_precision(symbol, amount))
        request: dict = {
            'symbol': market['id'],
            'side': side,
            'size': self.normalize_number_if_needed(convertedAmount),
            'type': type,
            # 'stop': float(self.price_to_precision(symbol, stopPrice)),
            # 'meta': {},  # other options such
        }
        triggerPrice = self.safe_number_n(params, ['triggerPrice', 'stopPrice', 'stop'])
        meta = self.safe_value(params, 'meta', {})
        exchangeSpecificParam = self.safe_bool(meta, 'post_only', False)
        isMarketOrder = type == 'market'
        postOnly = self.is_post_only(isMarketOrder, exchangeSpecificParam, params)
        if not isMarketOrder:
            convertedPrice = float(self.price_to_precision(symbol, price))
            request['price'] = self.normalize_number_if_needed(convertedPrice)
        if triggerPrice is not None:
            request['stop'] = self.normalize_number_if_needed(float(self.price_to_precision(symbol, triggerPrice)))
        if postOnly:
            request['meta'] = {'post_only': True}
        params = self.omit(params, ['postOnly', 'timeInForce', 'stopPrice', 'triggerPrice', 'stop'])
        response = await self.privatePostOrder(self.extend(request, params))
        #
        #     {
        #         "fee": 0,
        #         "meta": {},
        #         "symbol": "xht-usdt",
        #         "side": "sell",
        #         "size": 0.1,
        #         "type": "limit",
        #         "price": 1,
        #         "fee_structure": {
        #             "maker": 0.2,
        #             "taker": 0.2
        #         },
        #         "fee_coin": "usdt",
        #         "id": "string",
        #         "created_by": 116,
        #         "filled": 0,
        #         "status": "new",
        #         "updated_at": "2021-02-17T03:03:19.231Z",
        #         "created_at": "2021-02-17T03:03:19.231Z",
        #         "stop": null
        #     }
        #
        return self.parse_order(response, market)

    async def cancel_order(self, id: str, symbol: Str = None, params={}):
        """
        cancels an open order

        https://apidocs.hollaex.com/#cancel-order

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {
            'order_id': id,
        }
        response = await self.privateDeleteOrder(self.extend(request, params))
        #
        #     {
        #         "title": "string",
        #         "symbol": "xht-usdt",
        #         "side": "sell",
        #         "size": 1,
        #         "type": "limit",
        #         "price": 0.1,
        #         "id": "string",
        #         "created_by": 34,
        #         "filled": 0
        #     }
        #
        return self.parse_order(response)

    async def cancel_all_orders(self, symbol: Str = None, params={}):
        """
        cancel all open orders in a market

        https://apidocs.hollaex.com/#cancel-all-orders

        :param str symbol: unified market symbol of the market to cancel orders in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' cancelAllOrders() requires a symbol argument')
        await self.load_markets()
        request: dict = {}
        market = None
        market = self.market(symbol)
        request['symbol'] = market['id']
        response = await self.privateDeleteOrderAll(self.extend(request, params))
        #
        #     [
        #         {
        #             "title": "string",
        #             "symbol": "xht-usdt",
        #             "side": "sell",
        #             "size": 1,
        #             "type": "limit",
        #             "price": 0.1,
        #             "id": "string",
        #             "created_by": 34,
        #             "filled": 0
        #         }
        #     ]
        #
        return self.parse_orders(response, market)

    async def fetch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all trades made by the user

        https://apidocs.hollaex.com/#get-trades

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        await self.load_markets()
        request: dict = {
            # 'symbol': market['id'],
            # 'limit': 50,  # default 50, max 100
            # 'page': 1,  # page of data to retrieve
            # 'order_by': 'timestamp',  # field to order data
            # 'order': 'asc',  # asc or desc
            # 'start_date': 123,  # starting date of queried data
            # 'end_date': 321,  # ending date of queried data
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['symbol'] = market['id']
        if limit is not None:
            request['limit'] = limit  # default 50, max 100
        if since is not None:
            request['start_date'] = self.iso8601(since)
        response = await self.privateGetUserTrades(self.extend(request, params))
        #
        #     {
        #         "count": 1,
        #         "data": [
        #             {
        #                 "side": "buy",
        #                 "symbol": "eth-usdt",
        #                 "size": 0.086,
        #                 "price": 226.19,
        #                 "timestamp": "2020-03-03T08:03:55.459Z",
        #                 "fee": 0.1
        #             }
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_trades(data, market, since, limit)

    def parse_deposit_address(self, depositAddress, currency: Currency = None) -> DepositAddress:
        #
        #     {
        #         "currency":"usdt",
        #         "address":"TECLD9XBH31XpyykdHU3uEAeUK7E6Lrmik",
        #         "network":"trx",
        #         "standard":null,
        #         "is_valid":true,
        #         "created_at":"2021-05-12T02:43:05.446Z"
        #     }
        #
        address = self.safe_string(depositAddress, 'address')
        tag = None
        if address is not None:
            parts = address.split(':')
            address = self.safe_string(parts, 0)
            tag = self.safe_string(parts, 1)
        self.check_address(address)
        currencyId = self.safe_string(depositAddress, 'currency')
        currency = self.safe_currency(currencyId, currency)
        network = self.safe_string(depositAddress, 'network')
        return {
            'info': depositAddress,
            'currency': currency['code'],
            'network': network,
            'address': address,
            'tag': tag,
        }

    async def fetch_deposit_addresses(self, codes: Strings = None, params={}) -> List[DepositAddress]:
        """
        fetch deposit addresses for multiple currencies and chain types

        https://apidocs.hollaex.com/#get-user

        :param str[]|None codes: list of unified currency codes, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a list of `address structures <https://docs.ccxt.com/#/?id=address-structure>`
        """
        await self.load_markets()
        network = self.safe_string(params, 'network')
        params = self.omit(params, 'network')
        response = await self.privateGetUser(params)
        #
        #     {
        #         "id":620,
        #         "email":"<EMAIL>",
        #         "full_name":"",
        #         "gender":false,
        #         "nationality":"",
        #         "dob":null,
        #         "phone_number":"",
        #         "address":{"city":"","address":"","country":"","postal_code":""},
        #         "id_data":{"note":"","type":"","number":"","status":0,"issued_date":"","expiration_date":""},
        #         "bank_account":[],
        #         "crypto_wallet":{},
        #         "verification_level":1,
        #         "email_verified":true,
        #         "otp_enabled":true,
        #         "activated":true,
        #         "username":"igor.kroitor",
        #         "affiliation_code":"QSWA6G",
        #         "settings":{
        #             "chat":{"set_username":false},
        #             "risk":{"popup_warning":false,"order_portfolio_percentage":20},
        #             "audio":{"public_trade":false,"order_completed":true,"order_partially_completed":true},
        #             "language":"en",
        #             "interface":{"theme":"white","order_book_levels":10},
        #             "notification":{"popup_order_completed":true,"popup_order_confirmation":true,"popup_order_partially_filled":true}
        #         },
        #         "affiliation_rate":0,
        #         "network_id":10620,
        #         "discount":0,
        #         "created_at":"2021-03-24T02:37:57.379Z",
        #         "updated_at":"2021-03-24T02:37:57.379Z",
        #         "balance":{
        #             "btc_balance":0,
        #             "btc_available":0,
        #             "eth_balance":0.000914,
        #             "eth_available":0.000914,
        #             "updated_at":"2020-03-04T04:03:27.174Z
        #         "},
        #         "wallet":[
        #             {"currency":"usdt","address":"TECLD9XBH31XpyykdHU3uEAeUK7E6Lrmik","network":"trx","standard":null,"is_valid":true,"created_at":"2021-05-12T02:43:05.446Z"},
        #             {"currency":"xrp","address":"rGcSzmuRx8qngPRnrvpCKkP9V4njeCPGCv:286741597","network":"xrp","standard":null,"is_valid":true,"created_at":"2021-05-12T02:49:01.273Z"}
        #         ]
        #     }
        #
        wallet = self.safe_value(response, 'wallet', [])
        addresses = wallet if (network is None) else self.filter_by(wallet, 'network', network)
        return self.parse_deposit_addresses(addresses, codes)

    async def fetch_deposits(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all deposits made to an account

        https://apidocs.hollaex.com/#get-deposits

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch deposits for
        :param int [limit]: the maximum number of deposits structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {
            # 'currency': currency['id'],
            # 'limit': 50,  # default 50, max 100
            # 'page': 1,  # page of data to retrieve
            # 'order_by': 'timestamp',  # field to order data
            # 'order': 'asc',  # asc or desc
            # 'start_date': 123,  # starting date of queried data
            # 'end_date': 321,  # ending date of queried data
        }
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['currency'] = currency['id']
        if limit is not None:
            request['limit'] = limit  # default 50, max 100
        if since is not None:
            request['start_date'] = self.iso8601(since)
        response = await self.privateGetUserDeposits(self.extend(request, params))
        #
        #     {
        #         "count": 1,
        #         "data": [
        #             {
        #                 "id": 539,
        #                 "amount": 20,
        #                 "fee": 0,
        #                 "address": "0x5c0cc98270d7089408fcbcc8e2131287f5be2306",
        #                 "transaction_id": "0xd4006327a5ec2c41adbdcf566eaaba6597c3d45906abe78ea1a4a022647c2e28",
        #                 "status": True,
        #                 "dismissed": False,
        #                 "rejected": False,
        #                 "description": "",
        #                 "type": "deposit",
        #                 "currency": "usdt",
        #                 "created_at": "2020-03-03T07:56:36.198Z",
        #                 "updated_at": "2020-03-03T08:00:05.674Z",
        #                 "user_id": 620
        #             }
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_transactions(data, currency, since, limit)

    async def fetch_withdrawal(self, id: str, code: Str = None, params={}):
        """
        fetch data on a currency withdrawal via the withdrawal id

        https://apidocs.hollaex.com/#get-withdrawals

        :param str id: withdrawal id
        :param str code: unified currency code of the currency withdrawn, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {
            'transaction_id': id,
        }
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['currency'] = currency['id']
        response = await self.privateGetUserWithdrawals(self.extend(request, params))
        #
        #     {
        #         "count": 1,
        #         "data": [
        #             {
        #                 "id": 539,
        #                 "amount": 20,
        #                 "fee": 0,
        #                 "address": "0x5c0cc98270d7089408fcbcc8e2131287f5be2306",
        #                 "transaction_id": "0xd4006327a5ec2c41adbdcf566eaaba6597c3d45906abe78ea1a4a022647c2e28",
        #                 "status": True,
        #                 "dismissed": False,
        #                 "rejected": False,
        #                 "description": "",
        #                 "type": "withdrawal",
        #                 "currency": "usdt",
        #                 "created_at": "2020-03-03T07:56:36.198Z",
        #                 "updated_at": "2020-03-03T08:00:05.674Z",
        #                 "user_id": 620
        #             }
        #         ]
        #     }
        #
        data = self.safe_value(response, 'data', [])
        transaction = self.safe_dict(data, 0, {})
        return self.parse_transaction(transaction, currency)

    async def fetch_withdrawals(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all withdrawals made from an account

        https://apidocs.hollaex.com/#get-withdrawals

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawals structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {
            # 'currency': currency['id'],
            # 'limit': 50,  # default 50, max 100
            # 'page': 1,  # page of data to retrieve
            # 'order_by': 'timestamp',  # field to order data
            # 'order': 'asc',  # asc or desc
            # 'start_date': 123,  # starting date of queried data
            # 'end_date': 321,  # ending date of queried data
        }
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['currency'] = currency['id']
        if limit is not None:
            request['limit'] = limit  # default 50, max 100
        if since is not None:
            request['start_date'] = self.iso8601(since)
        response = await self.privateGetUserWithdrawals(self.extend(request, params))
        #
        #     {
        #         "count": 1,
        #         "data": [
        #             {
        #                 "id": 539,
        #                 "amount": 20,
        #                 "fee": 0,
        #                 "address": "0x5c0cc98270d7089408fcbcc8e2131287f5be2306",
        #                 "transaction_id": "0xd4006327a5ec2c41adbdcf566eaaba6597c3d45906abe78ea1a4a022647c2e28",
        #                 "status": True,
        #                 "dismissed": False,
        #                 "rejected": False,
        #                 "description": "",
        #                 "type": "withdrawal",
        #                 "currency": "usdt",
        #                 "created_at": "2020-03-03T07:56:36.198Z",
        #                 "updated_at": "2020-03-03T08:00:05.674Z",
        #                 "user_id": 620
        #             }
        #         ]
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_transactions(data, currency, since, limit)

    def parse_transaction(self, transaction: dict, currency: Currency = None) -> Transaction:
        #
        # fetchWithdrawals, fetchDeposits
        #
        #     {
        #         "id": 539,
        #         "amount": 20,
        #         "fee": 0,
        #         "address": "0x5c0cc98270d7089408fcbcc8e2131287f5be2306",
        #         "transaction_id": "0xd4006327a5ec2c41adbdcf566eaaba6597c3d45906abe78ea1a4a022647c2e28",
        #         "status": True,
        #         "dismissed": False,
        #         "rejected": False,
        #         "description": "",
        #         "type": "withdrawal",
        #         "currency": "usdt",
        #         "created_at": "2020-03-03T07:56:36.198Z",
        #         "updated_at": "2020-03-03T08:00:05.674Z",
        #         "user_id": 620
        #     }
        #
        # withdraw
        #
        #     {
        #         "message": "Withdrawal request is in the queue and will be processed.",
        #         "transaction_id": "1d1683c3-576a-4d53-8ff5-27c93fd9758a",
        #         "amount": 1,
        #         "currency": "xht",
        #         "fee": 0,
        #         "fee_coin": "xht"
        #     }
        #
        id = self.safe_string(transaction, 'id')
        txid = self.safe_string(transaction, 'transaction_id')
        timestamp = self.parse8601(self.safe_string(transaction, 'created_at'))
        updated = self.parse8601(self.safe_string(transaction, 'updated_at'))
        type = self.safe_string(transaction, 'type')
        amount = self.safe_number(transaction, 'amount')
        address = self.safe_string(transaction, 'address')
        addressTo = None
        addressFrom = None
        tag = None
        tagTo = None
        tagFrom = None
        if address is not None:
            parts = address.split(':')
            address = self.safe_string(parts, 0)
            tag = self.safe_string(parts, 1)
            addressTo = address
            tagTo = tag
        currencyId = self.safe_string(transaction, 'currency')
        currency = self.safe_currency(currencyId, currency)
        status = self.safe_value(transaction, 'status')
        dismissed = self.safe_value(transaction, 'dismissed')
        rejected = self.safe_value(transaction, 'rejected')
        if status:
            status = 'ok'
        elif dismissed:
            status = 'canceled'
        elif rejected:
            status = 'failed'
        else:
            status = 'pending'
        feeCurrencyId = self.safe_string(transaction, 'fee_coin')
        feeCurrencyCode = self.safe_currency_code(feeCurrencyId, currency)
        feeCost = self.safe_number(transaction, 'fee')
        fee = None
        if feeCost is not None:
            fee = {
                'currency': feeCurrencyCode,
                'cost': feeCost,
            }
        return {
            'info': transaction,
            'id': id,
            'txid': txid,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'network': None,
            'addressFrom': addressFrom,
            'address': address,
            'addressTo': addressTo,
            'tagFrom': tagFrom,
            'tag': tag,
            'tagTo': tagTo,
            'type': type,
            'amount': amount,
            'currency': currency['code'],
            'status': status,
            'updated': updated,
            'comment': self.safe_string(transaction, 'message'),
            'internal': None,
            'fee': fee,
        }

    async def withdraw(self, code: str, amount: float, address: str, tag=None, params={}) -> Transaction:
        """
        make a withdrawal

        https://apidocs.hollaex.com/#withdrawal

        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        self.check_address(address)
        await self.load_markets()
        currency = self.currency(code)
        if tag is not None:
            address += ':' + tag
        network = self.safe_string(params, 'network')
        if network is None:
            raise ArgumentsRequired(self.id + ' withdraw() requires a network parameter')
        params = self.omit(params, 'network')
        request: dict = {
            'currency': currency['id'],
            'amount': amount,
            'address': address,
            'network': self.network_code_to_id(network, code),
        }
        response = await self.privatePostUserWithdrawal(self.extend(request, params))
        #
        #     {
        #         "message": "Withdrawal request is in the queue and will be processed.",
        #         "transaction_id": "1d1683c3-576a-4d53-8ff5-27c93fd9758a",
        #         "amount": 1,
        #         "currency": "xht",
        #         "fee": 0,
        #         "fee_coin": "xht"
        #     }
        #
        return self.parse_transaction(response, currency)

    def parse_deposit_withdraw_fee(self, fee, currency: Currency = None):
        #
        #    "bch":{
        #        "id":4,
        #        "fullname":"Bitcoin Cash",
        #        "symbol":"bch",
        #        "active":true,
        #        "verified":true,
        #        "allow_deposit":true,
        #        "allow_withdrawal":true,
        #        "withdrawal_fee":0.0001,
        #        "min":0.001,
        #        "max":100000,
        #        "increment_unit":0.001,
        #        "logo":"https://bitholla.s3.ap-northeast-2.amazonaws.com/icon/BCH-hollaex-asset-01.svg",
        #        "code":"bch",
        #        "is_public":true,
        #        "meta":{},
        #        "estimated_price":null,
        #        "description":null,
        #        "type":"blockchain",
        #        "network":null,
        #        "standard":null,
        #        "issuer":"HollaEx",
        #        "withdrawal_fees":null,
        #        "created_at":"2019-08-09T10:45:43.367Z",
        #        "updated_at":"2021-12-13T03:08:32.372Z",
        #        "created_by":1,
        #        "owner_id":1
        #    }
        #
        result: dict = {
            'info': fee,
            'withdraw': {
                'fee': None,
                'percentage': None,
            },
            'deposit': {
                'fee': None,
                'percentage': None,
            },
            'networks': {},
        }
        allowWithdrawal = self.safe_value(fee, 'allow_withdrawal')
        if allowWithdrawal:
            result['withdraw'] = {'fee': self.safe_number(fee, 'withdrawal_fee'), 'percentage': False}
        withdrawalFees = self.safe_value(fee, 'withdrawal_fees')
        if withdrawalFees is not None:
            keys = list(withdrawalFees.keys())
            keysLength = len(keys)
            for i in range(0, keysLength):
                key = keys[i]
                value = withdrawalFees[key]
                currencyId = self.safe_string(value, 'symbol')
                currencyCode = self.safe_currency_code(currencyId)
                networkCode = self.network_id_to_code(key, currencyCode)
                networkCodeUpper = networkCode.upper()  # default to the upper case network code
                withdrawalFee = self.safe_number(value, 'value')
                result['networks'][networkCodeUpper] = {
                    'deposit': None,
                    'withdraw': withdrawalFee,
                }
        return result

    async def fetch_deposit_withdraw_fees(self, codes: Strings = None, params={}):
        """
        fetch deposit and withdraw fees

        https://apidocs.hollaex.com/#constants

        :param str[]|None codes: list of unified currency codes
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a list of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        response = await self.publicGetConstants(params)
        #
        #     {
        #         "coins":{
        #             "bch":{
        #                 "id":4,
        #                 "fullname":"Bitcoin Cash",
        #                 "symbol":"bch",
        #                 "active":true,
        #                 "verified":true,
        #                 "allow_deposit":true,
        #                 "allow_withdrawal":true,
        #                 "withdrawal_fee":0.0001,
        #                 "min":0.001,
        #                 "max":100000,
        #                 "increment_unit":0.001,
        #                 "logo":"https://bitholla.s3.ap-northeast-2.amazonaws.com/icon/BCH-hollaex-asset-01.svg",
        #                 "code":"bch",
        #                 "is_public":true,
        #                 "meta":{},
        #                 "estimated_price":null,
        #                 "description":null,
        #                 "type":"blockchain",
        #                 "network":null,
        #                 "standard":null,
        #                 "issuer":"HollaEx",
        #                 "withdrawal_fees":null,
        #                 "created_at":"2019-08-09T10:45:43.367Z",
        #                 "updated_at":"2021-12-13T03:08:32.372Z",
        #                 "created_by":1,
        #                 "owner_id":1
        #             },
        #         },
        #         "network":"https://api.hollaex.network"
        #     }
        #
        coins = self.safe_dict(response, 'coins', {})
        return self.parse_deposit_withdraw_fees(coins, codes, 'symbol')

    def normalize_number_if_needed(self, number):
        if self.is_round_number(number):
            number = int(number)
        return number

    def sign(self, path, api='public', method='GET', params={}, headers=None, body=None):
        query = self.omit(params, self.extract_params(path))
        path = '/' + self.version + '/' + self.implode_params(path, params)
        if (method == 'GET') or (method == 'DELETE'):
            if query:
                path += '?' + self.urlencode(query)
        url = self.urls['api']['rest'] + path
        if api == 'private':
            self.check_required_credentials()
            defaultExpires = self.safe_integer_2(self.options, 'api-expires', 'expires', self.parse_to_int(self.timeout / 1000))
            expires = self.sum(self.seconds(), defaultExpires)
            expiresString = str(expires)
            auth = method + path + expiresString
            headers = {
                'api-key': self.apiKey,
                'api-expires': expiresString,
            }
            if method == 'POST':
                headers['Content-type'] = 'application/json'
                if query:
                    body = self.json(query)
                    auth += body
            signature = self.hmac(self.encode(auth), self.encode(self.secret), hashlib.sha256)
            headers['api-signature'] = signature
        return {'url': url, 'method': method, 'body': body, 'headers': headers}

    def handle_errors(self, code: int, reason: str, url: str, method: str, headers: dict, body: str, response, requestHeaders, requestBody):
        # {"message": "Invalid token"}
        if response is None:
            return None
        if (code >= 400) and (code <= 503):
            #
            #  {"message": "Invalid token"}
            #
            # different errors return the same code eg
            #
            #  {"message":"Error 1001 - Order rejected. Order could not be submitted order was set to a post only order."}
            #
            #  {"message":"Error 1001 - POST ONLY order can not be of type market"}
            #
            feedback = self.id + ' ' + body
            message = self.safe_string(response, 'message')
            self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
            status = str(code)
            self.throw_exactly_matched_exception(self.exceptions['exact'], status, feedback)
        return None
