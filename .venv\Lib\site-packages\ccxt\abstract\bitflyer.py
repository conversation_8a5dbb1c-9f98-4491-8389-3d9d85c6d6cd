from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_getmarkets_usa = publicGetGetmarketsUsa = Entry('getmarkets/usa', 'public', 'GET', {})
    public_get_getmarkets_eu = publicGetGetmarketsEu = Entry('getmarkets/eu', 'public', 'GET', {})
    public_get_getmarkets = publicGetGetmarkets = Entry('getmarkets', 'public', 'GET', {})
    public_get_getboard = publicGetGetboard = Entry('getboard', 'public', 'GET', {})
    public_get_getticker = publicGetGetticker = Entry('getticker', 'public', 'GET', {})
    public_get_getexecutions = publicGetGetexecutions = Entry('getexecutions', 'public', 'GET', {})
    public_get_gethealth = publicGetGethealth = Entry('gethealth', 'public', 'GET', {})
    public_get_getboardstate = publicGetGetboardstate = Entry('getboardstate', 'public', 'GET', {})
    public_get_getchats = publicGetGetchats = Entry('getchats', 'public', 'GET', {})
    public_get_getfundingrate = publicGetGetfundingrate = Entry('getfundingrate', 'public', 'GET', {})
    private_get_getpermissions = privateGetGetpermissions = Entry('getpermissions', 'private', 'GET', {})
    private_get_getbalance = privateGetGetbalance = Entry('getbalance', 'private', 'GET', {})
    private_get_getbalancehistory = privateGetGetbalancehistory = Entry('getbalancehistory', 'private', 'GET', {})
    private_get_getcollateral = privateGetGetcollateral = Entry('getcollateral', 'private', 'GET', {})
    private_get_getcollateralhistory = privateGetGetcollateralhistory = Entry('getcollateralhistory', 'private', 'GET', {})
    private_get_getcollateralaccounts = privateGetGetcollateralaccounts = Entry('getcollateralaccounts', 'private', 'GET', {})
    private_get_getaddresses = privateGetGetaddresses = Entry('getaddresses', 'private', 'GET', {})
    private_get_getcoinins = privateGetGetcoinins = Entry('getcoinins', 'private', 'GET', {})
    private_get_getcoinouts = privateGetGetcoinouts = Entry('getcoinouts', 'private', 'GET', {})
    private_get_getbankaccounts = privateGetGetbankaccounts = Entry('getbankaccounts', 'private', 'GET', {})
    private_get_getdeposits = privateGetGetdeposits = Entry('getdeposits', 'private', 'GET', {})
    private_get_getwithdrawals = privateGetGetwithdrawals = Entry('getwithdrawals', 'private', 'GET', {})
    private_get_getchildorders = privateGetGetchildorders = Entry('getchildorders', 'private', 'GET', {})
    private_get_getparentorders = privateGetGetparentorders = Entry('getparentorders', 'private', 'GET', {})
    private_get_getparentorder = privateGetGetparentorder = Entry('getparentorder', 'private', 'GET', {})
    private_get_getexecutions = privateGetGetexecutions = Entry('getexecutions', 'private', 'GET', {})
    private_get_getpositions = privateGetGetpositions = Entry('getpositions', 'private', 'GET', {})
    private_get_gettradingcommission = privateGetGettradingcommission = Entry('gettradingcommission', 'private', 'GET', {})
    private_post_sendcoin = privatePostSendcoin = Entry('sendcoin', 'private', 'POST', {})
    private_post_withdraw = privatePostWithdraw = Entry('withdraw', 'private', 'POST', {})
    private_post_sendchildorder = privatePostSendchildorder = Entry('sendchildorder', 'private', 'POST', {})
    private_post_cancelchildorder = privatePostCancelchildorder = Entry('cancelchildorder', 'private', 'POST', {})
    private_post_sendparentorder = privatePostSendparentorder = Entry('sendparentorder', 'private', 'POST', {})
    private_post_cancelparentorder = privatePostCancelparentorder = Entry('cancelparentorder', 'private', 'POST', {})
    private_post_cancelallchildorders = privatePostCancelallchildorders = Entry('cancelallchildorders', 'private', 'POST', {})
