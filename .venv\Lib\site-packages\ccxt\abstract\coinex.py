from ccxt.base.types import Entry


class ImplicitAPI:
    v1_public_get_amm_market = v1PublicGetAmmMarket = Entry('amm/market', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_common_currency_rate = v1PublicGetCommonCurrencyRate = Entry('common/currency/rate', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_common_asset_config = v1PublicGetCommonAssetConfig = Entry('common/asset/config', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_common_maintain_info = v1PublicGetCommonMaintainInfo = Entry('common/maintain/info', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_common_temp_maintain_info = v1PublicGetCommonTempMaintainInfo = Entry('common/temp-maintain/info', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_margin_market = v1PublicGetMarginMarket = Entry('margin/market', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_info = v1PublicGetMarketInfo = Entry('market/info', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_list = v1PublicGetMarketList = Entry('market/list', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_ticker = v1PublicGetMarketTicker = Entry('market/ticker', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_ticker_all = v1PublicGetMarketTickerAll = Entry('market/ticker/all', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_depth = v1PublicGetMarketDepth = Entry('market/depth', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_deals = v1PublicGetMarketDeals = Entry('market/deals', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_kline = v1PublicGetMarketKline = Entry('market/kline', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_market_detail = v1PublicGetMarketDetail = Entry('market/detail', ['v1', 'public'], 'GET', {'cost': 1})
    v1_private_get_account_amm_balance = v1PrivateGetAccountAmmBalance = Entry('account/amm/balance', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_account_investment_balance = v1PrivateGetAccountInvestmentBalance = Entry('account/investment/balance', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_account_balance_history = v1PrivateGetAccountBalanceHistory = Entry('account/balance/history', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_account_market_fee = v1PrivateGetAccountMarketFee = Entry('account/market/fee', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_balance_coin_deposit = v1PrivateGetBalanceCoinDeposit = Entry('balance/coin/deposit', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_balance_coin_withdraw = v1PrivateGetBalanceCoinWithdraw = Entry('balance/coin/withdraw', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_balance_info = v1PrivateGetBalanceInfo = Entry('balance/info', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_balance_deposit_address_coin_type = v1PrivateGetBalanceDepositAddressCoinType = Entry('balance/deposit/address/{coin_type}', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_contract_transfer_history = v1PrivateGetContractTransferHistory = Entry('contract/transfer/history', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_credit_info = v1PrivateGetCreditInfo = Entry('credit/info', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_credit_balance = v1PrivateGetCreditBalance = Entry('credit/balance', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_investment_transfer_history = v1PrivateGetInvestmentTransferHistory = Entry('investment/transfer/history', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_margin_account = v1PrivateGetMarginAccount = Entry('margin/account', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_margin_config = v1PrivateGetMarginConfig = Entry('margin/config', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_margin_loan_history = v1PrivateGetMarginLoanHistory = Entry('margin/loan/history', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_margin_transfer_history = v1PrivateGetMarginTransferHistory = Entry('margin/transfer/history', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_order_deals = v1PrivateGetOrderDeals = Entry('order/deals', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_order_finished = v1PrivateGetOrderFinished = Entry('order/finished', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_order_pending = v1PrivateGetOrderPending = Entry('order/pending', ['v1', 'private'], 'GET', {'cost': 8})
    v1_private_get_order_status = v1PrivateGetOrderStatus = Entry('order/status', ['v1', 'private'], 'GET', {'cost': 8})
    v1_private_get_order_status_batch = v1PrivateGetOrderStatusBatch = Entry('order/status/batch', ['v1', 'private'], 'GET', {'cost': 8})
    v1_private_get_order_user_deals = v1PrivateGetOrderUserDeals = Entry('order/user/deals', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_order_stop_finished = v1PrivateGetOrderStopFinished = Entry('order/stop/finished', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_order_stop_pending = v1PrivateGetOrderStopPending = Entry('order/stop/pending', ['v1', 'private'], 'GET', {'cost': 8})
    v1_private_get_order_user_trade_fee = v1PrivateGetOrderUserTradeFee = Entry('order/user/trade/fee', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_order_market_trade_info = v1PrivateGetOrderMarketTradeInfo = Entry('order/market/trade/info', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_sub_account_balance = v1PrivateGetSubAccountBalance = Entry('sub_account/balance', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_sub_account_transfer_history = v1PrivateGetSubAccountTransferHistory = Entry('sub_account/transfer/history', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_sub_account_auth_api = v1PrivateGetSubAccountAuthApi = Entry('sub_account/auth/api', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_get_sub_account_auth_api_user_auth_id = v1PrivateGetSubAccountAuthApiUserAuthId = Entry('sub_account/auth/api/{user_auth_id}', ['v1', 'private'], 'GET', {'cost': 40})
    v1_private_post_balance_coin_withdraw = v1PrivatePostBalanceCoinWithdraw = Entry('balance/coin/withdraw', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_contract_balance_transfer = v1PrivatePostContractBalanceTransfer = Entry('contract/balance/transfer', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_margin_flat = v1PrivatePostMarginFlat = Entry('margin/flat', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_margin_loan = v1PrivatePostMarginLoan = Entry('margin/loan', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_margin_transfer = v1PrivatePostMarginTransfer = Entry('margin/transfer', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_order_limit_batch = v1PrivatePostOrderLimitBatch = Entry('order/limit/batch', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_order_ioc = v1PrivatePostOrderIoc = Entry('order/ioc', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_order_limit = v1PrivatePostOrderLimit = Entry('order/limit', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_order_market = v1PrivatePostOrderMarket = Entry('order/market', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_order_modify = v1PrivatePostOrderModify = Entry('order/modify', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_order_stop_limit = v1PrivatePostOrderStopLimit = Entry('order/stop/limit', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_order_stop_market = v1PrivatePostOrderStopMarket = Entry('order/stop/market', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_order_stop_modify = v1PrivatePostOrderStopModify = Entry('order/stop/modify', ['v1', 'private'], 'POST', {'cost': 13.334})
    v1_private_post_sub_account_transfer = v1PrivatePostSubAccountTransfer = Entry('sub_account/transfer', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_sub_account_register = v1PrivatePostSubAccountRegister = Entry('sub_account/register', ['v1', 'private'], 'POST', {'cost': 1})
    v1_private_post_sub_account_unfrozen = v1PrivatePostSubAccountUnfrozen = Entry('sub_account/unfrozen', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_sub_account_frozen = v1PrivatePostSubAccountFrozen = Entry('sub_account/frozen', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_post_sub_account_auth_api = v1PrivatePostSubAccountAuthApi = Entry('sub_account/auth/api', ['v1', 'private'], 'POST', {'cost': 40})
    v1_private_put_balance_deposit_address_coin_type = v1PrivatePutBalanceDepositAddressCoinType = Entry('balance/deposit/address/{coin_type}', ['v1', 'private'], 'PUT', {'cost': 40})
    v1_private_put_sub_account_unfrozen = v1PrivatePutSubAccountUnfrozen = Entry('sub_account/unfrozen', ['v1', 'private'], 'PUT', {'cost': 40})
    v1_private_put_sub_account_frozen = v1PrivatePutSubAccountFrozen = Entry('sub_account/frozen', ['v1', 'private'], 'PUT', {'cost': 40})
    v1_private_put_sub_account_auth_api_user_auth_id = v1PrivatePutSubAccountAuthApiUserAuthId = Entry('sub_account/auth/api/{user_auth_id}', ['v1', 'private'], 'PUT', {'cost': 40})
    v1_private_put_v1_account_settings = v1PrivatePutV1AccountSettings = Entry('v1/account/settings', ['v1', 'private'], 'PUT', {'cost': 40})
    v1_private_delete_balance_coin_withdraw = v1PrivateDeleteBalanceCoinWithdraw = Entry('balance/coin/withdraw', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_private_delete_order_pending_batch = v1PrivateDeleteOrderPendingBatch = Entry('order/pending/batch', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_private_delete_order_pending = v1PrivateDeleteOrderPending = Entry('order/pending', ['v1', 'private'], 'DELETE', {'cost': 13.334})
    v1_private_delete_order_stop_pending = v1PrivateDeleteOrderStopPending = Entry('order/stop/pending', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_private_delete_order_stop_pending_id = v1PrivateDeleteOrderStopPendingId = Entry('order/stop/pending/{id}', ['v1', 'private'], 'DELETE', {'cost': 13.334})
    v1_private_delete_order_pending_by_client_id = v1PrivateDeleteOrderPendingByClientId = Entry('order/pending/by_client_id', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_private_delete_order_stop_pending_by_client_id = v1PrivateDeleteOrderStopPendingByClientId = Entry('order/stop/pending/by_client_id', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_private_delete_sub_account_auth_api_user_auth_id = v1PrivateDeleteSubAccountAuthApiUserAuthId = Entry('sub_account/auth/api/{user_auth_id}', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_private_delete_sub_account_authorize_id = v1PrivateDeleteSubAccountAuthorizeId = Entry('sub_account/authorize/{id}', ['v1', 'private'], 'DELETE', {'cost': 40})
    v1_perpetualpublic_get_ping = v1PerpetualPublicGetPing = Entry('ping', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_time = v1PerpetualPublicGetTime = Entry('time', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_list = v1PerpetualPublicGetMarketList = Entry('market/list', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_limit_config = v1PerpetualPublicGetMarketLimitConfig = Entry('market/limit_config', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_ticker = v1PerpetualPublicGetMarketTicker = Entry('market/ticker', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_ticker_all = v1PerpetualPublicGetMarketTickerAll = Entry('market/ticker/all', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_depth = v1PerpetualPublicGetMarketDepth = Entry('market/depth', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_deals = v1PerpetualPublicGetMarketDeals = Entry('market/deals', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_funding_history = v1PerpetualPublicGetMarketFundingHistory = Entry('market/funding_history', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualpublic_get_market_kline = v1PerpetualPublicGetMarketKline = Entry('market/kline', ['v1', 'perpetualPublic'], 'GET', {'cost': 1})
    v1_perpetualprivate_get_market_user_deals = v1PerpetualPrivateGetMarketUserDeals = Entry('market/user_deals', ['v1', 'perpetualPrivate'], 'GET', {'cost': 1})
    v1_perpetualprivate_get_asset_query = v1PerpetualPrivateGetAssetQuery = Entry('asset/query', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_order_pending = v1PerpetualPrivateGetOrderPending = Entry('order/pending', ['v1', 'perpetualPrivate'], 'GET', {'cost': 8})
    v1_perpetualprivate_get_order_finished = v1PerpetualPrivateGetOrderFinished = Entry('order/finished', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_order_stop_finished = v1PerpetualPrivateGetOrderStopFinished = Entry('order/stop_finished', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_order_stop_pending = v1PerpetualPrivateGetOrderStopPending = Entry('order/stop_pending', ['v1', 'perpetualPrivate'], 'GET', {'cost': 8})
    v1_perpetualprivate_get_order_status = v1PerpetualPrivateGetOrderStatus = Entry('order/status', ['v1', 'perpetualPrivate'], 'GET', {'cost': 8})
    v1_perpetualprivate_get_order_stop_status = v1PerpetualPrivateGetOrderStopStatus = Entry('order/stop_status', ['v1', 'perpetualPrivate'], 'GET', {'cost': 8})
    v1_perpetualprivate_get_position_finished = v1PerpetualPrivateGetPositionFinished = Entry('position/finished', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_position_pending = v1PerpetualPrivateGetPositionPending = Entry('position/pending', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_position_funding = v1PerpetualPrivateGetPositionFunding = Entry('position/funding', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_position_adl_history = v1PerpetualPrivateGetPositionAdlHistory = Entry('position/adl_history', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_market_preference = v1PerpetualPrivateGetMarketPreference = Entry('market/preference', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_position_margin_history = v1PerpetualPrivateGetPositionMarginHistory = Entry('position/margin_history', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_get_position_settle_history = v1PerpetualPrivateGetPositionSettleHistory = Entry('position/settle_history', ['v1', 'perpetualPrivate'], 'GET', {'cost': 40})
    v1_perpetualprivate_post_market_adjust_leverage = v1PerpetualPrivatePostMarketAdjustLeverage = Entry('market/adjust_leverage', ['v1', 'perpetualPrivate'], 'POST', {'cost': 1})
    v1_perpetualprivate_post_market_position_expect = v1PerpetualPrivatePostMarketPositionExpect = Entry('market/position_expect', ['v1', 'perpetualPrivate'], 'POST', {'cost': 1})
    v1_perpetualprivate_post_order_put_limit = v1PerpetualPrivatePostOrderPutLimit = Entry('order/put_limit', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_put_market = v1PerpetualPrivatePostOrderPutMarket = Entry('order/put_market', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_put_stop_limit = v1PerpetualPrivatePostOrderPutStopLimit = Entry('order/put_stop_limit', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_put_stop_market = v1PerpetualPrivatePostOrderPutStopMarket = Entry('order/put_stop_market', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_modify = v1PerpetualPrivatePostOrderModify = Entry('order/modify', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_modify_stop = v1PerpetualPrivatePostOrderModifyStop = Entry('order/modify_stop', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_cancel = v1PerpetualPrivatePostOrderCancel = Entry('order/cancel', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_cancel_all = v1PerpetualPrivatePostOrderCancelAll = Entry('order/cancel_all', ['v1', 'perpetualPrivate'], 'POST', {'cost': 40})
    v1_perpetualprivate_post_order_cancel_batch = v1PerpetualPrivatePostOrderCancelBatch = Entry('order/cancel_batch', ['v1', 'perpetualPrivate'], 'POST', {'cost': 40})
    v1_perpetualprivate_post_order_cancel_stop = v1PerpetualPrivatePostOrderCancelStop = Entry('order/cancel_stop', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_cancel_stop_all = v1PerpetualPrivatePostOrderCancelStopAll = Entry('order/cancel_stop_all', ['v1', 'perpetualPrivate'], 'POST', {'cost': 40})
    v1_perpetualprivate_post_order_close_limit = v1PerpetualPrivatePostOrderCloseLimit = Entry('order/close_limit', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_close_market = v1PerpetualPrivatePostOrderCloseMarket = Entry('order/close_market', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_position_adjust_margin = v1PerpetualPrivatePostPositionAdjustMargin = Entry('position/adjust_margin', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_position_stop_loss = v1PerpetualPrivatePostPositionStopLoss = Entry('position/stop_loss', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_position_take_profit = v1PerpetualPrivatePostPositionTakeProfit = Entry('position/take_profit', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_position_market_close = v1PerpetualPrivatePostPositionMarketClose = Entry('position/market_close', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_cancel_by_client_id = v1PerpetualPrivatePostOrderCancelByClientId = Entry('order/cancel/by_client_id', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_order_cancel_stop_by_client_id = v1PerpetualPrivatePostOrderCancelStopByClientId = Entry('order/cancel_stop/by_client_id', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v1_perpetualprivate_post_market_preference = v1PerpetualPrivatePostMarketPreference = Entry('market/preference', ['v1', 'perpetualPrivate'], 'POST', {'cost': 20})
    v2_public_get_maintain_info = v2PublicGetMaintainInfo = Entry('maintain/info', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_ping = v2PublicGetPing = Entry('ping', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_time = v2PublicGetTime = Entry('time', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_spot_market = v2PublicGetSpotMarket = Entry('spot/market', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_spot_ticker = v2PublicGetSpotTicker = Entry('spot/ticker', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_spot_depth = v2PublicGetSpotDepth = Entry('spot/depth', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_spot_deals = v2PublicGetSpotDeals = Entry('spot/deals', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_spot_kline = v2PublicGetSpotKline = Entry('spot/kline', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_spot_index = v2PublicGetSpotIndex = Entry('spot/index', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_market = v2PublicGetFuturesMarket = Entry('futures/market', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_ticker = v2PublicGetFuturesTicker = Entry('futures/ticker', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_depth = v2PublicGetFuturesDepth = Entry('futures/depth', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_deals = v2PublicGetFuturesDeals = Entry('futures/deals', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_kline = v2PublicGetFuturesKline = Entry('futures/kline', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_index = v2PublicGetFuturesIndex = Entry('futures/index', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_funding_rate = v2PublicGetFuturesFundingRate = Entry('futures/funding-rate', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_funding_rate_history = v2PublicGetFuturesFundingRateHistory = Entry('futures/funding-rate-history', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_position_level = v2PublicGetFuturesPositionLevel = Entry('futures/position-level', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_liquidation_history = v2PublicGetFuturesLiquidationHistory = Entry('futures/liquidation-history', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_basis_history = v2PublicGetFuturesBasisHistory = Entry('futures/basis-history', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_assets_deposit_withdraw_config = v2PublicGetAssetsDepositWithdrawConfig = Entry('assets/deposit-withdraw-config', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_assets_all_deposit_withdraw_config = v2PublicGetAssetsAllDepositWithdrawConfig = Entry('assets/all-deposit-withdraw-config', ['v2', 'public'], 'GET', {'cost': 1})
    v2_private_get_account_subs = v2PrivateGetAccountSubs = Entry('account/subs', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_account_subs_api_detail = v2PrivateGetAccountSubsApiDetail = Entry('account/subs/api-detail', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_account_subs_info = v2PrivateGetAccountSubsInfo = Entry('account/subs/info', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_account_subs_api = v2PrivateGetAccountSubsApi = Entry('account/subs/api', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_account_subs_transfer_history = v2PrivateGetAccountSubsTransferHistory = Entry('account/subs/transfer-history', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_account_subs_spot_balance = v2PrivateGetAccountSubsSpotBalance = Entry('account/subs/spot-balance', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_account_trade_fee_rate = v2PrivateGetAccountTradeFeeRate = Entry('account/trade-fee-rate', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_spot_balance = v2PrivateGetAssetsSpotBalance = Entry('assets/spot/balance', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_futures_balance = v2PrivateGetAssetsFuturesBalance = Entry('assets/futures/balance', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_margin_balance = v2PrivateGetAssetsMarginBalance = Entry('assets/margin/balance', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_assets_financial_balance = v2PrivateGetAssetsFinancialBalance = Entry('assets/financial/balance', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_amm_liquidity = v2PrivateGetAssetsAmmLiquidity = Entry('assets/amm/liquidity', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_credit_info = v2PrivateGetAssetsCreditInfo = Entry('assets/credit/info', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_margin_borrow_history = v2PrivateGetAssetsMarginBorrowHistory = Entry('assets/margin/borrow-history', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_margin_interest_limit = v2PrivateGetAssetsMarginInterestLimit = Entry('assets/margin/interest-limit', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_assets_deposit_address = v2PrivateGetAssetsDepositAddress = Entry('assets/deposit-address', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_deposit_history = v2PrivateGetAssetsDepositHistory = Entry('assets/deposit-history', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_withdraw = v2PrivateGetAssetsWithdraw = Entry('assets/withdraw', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_assets_transfer_history = v2PrivateGetAssetsTransferHistory = Entry('assets/transfer-history', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_spot_order_status = v2PrivateGetSpotOrderStatus = Entry('spot/order-status', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_spot_batch_order_status = v2PrivateGetSpotBatchOrderStatus = Entry('spot/batch-order-status', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_spot_pending_order = v2PrivateGetSpotPendingOrder = Entry('spot/pending-order', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_spot_finished_order = v2PrivateGetSpotFinishedOrder = Entry('spot/finished-order', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_spot_pending_stop_order = v2PrivateGetSpotPendingStopOrder = Entry('spot/pending-stop-order', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_spot_finished_stop_order = v2PrivateGetSpotFinishedStopOrder = Entry('spot/finished-stop-order', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_spot_user_deals = v2PrivateGetSpotUserDeals = Entry('spot/user-deals', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_spot_order_deals = v2PrivateGetSpotOrderDeals = Entry('spot/order-deals', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_futures_order_status = v2PrivateGetFuturesOrderStatus = Entry('futures/order-status', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_futures_batch_order_status = v2PrivateGetFuturesBatchOrderStatus = Entry('futures/batch-order-status', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_futures_pending_order = v2PrivateGetFuturesPendingOrder = Entry('futures/pending-order', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_futures_finished_order = v2PrivateGetFuturesFinishedOrder = Entry('futures/finished-order', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_futures_pending_stop_order = v2PrivateGetFuturesPendingStopOrder = Entry('futures/pending-stop-order', ['v2', 'private'], 'GET', {'cost': 8})
    v2_private_get_futures_finished_stop_order = v2PrivateGetFuturesFinishedStopOrder = Entry('futures/finished-stop-order', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_futures_user_deals = v2PrivateGetFuturesUserDeals = Entry('futures/user-deals', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_futures_order_deals = v2PrivateGetFuturesOrderDeals = Entry('futures/order-deals', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_futures_pending_position = v2PrivateGetFuturesPendingPosition = Entry('futures/pending-position', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_futures_finished_position = v2PrivateGetFuturesFinishedPosition = Entry('futures/finished-position', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_futures_position_margin_history = v2PrivateGetFuturesPositionMarginHistory = Entry('futures/position-margin-history', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_futures_position_funding_history = v2PrivateGetFuturesPositionFundingHistory = Entry('futures/position-funding-history', ['v2', 'private'], 'GET', {'cost': 40})
    v2_private_get_futures_position_adl_history = v2PrivateGetFuturesPositionAdlHistory = Entry('futures/position-adl-history', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_get_futures_position_settle_history = v2PrivateGetFuturesPositionSettleHistory = Entry('futures/position-settle-history', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_post_account_subs = v2PrivatePostAccountSubs = Entry('account/subs', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_subs_frozen = v2PrivatePostAccountSubsFrozen = Entry('account/subs/frozen', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_subs_unfrozen = v2PrivatePostAccountSubsUnfrozen = Entry('account/subs/unfrozen', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_subs_api = v2PrivatePostAccountSubsApi = Entry('account/subs/api', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_subs_edit_api = v2PrivatePostAccountSubsEditApi = Entry('account/subs/edit-api', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_subs_delete_api = v2PrivatePostAccountSubsDeleteApi = Entry('account/subs/delete-api', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_subs_transfer = v2PrivatePostAccountSubsTransfer = Entry('account/subs/transfer', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_account_settings = v2PrivatePostAccountSettings = Entry('account/settings', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_margin_borrow = v2PrivatePostAssetsMarginBorrow = Entry('assets/margin/borrow', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_margin_repay = v2PrivatePostAssetsMarginRepay = Entry('assets/margin/repay', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_renewal_deposit_address = v2PrivatePostAssetsRenewalDepositAddress = Entry('assets/renewal-deposit-address', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_withdraw = v2PrivatePostAssetsWithdraw = Entry('assets/withdraw', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_cancel_withdraw = v2PrivatePostAssetsCancelWithdraw = Entry('assets/cancel-withdraw', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_transfer = v2PrivatePostAssetsTransfer = Entry('assets/transfer', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_assets_amm_add_liquidity = v2PrivatePostAssetsAmmAddLiquidity = Entry('assets/amm/add-liquidity', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_assets_amm_remove_liquidity = v2PrivatePostAssetsAmmRemoveLiquidity = Entry('assets/amm/remove-liquidity', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_spot_order = v2PrivatePostSpotOrder = Entry('spot/order', ['v2', 'private'], 'POST', {'cost': 13.334})
    v2_private_post_spot_stop_order = v2PrivatePostSpotStopOrder = Entry('spot/stop-order', ['v2', 'private'], 'POST', {'cost': 13.334})
    v2_private_post_spot_batch_order = v2PrivatePostSpotBatchOrder = Entry('spot/batch-order', ['v2', 'private'], 'POST', {'cost': 40})
    v2_private_post_spot_batch_stop_order = v2PrivatePostSpotBatchStopOrder = Entry('spot/batch-stop-order', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_spot_modify_order = v2PrivatePostSpotModifyOrder = Entry('spot/modify-order', ['v2', 'private'], 'POST', {'cost': 13.334})
    v2_private_post_spot_modify_stop_order = v2PrivatePostSpotModifyStopOrder = Entry('spot/modify-stop-order', ['v2', 'private'], 'POST', {'cost': 13.334})
    v2_private_post_spot_cancel_all_order = v2PrivatePostSpotCancelAllOrder = Entry('spot/cancel-all-order', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_spot_cancel_order = v2PrivatePostSpotCancelOrder = Entry('spot/cancel-order', ['v2', 'private'], 'POST', {'cost': 6.667})
    v2_private_post_spot_cancel_stop_order = v2PrivatePostSpotCancelStopOrder = Entry('spot/cancel-stop-order', ['v2', 'private'], 'POST', {'cost': 6.667})
    v2_private_post_spot_cancel_batch_order = v2PrivatePostSpotCancelBatchOrder = Entry('spot/cancel-batch-order', ['v2', 'private'], 'POST', {'cost': 10})
    v2_private_post_spot_cancel_batch_stop_order = v2PrivatePostSpotCancelBatchStopOrder = Entry('spot/cancel-batch-stop-order', ['v2', 'private'], 'POST', {'cost': 10})
    v2_private_post_spot_cancel_order_by_client_id = v2PrivatePostSpotCancelOrderByClientId = Entry('spot/cancel-order-by-client-id', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_spot_cancel_stop_order_by_client_id = v2PrivatePostSpotCancelStopOrderByClientId = Entry('spot/cancel-stop-order-by-client-id', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_futures_order = v2PrivatePostFuturesOrder = Entry('futures/order', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_stop_order = v2PrivatePostFuturesStopOrder = Entry('futures/stop-order', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_batch_order = v2PrivatePostFuturesBatchOrder = Entry('futures/batch-order', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_futures_batch_stop_order = v2PrivatePostFuturesBatchStopOrder = Entry('futures/batch-stop-order', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_futures_modify_order = v2PrivatePostFuturesModifyOrder = Entry('futures/modify-order', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_modify_stop_order = v2PrivatePostFuturesModifyStopOrder = Entry('futures/modify-stop-order', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_cancel_all_order = v2PrivatePostFuturesCancelAllOrder = Entry('futures/cancel-all-order', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_futures_cancel_order = v2PrivatePostFuturesCancelOrder = Entry('futures/cancel-order', ['v2', 'private'], 'POST', {'cost': 10})
    v2_private_post_futures_cancel_stop_order = v2PrivatePostFuturesCancelStopOrder = Entry('futures/cancel-stop-order', ['v2', 'private'], 'POST', {'cost': 10})
    v2_private_post_futures_cancel_batch_order = v2PrivatePostFuturesCancelBatchOrder = Entry('futures/cancel-batch-order', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_cancel_batch_stop_order = v2PrivatePostFuturesCancelBatchStopOrder = Entry('futures/cancel-batch-stop-order', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_cancel_order_by_client_id = v2PrivatePostFuturesCancelOrderByClientId = Entry('futures/cancel-order-by-client-id', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_futures_cancel_stop_order_by_client_id = v2PrivatePostFuturesCancelStopOrderByClientId = Entry('futures/cancel-stop-order-by-client-id', ['v2', 'private'], 'POST', {'cost': 1})
    v2_private_post_futures_close_position = v2PrivatePostFuturesClosePosition = Entry('futures/close-position', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_adjust_position_margin = v2PrivatePostFuturesAdjustPositionMargin = Entry('futures/adjust-position-margin', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_adjust_position_leverage = v2PrivatePostFuturesAdjustPositionLeverage = Entry('futures/adjust-position-leverage', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_set_position_stop_loss = v2PrivatePostFuturesSetPositionStopLoss = Entry('futures/set-position-stop-loss', ['v2', 'private'], 'POST', {'cost': 20})
    v2_private_post_futures_set_position_take_profit = v2PrivatePostFuturesSetPositionTakeProfit = Entry('futures/set-position-take-profit', ['v2', 'private'], 'POST', {'cost': 20})
