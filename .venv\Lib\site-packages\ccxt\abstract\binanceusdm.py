from ccxt.base.types import Entry


class ImplicitAPI:
    sapi_get_copytrading_futures_userstatus = sapiGetCopyTradingFuturesUserStatus = Entry('copyTrading/futures/userStatus', 'sapi', 'GET', {'cost': 2})
    sapi_get_copytrading_futures_leadsymbol = sapiGetCopyTradingFuturesLeadSymbol = Entry('copyTrading/futures/leadSymbol', 'sapi', 'GET', {'cost': 2})
    sapi_get_system_status = sapiGetSystemStatus = Entry('system/status', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_accountsnapshot = sapiGetAccountSnapshot = Entry('accountSnapshot', 'sapi', 'GET', {'cost': 240})
    sapi_get_account_info = sapiGetAccountInfo = Entry('account/info', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_asset = sapiGetMarginAsset = Entry('margin/asset', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_pair = sapiGetMarginPair = Entry('margin/pair', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_allassets = sapiGetMarginAllAssets = Entry('margin/allAssets', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_allpairs = sapiGetMarginAllPairs = Entry('margin/allPairs', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_priceindex = sapiGetMarginPriceIndex = Entry('margin/priceIndex', 'sapi', 'GET', {'cost': 1})
    sapi_get_spot_delist_schedule = sapiGetSpotDelistSchedule = Entry('spot/delist-schedule', 'sapi', 'GET', {'cost': 10})
    sapi_get_asset_assetdividend = sapiGetAssetAssetDividend = Entry('asset/assetDividend', 'sapi', 'GET', {'cost': 1})
    sapi_get_asset_dribblet = sapiGetAssetDribblet = Entry('asset/dribblet', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_asset_transfer = sapiGetAssetTransfer = Entry('asset/transfer', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_asset_assetdetail = sapiGetAssetAssetDetail = Entry('asset/assetDetail', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_asset_tradefee = sapiGetAssetTradeFee = Entry('asset/tradeFee', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_asset_ledger_transfer_cloud_mining_querybypage = sapiGetAssetLedgerTransferCloudMiningQueryByPage = Entry('asset/ledger-transfer/cloud-mining/queryByPage', 'sapi', 'GET', {'cost': 4.0002})
    sapi_get_asset_convert_transfer_querybypage = sapiGetAssetConvertTransferQueryByPage = Entry('asset/convert-transfer/queryByPage', 'sapi', 'GET', {'cost': 0.033335})
    sapi_get_asset_wallet_balance = sapiGetAssetWalletBalance = Entry('asset/wallet/balance', 'sapi', 'GET', {'cost': 6})
    sapi_get_asset_custody_transfer_history = sapiGetAssetCustodyTransferHistory = Entry('asset/custody/transfer-history', 'sapi', 'GET', {'cost': 6})
    sapi_get_margin_borrow_repay = sapiGetMarginBorrowRepay = Entry('margin/borrow-repay', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_loan = sapiGetMarginLoan = Entry('margin/loan', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_repay = sapiGetMarginRepay = Entry('margin/repay', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_account = sapiGetMarginAccount = Entry('margin/account', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_transfer = sapiGetMarginTransfer = Entry('margin/transfer', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_interesthistory = sapiGetMarginInterestHistory = Entry('margin/interestHistory', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_forceliquidationrec = sapiGetMarginForceLiquidationRec = Entry('margin/forceLiquidationRec', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_order = sapiGetMarginOrder = Entry('margin/order', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_openorders = sapiGetMarginOpenOrders = Entry('margin/openOrders', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_allorders = sapiGetMarginAllOrders = Entry('margin/allOrders', 'sapi', 'GET', {'cost': 20})
    sapi_get_margin_mytrades = sapiGetMarginMyTrades = Entry('margin/myTrades', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_maxborrowable = sapiGetMarginMaxBorrowable = Entry('margin/maxBorrowable', 'sapi', 'GET', {'cost': 5})
    sapi_get_margin_maxtransferable = sapiGetMarginMaxTransferable = Entry('margin/maxTransferable', 'sapi', 'GET', {'cost': 5})
    sapi_get_margin_tradecoeff = sapiGetMarginTradeCoeff = Entry('margin/tradeCoeff', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_isolated_transfer = sapiGetMarginIsolatedTransfer = Entry('margin/isolated/transfer', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_isolated_account = sapiGetMarginIsolatedAccount = Entry('margin/isolated/account', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_isolated_pair = sapiGetMarginIsolatedPair = Entry('margin/isolated/pair', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_isolated_allpairs = sapiGetMarginIsolatedAllPairs = Entry('margin/isolated/allPairs', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_isolated_accountlimit = sapiGetMarginIsolatedAccountLimit = Entry('margin/isolated/accountLimit', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_interestratehistory = sapiGetMarginInterestRateHistory = Entry('margin/interestRateHistory', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_orderlist = sapiGetMarginOrderList = Entry('margin/orderList', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_allorderlist = sapiGetMarginAllOrderList = Entry('margin/allOrderList', 'sapi', 'GET', {'cost': 20})
    sapi_get_margin_openorderlist = sapiGetMarginOpenOrderList = Entry('margin/openOrderList', 'sapi', 'GET', {'cost': 1})
    sapi_get_margin_crossmargindata = sapiGetMarginCrossMarginData = Entry('margin/crossMarginData', 'sapi', 'GET', {'cost': 0.1, 'noCoin': 0.5})
    sapi_get_margin_isolatedmargindata = sapiGetMarginIsolatedMarginData = Entry('margin/isolatedMarginData', 'sapi', 'GET', {'cost': 0.1, 'noCoin': 1})
    sapi_get_margin_isolatedmargintier = sapiGetMarginIsolatedMarginTier = Entry('margin/isolatedMarginTier', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_ratelimit_order = sapiGetMarginRateLimitOrder = Entry('margin/rateLimit/order', 'sapi', 'GET', {'cost': 2})
    sapi_get_margin_dribblet = sapiGetMarginDribblet = Entry('margin/dribblet', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_margin_dust = sapiGetMarginDust = Entry('margin/dust', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_margin_crossmargincollateralratio = sapiGetMarginCrossMarginCollateralRatio = Entry('margin/crossMarginCollateralRatio', 'sapi', 'GET', {'cost': 10})
    sapi_get_margin_exchange_small_liability = sapiGetMarginExchangeSmallLiability = Entry('margin/exchange-small-liability', 'sapi', 'GET', {'cost': 0.6667})
    sapi_get_margin_exchange_small_liability_history = sapiGetMarginExchangeSmallLiabilityHistory = Entry('margin/exchange-small-liability-history', 'sapi', 'GET', {'cost': 0.6667})
    sapi_get_margin_next_hourly_interest_rate = sapiGetMarginNextHourlyInterestRate = Entry('margin/next-hourly-interest-rate', 'sapi', 'GET', {'cost': 0.6667})
    sapi_get_margin_capital_flow = sapiGetMarginCapitalFlow = Entry('margin/capital-flow', 'sapi', 'GET', {'cost': 10})
    sapi_get_margin_delist_schedule = sapiGetMarginDelistSchedule = Entry('margin/delist-schedule', 'sapi', 'GET', {'cost': 10})
    sapi_get_margin_available_inventory = sapiGetMarginAvailableInventory = Entry('margin/available-inventory', 'sapi', 'GET', {'cost': 0.3334})
    sapi_get_margin_leveragebracket = sapiGetMarginLeverageBracket = Entry('margin/leverageBracket', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_loan_vip_loanable_data = sapiGetLoanVipLoanableData = Entry('loan/vip/loanable/data', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_vip_collateral_data = sapiGetLoanVipCollateralData = Entry('loan/vip/collateral/data', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_vip_request_data = sapiGetLoanVipRequestData = Entry('loan/vip/request/data', 'sapi', 'GET', {'cost': 2.6668})
    sapi_get_loan_vip_request_interestrate = sapiGetLoanVipRequestInterestRate = Entry('loan/vip/request/interestRate', 'sapi', 'GET', {'cost': 2.6668})
    sapi_get_loan_income = sapiGetLoanIncome = Entry('loan/income', 'sapi', 'GET', {'cost': 40.002})
    sapi_get_loan_ongoing_orders = sapiGetLoanOngoingOrders = Entry('loan/ongoing/orders', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_ltv_adjustment_history = sapiGetLoanLtvAdjustmentHistory = Entry('loan/ltv/adjustment/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_borrow_history = sapiGetLoanBorrowHistory = Entry('loan/borrow/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_repay_history = sapiGetLoanRepayHistory = Entry('loan/repay/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_loanable_data = sapiGetLoanLoanableData = Entry('loan/loanable/data', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_collateral_data = sapiGetLoanCollateralData = Entry('loan/collateral/data', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_repay_collateral_rate = sapiGetLoanRepayCollateralRate = Entry('loan/repay/collateral/rate', 'sapi', 'GET', {'cost': 600})
    sapi_get_loan_flexible_ongoing_orders = sapiGetLoanFlexibleOngoingOrders = Entry('loan/flexible/ongoing/orders', 'sapi', 'GET', {'cost': 30})
    sapi_get_loan_flexible_borrow_history = sapiGetLoanFlexibleBorrowHistory = Entry('loan/flexible/borrow/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_flexible_repay_history = sapiGetLoanFlexibleRepayHistory = Entry('loan/flexible/repay/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_flexible_ltv_adjustment_history = sapiGetLoanFlexibleLtvAdjustmentHistory = Entry('loan/flexible/ltv/adjustment/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_vip_ongoing_orders = sapiGetLoanVipOngoingOrders = Entry('loan/vip/ongoing/orders', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_vip_repay_history = sapiGetLoanVipRepayHistory = Entry('loan/vip/repay/history', 'sapi', 'GET', {'cost': 40})
    sapi_get_loan_vip_collateral_account = sapiGetLoanVipCollateralAccount = Entry('loan/vip/collateral/account', 'sapi', 'GET', {'cost': 600})
    sapi_get_fiat_orders = sapiGetFiatOrders = Entry('fiat/orders', 'sapi', 'GET', {'cost': 600.03})
    sapi_get_fiat_payments = sapiGetFiatPayments = Entry('fiat/payments', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_futures_transfer = sapiGetFuturesTransfer = Entry('futures/transfer', 'sapi', 'GET', {'cost': 1})
    sapi_get_futures_histdatalink = sapiGetFuturesHistDataLink = Entry('futures/histDataLink', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_rebate_taxquery = sapiGetRebateTaxQuery = Entry('rebate/taxQuery', 'sapi', 'GET', {'cost': 80.004})
    sapi_get_capital_config_getall = sapiGetCapitalConfigGetall = Entry('capital/config/getall', 'sapi', 'GET', {'cost': 1})
    sapi_get_capital_deposit_address = sapiGetCapitalDepositAddress = Entry('capital/deposit/address', 'sapi', 'GET', {'cost': 1})
    sapi_get_capital_deposit_address_list = sapiGetCapitalDepositAddressList = Entry('capital/deposit/address/list', 'sapi', 'GET', {'cost': 1})
    sapi_get_capital_deposit_hisrec = sapiGetCapitalDepositHisrec = Entry('capital/deposit/hisrec', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_capital_deposit_subaddress = sapiGetCapitalDepositSubAddress = Entry('capital/deposit/subAddress', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_capital_deposit_subhisrec = sapiGetCapitalDepositSubHisrec = Entry('capital/deposit/subHisrec', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_capital_withdraw_history = sapiGetCapitalWithdrawHistory = Entry('capital/withdraw/history', 'sapi', 'GET', {'cost': 2})
    sapi_get_capital_withdraw_address_list = sapiGetCapitalWithdrawAddressList = Entry('capital/withdraw/address/list', 'sapi', 'GET', {'cost': 10})
    sapi_get_capital_contract_convertible_coins = sapiGetCapitalContractConvertibleCoins = Entry('capital/contract/convertible-coins', 'sapi', 'GET', {'cost': 4.0002})
    sapi_get_convert_tradeflow = sapiGetConvertTradeFlow = Entry('convert/tradeFlow', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_convert_exchangeinfo = sapiGetConvertExchangeInfo = Entry('convert/exchangeInfo', 'sapi', 'GET', {'cost': 50})
    sapi_get_convert_assetinfo = sapiGetConvertAssetInfo = Entry('convert/assetInfo', 'sapi', 'GET', {'cost': 10})
    sapi_get_convert_orderstatus = sapiGetConvertOrderStatus = Entry('convert/orderStatus', 'sapi', 'GET', {'cost': 0.6667})
    sapi_get_convert_limit_queryopenorders = sapiGetConvertLimitQueryOpenOrders = Entry('convert/limit/queryOpenOrders', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_account_status = sapiGetAccountStatus = Entry('account/status', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_account_apitradingstatus = sapiGetAccountApiTradingStatus = Entry('account/apiTradingStatus', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_account_apirestrictions_iprestriction = sapiGetAccountApiRestrictionsIpRestriction = Entry('account/apiRestrictions/ipRestriction', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_bnbburn = sapiGetBnbBurn = Entry('bnbBurn', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_futures_account = sapiGetSubAccountFuturesAccount = Entry('sub-account/futures/account', 'sapi', 'GET', {'cost': 1})
    sapi_get_sub_account_futures_accountsummary = sapiGetSubAccountFuturesAccountSummary = Entry('sub-account/futures/accountSummary', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_futures_positionrisk = sapiGetSubAccountFuturesPositionRisk = Entry('sub-account/futures/positionRisk', 'sapi', 'GET', {'cost': 1})
    sapi_get_sub_account_futures_internaltransfer = sapiGetSubAccountFuturesInternalTransfer = Entry('sub-account/futures/internalTransfer', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_list = sapiGetSubAccountList = Entry('sub-account/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_margin_account = sapiGetSubAccountMarginAccount = Entry('sub-account/margin/account', 'sapi', 'GET', {'cost': 1})
    sapi_get_sub_account_margin_accountsummary = sapiGetSubAccountMarginAccountSummary = Entry('sub-account/margin/accountSummary', 'sapi', 'GET', {'cost': 1})
    sapi_get_sub_account_spotsummary = sapiGetSubAccountSpotSummary = Entry('sub-account/spotSummary', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_status = sapiGetSubAccountStatus = Entry('sub-account/status', 'sapi', 'GET', {'cost': 1})
    sapi_get_sub_account_sub_transfer_history = sapiGetSubAccountSubTransferHistory = Entry('sub-account/sub/transfer/history', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_transfer_subuserhistory = sapiGetSubAccountTransferSubUserHistory = Entry('sub-account/transfer/subUserHistory', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_universaltransfer = sapiGetSubAccountUniversalTransfer = Entry('sub-account/universalTransfer', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_sub_account_apirestrictions_iprestriction_thirdpartylist = sapiGetSubAccountApiRestrictionsIpRestrictionThirdPartyList = Entry('sub-account/apiRestrictions/ipRestriction/thirdPartyList', 'sapi', 'GET', {'cost': 1})
    sapi_get_sub_account_transaction_statistics = sapiGetSubAccountTransactionStatistics = Entry('sub-account/transaction-statistics', 'sapi', 'GET', {'cost': 0.40002})
    sapi_get_sub_account_subaccountapi_iprestriction = sapiGetSubAccountSubAccountApiIpRestriction = Entry('sub-account/subAccountApi/ipRestriction', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_managed_subaccount_asset = sapiGetManagedSubaccountAsset = Entry('managed-subaccount/asset', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_managed_subaccount_accountsnapshot = sapiGetManagedSubaccountAccountSnapshot = Entry('managed-subaccount/accountSnapshot', 'sapi', 'GET', {'cost': 240})
    sapi_get_managed_subaccount_querytranslogforinvestor = sapiGetManagedSubaccountQueryTransLogForInvestor = Entry('managed-subaccount/queryTransLogForInvestor', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_managed_subaccount_querytranslogfortradeparent = sapiGetManagedSubaccountQueryTransLogForTradeParent = Entry('managed-subaccount/queryTransLogForTradeParent', 'sapi', 'GET', {'cost': 0.40002})
    sapi_get_managed_subaccount_fetch_future_asset = sapiGetManagedSubaccountFetchFutureAsset = Entry('managed-subaccount/fetch-future-asset', 'sapi', 'GET', {'cost': 0.40002})
    sapi_get_managed_subaccount_marginasset = sapiGetManagedSubaccountMarginAsset = Entry('managed-subaccount/marginAsset', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_managed_subaccount_info = sapiGetManagedSubaccountInfo = Entry('managed-subaccount/info', 'sapi', 'GET', {'cost': 0.40002})
    sapi_get_managed_subaccount_deposit_address = sapiGetManagedSubaccountDepositAddress = Entry('managed-subaccount/deposit/address', 'sapi', 'GET', {'cost': 0.006667})
    sapi_get_managed_subaccount_query_trans_log = sapiGetManagedSubaccountQueryTransLog = Entry('managed-subaccount/query-trans-log', 'sapi', 'GET', {'cost': 0.40002})
    sapi_get_lending_daily_product_list = sapiGetLendingDailyProductList = Entry('lending/daily/product/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_daily_userleftquota = sapiGetLendingDailyUserLeftQuota = Entry('lending/daily/userLeftQuota', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_daily_userredemptionquota = sapiGetLendingDailyUserRedemptionQuota = Entry('lending/daily/userRedemptionQuota', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_daily_token_position = sapiGetLendingDailyTokenPosition = Entry('lending/daily/token/position', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_union_account = sapiGetLendingUnionAccount = Entry('lending/union/account', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_union_purchaserecord = sapiGetLendingUnionPurchaseRecord = Entry('lending/union/purchaseRecord', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_union_redemptionrecord = sapiGetLendingUnionRedemptionRecord = Entry('lending/union/redemptionRecord', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_union_interesthistory = sapiGetLendingUnionInterestHistory = Entry('lending/union/interestHistory', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_project_list = sapiGetLendingProjectList = Entry('lending/project/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_project_position_list = sapiGetLendingProjectPositionList = Entry('lending/project/position/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_eth_staking_eth_history_stakinghistory = sapiGetEthStakingEthHistoryStakingHistory = Entry('eth-staking/eth/history/stakingHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_eth_history_redemptionhistory = sapiGetEthStakingEthHistoryRedemptionHistory = Entry('eth-staking/eth/history/redemptionHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_eth_history_rewardshistory = sapiGetEthStakingEthHistoryRewardsHistory = Entry('eth-staking/eth/history/rewardsHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_eth_quota = sapiGetEthStakingEthQuota = Entry('eth-staking/eth/quota', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_eth_history_ratehistory = sapiGetEthStakingEthHistoryRateHistory = Entry('eth-staking/eth/history/rateHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_account = sapiGetEthStakingAccount = Entry('eth-staking/account', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_wbeth_history_wraphistory = sapiGetEthStakingWbethHistoryWrapHistory = Entry('eth-staking/wbeth/history/wrapHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_wbeth_history_unwraphistory = sapiGetEthStakingWbethHistoryUnwrapHistory = Entry('eth-staking/wbeth/history/unwrapHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_eth_staking_eth_history_wbethrewardshistory = sapiGetEthStakingEthHistoryWbethRewardsHistory = Entry('eth-staking/eth/history/wbethRewardsHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_sol_staking_sol_history_stakinghistory = sapiGetSolStakingSolHistoryStakingHistory = Entry('sol-staking/sol/history/stakingHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_sol_staking_sol_history_redemptionhistory = sapiGetSolStakingSolHistoryRedemptionHistory = Entry('sol-staking/sol/history/redemptionHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_sol_staking_sol_history_bnsolrewardshistory = sapiGetSolStakingSolHistoryBnsolRewardsHistory = Entry('sol-staking/sol/history/bnsolRewardsHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_sol_staking_sol_history_ratehistory = sapiGetSolStakingSolHistoryRateHistory = Entry('sol-staking/sol/history/rateHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_sol_staking_account = sapiGetSolStakingAccount = Entry('sol-staking/account', 'sapi', 'GET', {'cost': 15})
    sapi_get_sol_staking_sol_quota = sapiGetSolStakingSolQuota = Entry('sol-staking/sol/quota', 'sapi', 'GET', {'cost': 15})
    sapi_get_mining_pub_algolist = sapiGetMiningPubAlgoList = Entry('mining/pub/algoList', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_mining_pub_coinlist = sapiGetMiningPubCoinList = Entry('mining/pub/coinList', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_mining_worker_detail = sapiGetMiningWorkerDetail = Entry('mining/worker/detail', 'sapi', 'GET', {'cost': 0.5})
    sapi_get_mining_worker_list = sapiGetMiningWorkerList = Entry('mining/worker/list', 'sapi', 'GET', {'cost': 0.5})
    sapi_get_mining_payment_list = sapiGetMiningPaymentList = Entry('mining/payment/list', 'sapi', 'GET', {'cost': 0.5})
    sapi_get_mining_statistics_user_status = sapiGetMiningStatisticsUserStatus = Entry('mining/statistics/user/status', 'sapi', 'GET', {'cost': 0.5})
    sapi_get_mining_statistics_user_list = sapiGetMiningStatisticsUserList = Entry('mining/statistics/user/list', 'sapi', 'GET', {'cost': 0.5})
    sapi_get_mining_payment_uid = sapiGetMiningPaymentUid = Entry('mining/payment/uid', 'sapi', 'GET', {'cost': 0.5})
    sapi_get_bswap_pools = sapiGetBswapPools = Entry('bswap/pools', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_bswap_liquidity = sapiGetBswapLiquidity = Entry('bswap/liquidity', 'sapi', 'GET', {'cost': 0.1, 'noPoolId': 1})
    sapi_get_bswap_liquidityops = sapiGetBswapLiquidityOps = Entry('bswap/liquidityOps', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_bswap_quote = sapiGetBswapQuote = Entry('bswap/quote', 'sapi', 'GET', {'cost': 1.00005})
    sapi_get_bswap_swap = sapiGetBswapSwap = Entry('bswap/swap', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_bswap_poolconfigure = sapiGetBswapPoolConfigure = Entry('bswap/poolConfigure', 'sapi', 'GET', {'cost': 1.00005})
    sapi_get_bswap_addliquiditypreview = sapiGetBswapAddLiquidityPreview = Entry('bswap/addLiquidityPreview', 'sapi', 'GET', {'cost': 1.00005})
    sapi_get_bswap_removeliquiditypreview = sapiGetBswapRemoveLiquidityPreview = Entry('bswap/removeLiquidityPreview', 'sapi', 'GET', {'cost': 1.00005})
    sapi_get_bswap_unclaimedrewards = sapiGetBswapUnclaimedRewards = Entry('bswap/unclaimedRewards', 'sapi', 'GET', {'cost': 6.667})
    sapi_get_bswap_claimedhistory = sapiGetBswapClaimedHistory = Entry('bswap/claimedHistory', 'sapi', 'GET', {'cost': 6.667})
    sapi_get_blvt_tokeninfo = sapiGetBlvtTokenInfo = Entry('blvt/tokenInfo', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_blvt_subscribe_record = sapiGetBlvtSubscribeRecord = Entry('blvt/subscribe/record', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_blvt_redeem_record = sapiGetBlvtRedeemRecord = Entry('blvt/redeem/record', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_blvt_userlimit = sapiGetBlvtUserLimit = Entry('blvt/userLimit', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_apireferral_ifnewuser = sapiGetApiReferralIfNewUser = Entry('apiReferral/ifNewUser', 'sapi', 'GET', {'cost': 1})
    sapi_get_apireferral_customization = sapiGetApiReferralCustomization = Entry('apiReferral/customization', 'sapi', 'GET', {'cost': 1})
    sapi_get_apireferral_usercustomization = sapiGetApiReferralUserCustomization = Entry('apiReferral/userCustomization', 'sapi', 'GET', {'cost': 1})
    sapi_get_apireferral_rebate_recentrecord = sapiGetApiReferralRebateRecentRecord = Entry('apiReferral/rebate/recentRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_apireferral_rebate_historicalrecord = sapiGetApiReferralRebateHistoricalRecord = Entry('apiReferral/rebate/historicalRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_apireferral_kickback_recentrecord = sapiGetApiReferralKickbackRecentRecord = Entry('apiReferral/kickback/recentRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_apireferral_kickback_historicalrecord = sapiGetApiReferralKickbackHistoricalRecord = Entry('apiReferral/kickback/historicalRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccountapi = sapiGetBrokerSubAccountApi = Entry('broker/subAccountApi', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccount = sapiGetBrokerSubAccount = Entry('broker/subAccount', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccountapi_commission_futures = sapiGetBrokerSubAccountApiCommissionFutures = Entry('broker/subAccountApi/commission/futures', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccountapi_commission_coinfutures = sapiGetBrokerSubAccountApiCommissionCoinFutures = Entry('broker/subAccountApi/commission/coinFutures', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_info = sapiGetBrokerInfo = Entry('broker/info', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_transfer = sapiGetBrokerTransfer = Entry('broker/transfer', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_transfer_futures = sapiGetBrokerTransferFutures = Entry('broker/transfer/futures', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_rebate_recentrecord = sapiGetBrokerRebateRecentRecord = Entry('broker/rebate/recentRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_rebate_historicalrecord = sapiGetBrokerRebateHistoricalRecord = Entry('broker/rebate/historicalRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccount_bnbburn_status = sapiGetBrokerSubAccountBnbBurnStatus = Entry('broker/subAccount/bnbBurn/status', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccount_deposithist = sapiGetBrokerSubAccountDepositHist = Entry('broker/subAccount/depositHist', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccount_spotsummary = sapiGetBrokerSubAccountSpotSummary = Entry('broker/subAccount/spotSummary', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccount_marginsummary = sapiGetBrokerSubAccountMarginSummary = Entry('broker/subAccount/marginSummary', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccount_futuressummary = sapiGetBrokerSubAccountFuturesSummary = Entry('broker/subAccount/futuresSummary', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_rebate_futures_recentrecord = sapiGetBrokerRebateFuturesRecentRecord = Entry('broker/rebate/futures/recentRecord', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_subaccountapi_iprestriction = sapiGetBrokerSubAccountApiIpRestriction = Entry('broker/subAccountApi/ipRestriction', 'sapi', 'GET', {'cost': 1})
    sapi_get_broker_universaltransfer = sapiGetBrokerUniversalTransfer = Entry('broker/universalTransfer', 'sapi', 'GET', {'cost': 1})
    sapi_get_account_apirestrictions = sapiGetAccountApiRestrictions = Entry('account/apiRestrictions', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_c2c_ordermatch_listuserorderhistory = sapiGetC2cOrderMatchListUserOrderHistory = Entry('c2c/orderMatch/listUserOrderHistory', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_nft_history_transactions = sapiGetNftHistoryTransactions = Entry('nft/history/transactions', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_nft_history_deposit = sapiGetNftHistoryDeposit = Entry('nft/history/deposit', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_nft_history_withdraw = sapiGetNftHistoryWithdraw = Entry('nft/history/withdraw', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_nft_user_getasset = sapiGetNftUserGetAsset = Entry('nft/user/getAsset', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_pay_transactions = sapiGetPayTransactions = Entry('pay/transactions', 'sapi', 'GET', {'cost': 20.001})
    sapi_get_giftcard_verify = sapiGetGiftcardVerify = Entry('giftcard/verify', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_giftcard_cryptography_rsa_public_key = sapiGetGiftcardCryptographyRsaPublicKey = Entry('giftcard/cryptography/rsa-public-key', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_giftcard_buycode_token_limit = sapiGetGiftcardBuyCodeTokenLimit = Entry('giftcard/buyCode/token-limit', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_algo_spot_openorders = sapiGetAlgoSpotOpenOrders = Entry('algo/spot/openOrders', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_algo_spot_historicalorders = sapiGetAlgoSpotHistoricalOrders = Entry('algo/spot/historicalOrders', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_algo_spot_suborders = sapiGetAlgoSpotSubOrders = Entry('algo/spot/subOrders', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_algo_futures_openorders = sapiGetAlgoFuturesOpenOrders = Entry('algo/futures/openOrders', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_algo_futures_historicalorders = sapiGetAlgoFuturesHistoricalOrders = Entry('algo/futures/historicalOrders', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_algo_futures_suborders = sapiGetAlgoFuturesSubOrders = Entry('algo/futures/subOrders', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_portfolio_account = sapiGetPortfolioAccount = Entry('portfolio/account', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_portfolio_collateralrate = sapiGetPortfolioCollateralRate = Entry('portfolio/collateralRate', 'sapi', 'GET', {'cost': 5})
    sapi_get_portfolio_pmloan = sapiGetPortfolioPmLoan = Entry('portfolio/pmLoan', 'sapi', 'GET', {'cost': 3.3335})
    sapi_get_portfolio_interest_history = sapiGetPortfolioInterestHistory = Entry('portfolio/interest-history', 'sapi', 'GET', {'cost': 0.6667})
    sapi_get_portfolio_asset_index_price = sapiGetPortfolioAssetIndexPrice = Entry('portfolio/asset-index-price', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_portfolio_repay_futures_switch = sapiGetPortfolioRepayFuturesSwitch = Entry('portfolio/repay-futures-switch', 'sapi', 'GET', {'cost': 3})
    sapi_get_portfolio_margin_asset_leverage = sapiGetPortfolioMarginAssetLeverage = Entry('portfolio/margin-asset-leverage', 'sapi', 'GET', {'cost': 5})
    sapi_get_portfolio_balance = sapiGetPortfolioBalance = Entry('portfolio/balance', 'sapi', 'GET', {'cost': 2})
    sapi_get_portfolio_negative_balance_exchange_record = sapiGetPortfolioNegativeBalanceExchangeRecord = Entry('portfolio/negative-balance-exchange-record', 'sapi', 'GET', {'cost': 2})
    sapi_get_portfolio_pmloan_history = sapiGetPortfolioPmloanHistory = Entry('portfolio/pmloan-history', 'sapi', 'GET', {'cost': 5})
    sapi_get_staking_productlist = sapiGetStakingProductList = Entry('staking/productList', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_staking_position = sapiGetStakingPosition = Entry('staking/position', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_staking_stakingrecord = sapiGetStakingStakingRecord = Entry('staking/stakingRecord', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_staking_personalleftquota = sapiGetStakingPersonalLeftQuota = Entry('staking/personalLeftQuota', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_target_asset_list = sapiGetLendingAutoInvestTargetAssetList = Entry('lending/auto-invest/target-asset/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_target_asset_roi_list = sapiGetLendingAutoInvestTargetAssetRoiList = Entry('lending/auto-invest/target-asset/roi/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_all_asset = sapiGetLendingAutoInvestAllAsset = Entry('lending/auto-invest/all/asset', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_source_asset_list = sapiGetLendingAutoInvestSourceAssetList = Entry('lending/auto-invest/source-asset/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_plan_list = sapiGetLendingAutoInvestPlanList = Entry('lending/auto-invest/plan/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_plan_id = sapiGetLendingAutoInvestPlanId = Entry('lending/auto-invest/plan/id', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_history_list = sapiGetLendingAutoInvestHistoryList = Entry('lending/auto-invest/history/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_index_info = sapiGetLendingAutoInvestIndexInfo = Entry('lending/auto-invest/index/info', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_index_user_summary = sapiGetLendingAutoInvestIndexUserSummary = Entry('lending/auto-invest/index/user-summary', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_one_off_status = sapiGetLendingAutoInvestOneOffStatus = Entry('lending/auto-invest/one-off/status', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_redeem_history = sapiGetLendingAutoInvestRedeemHistory = Entry('lending/auto-invest/redeem/history', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_lending_auto_invest_rebalance_history = sapiGetLendingAutoInvestRebalanceHistory = Entry('lending/auto-invest/rebalance/history', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_simple_earn_flexible_list = sapiGetSimpleEarnFlexibleList = Entry('simple-earn/flexible/list', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_list = sapiGetSimpleEarnLockedList = Entry('simple-earn/locked/list', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_personalleftquota = sapiGetSimpleEarnFlexiblePersonalLeftQuota = Entry('simple-earn/flexible/personalLeftQuota', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_personalleftquota = sapiGetSimpleEarnLockedPersonalLeftQuota = Entry('simple-earn/locked/personalLeftQuota', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_subscriptionpreview = sapiGetSimpleEarnFlexibleSubscriptionPreview = Entry('simple-earn/flexible/subscriptionPreview', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_subscriptionpreview = sapiGetSimpleEarnLockedSubscriptionPreview = Entry('simple-earn/locked/subscriptionPreview', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_history_ratehistory = sapiGetSimpleEarnFlexibleHistoryRateHistory = Entry('simple-earn/flexible/history/rateHistory', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_position = sapiGetSimpleEarnFlexiblePosition = Entry('simple-earn/flexible/position', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_position = sapiGetSimpleEarnLockedPosition = Entry('simple-earn/locked/position', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_account = sapiGetSimpleEarnAccount = Entry('simple-earn/account', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_history_subscriptionrecord = sapiGetSimpleEarnFlexibleHistorySubscriptionRecord = Entry('simple-earn/flexible/history/subscriptionRecord', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_history_subscriptionrecord = sapiGetSimpleEarnLockedHistorySubscriptionRecord = Entry('simple-earn/locked/history/subscriptionRecord', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_history_redemptionrecord = sapiGetSimpleEarnFlexibleHistoryRedemptionRecord = Entry('simple-earn/flexible/history/redemptionRecord', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_history_redemptionrecord = sapiGetSimpleEarnLockedHistoryRedemptionRecord = Entry('simple-earn/locked/history/redemptionRecord', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_history_rewardsrecord = sapiGetSimpleEarnFlexibleHistoryRewardsRecord = Entry('simple-earn/flexible/history/rewardsRecord', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_locked_history_rewardsrecord = sapiGetSimpleEarnLockedHistoryRewardsRecord = Entry('simple-earn/locked/history/rewardsRecord', 'sapi', 'GET', {'cost': 15})
    sapi_get_simple_earn_flexible_history_collateralrecord = sapiGetSimpleEarnFlexibleHistoryCollateralRecord = Entry('simple-earn/flexible/history/collateralRecord', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_dci_product_list = sapiGetDciProductList = Entry('dci/product/list', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_dci_product_positions = sapiGetDciProductPositions = Entry('dci/product/positions', 'sapi', 'GET', {'cost': 0.1})
    sapi_get_dci_product_accounts = sapiGetDciProductAccounts = Entry('dci/product/accounts', 'sapi', 'GET', {'cost': 0.1})
    sapi_post_asset_dust = sapiPostAssetDust = Entry('asset/dust', 'sapi', 'POST', {'cost': 0.06667})
    sapi_post_asset_dust_btc = sapiPostAssetDustBtc = Entry('asset/dust-btc', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_asset_transfer = sapiPostAssetTransfer = Entry('asset/transfer', 'sapi', 'POST', {'cost': 6.0003})
    sapi_post_asset_get_funding_asset = sapiPostAssetGetFundingAsset = Entry('asset/get-funding-asset', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_asset_convert_transfer = sapiPostAssetConvertTransfer = Entry('asset/convert-transfer', 'sapi', 'POST', {'cost': 0.033335})
    sapi_post_account_disablefastwithdrawswitch = sapiPostAccountDisableFastWithdrawSwitch = Entry('account/disableFastWithdrawSwitch', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_account_enablefastwithdrawswitch = sapiPostAccountEnableFastWithdrawSwitch = Entry('account/enableFastWithdrawSwitch', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_capital_withdraw_apply = sapiPostCapitalWithdrawApply = Entry('capital/withdraw/apply', 'sapi', 'POST', {'cost': 4.0002})
    sapi_post_capital_contract_convertible_coins = sapiPostCapitalContractConvertibleCoins = Entry('capital/contract/convertible-coins', 'sapi', 'POST', {'cost': 4.0002})
    sapi_post_capital_deposit_credit_apply = sapiPostCapitalDepositCreditApply = Entry('capital/deposit/credit-apply', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_margin_borrow_repay = sapiPostMarginBorrowRepay = Entry('margin/borrow-repay', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_margin_transfer = sapiPostMarginTransfer = Entry('margin/transfer', 'sapi', 'POST', {'cost': 4.0002})
    sapi_post_margin_loan = sapiPostMarginLoan = Entry('margin/loan', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_margin_repay = sapiPostMarginRepay = Entry('margin/repay', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_margin_order = sapiPostMarginOrder = Entry('margin/order', 'sapi', 'POST', {'cost': 0.040002})
    sapi_post_margin_order_oco = sapiPostMarginOrderOco = Entry('margin/order/oco', 'sapi', 'POST', {'cost': 0.040002})
    sapi_post_margin_dust = sapiPostMarginDust = Entry('margin/dust', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_margin_exchange_small_liability = sapiPostMarginExchangeSmallLiability = Entry('margin/exchange-small-liability', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_margin_isolated_transfer = sapiPostMarginIsolatedTransfer = Entry('margin/isolated/transfer', 'sapi', 'POST', {'cost': 4.0002})
    sapi_post_margin_isolated_account = sapiPostMarginIsolatedAccount = Entry('margin/isolated/account', 'sapi', 'POST', {'cost': 2.0001})
    sapi_post_margin_max_leverage = sapiPostMarginMaxLeverage = Entry('margin/max-leverage', 'sapi', 'POST', {'cost': 300})
    sapi_post_bnbburn = sapiPostBnbBurn = Entry('bnbBurn', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_virtualsubaccount = sapiPostSubAccountVirtualSubAccount = Entry('sub-account/virtualSubAccount', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_margin_transfer = sapiPostSubAccountMarginTransfer = Entry('sub-account/margin/transfer', 'sapi', 'POST', {'cost': 4.0002})
    sapi_post_sub_account_margin_enable = sapiPostSubAccountMarginEnable = Entry('sub-account/margin/enable', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_futures_enable = sapiPostSubAccountFuturesEnable = Entry('sub-account/futures/enable', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_futures_transfer = sapiPostSubAccountFuturesTransfer = Entry('sub-account/futures/transfer', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_futures_internaltransfer = sapiPostSubAccountFuturesInternalTransfer = Entry('sub-account/futures/internalTransfer', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_transfer_subtosub = sapiPostSubAccountTransferSubToSub = Entry('sub-account/transfer/subToSub', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_transfer_subtomaster = sapiPostSubAccountTransferSubToMaster = Entry('sub-account/transfer/subToMaster', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_universaltransfer = sapiPostSubAccountUniversalTransfer = Entry('sub-account/universalTransfer', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_sub_account_options_enable = sapiPostSubAccountOptionsEnable = Entry('sub-account/options/enable', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_managed_subaccount_deposit = sapiPostManagedSubaccountDeposit = Entry('managed-subaccount/deposit', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_managed_subaccount_withdraw = sapiPostManagedSubaccountWithdraw = Entry('managed-subaccount/withdraw', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_userdatastream = sapiPostUserDataStream = Entry('userDataStream', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_userdatastream_isolated = sapiPostUserDataStreamIsolated = Entry('userDataStream/isolated', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_futures_transfer = sapiPostFuturesTransfer = Entry('futures/transfer', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_customizedfixed_purchase = sapiPostLendingCustomizedFixedPurchase = Entry('lending/customizedFixed/purchase', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_daily_purchase = sapiPostLendingDailyPurchase = Entry('lending/daily/purchase', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_daily_redeem = sapiPostLendingDailyRedeem = Entry('lending/daily/redeem', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_bswap_liquidityadd = sapiPostBswapLiquidityAdd = Entry('bswap/liquidityAdd', 'sapi', 'POST', {'cost': 60})
    sapi_post_bswap_liquidityremove = sapiPostBswapLiquidityRemove = Entry('bswap/liquidityRemove', 'sapi', 'POST', {'cost': 60})
    sapi_post_bswap_swap = sapiPostBswapSwap = Entry('bswap/swap', 'sapi', 'POST', {'cost': 60})
    sapi_post_bswap_claimrewards = sapiPostBswapClaimRewards = Entry('bswap/claimRewards', 'sapi', 'POST', {'cost': 6.667})
    sapi_post_blvt_subscribe = sapiPostBlvtSubscribe = Entry('blvt/subscribe', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_blvt_redeem = sapiPostBlvtRedeem = Entry('blvt/redeem', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_apireferral_customization = sapiPostApiReferralCustomization = Entry('apiReferral/customization', 'sapi', 'POST', {'cost': 1})
    sapi_post_apireferral_usercustomization = sapiPostApiReferralUserCustomization = Entry('apiReferral/userCustomization', 'sapi', 'POST', {'cost': 1})
    sapi_post_apireferral_rebate_historicalrecord = sapiPostApiReferralRebateHistoricalRecord = Entry('apiReferral/rebate/historicalRecord', 'sapi', 'POST', {'cost': 1})
    sapi_post_apireferral_kickback_historicalrecord = sapiPostApiReferralKickbackHistoricalRecord = Entry('apiReferral/kickback/historicalRecord', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccount = sapiPostBrokerSubAccount = Entry('broker/subAccount', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccount_margin = sapiPostBrokerSubAccountMargin = Entry('broker/subAccount/margin', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccount_futures = sapiPostBrokerSubAccountFutures = Entry('broker/subAccount/futures', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi = sapiPostBrokerSubAccountApi = Entry('broker/subAccountApi', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_permission = sapiPostBrokerSubAccountApiPermission = Entry('broker/subAccountApi/permission', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_commission = sapiPostBrokerSubAccountApiCommission = Entry('broker/subAccountApi/commission', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_commission_futures = sapiPostBrokerSubAccountApiCommissionFutures = Entry('broker/subAccountApi/commission/futures', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_commission_coinfutures = sapiPostBrokerSubAccountApiCommissionCoinFutures = Entry('broker/subAccountApi/commission/coinFutures', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_transfer = sapiPostBrokerTransfer = Entry('broker/transfer', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_transfer_futures = sapiPostBrokerTransferFutures = Entry('broker/transfer/futures', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_rebate_historicalrecord = sapiPostBrokerRebateHistoricalRecord = Entry('broker/rebate/historicalRecord', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccount_bnbburn_spot = sapiPostBrokerSubAccountBnbBurnSpot = Entry('broker/subAccount/bnbBurn/spot', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccount_bnbburn_margininterest = sapiPostBrokerSubAccountBnbBurnMarginInterest = Entry('broker/subAccount/bnbBurn/marginInterest', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccount_blvt = sapiPostBrokerSubAccountBlvt = Entry('broker/subAccount/blvt', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_iprestriction = sapiPostBrokerSubAccountApiIpRestriction = Entry('broker/subAccountApi/ipRestriction', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_iprestriction_iplist = sapiPostBrokerSubAccountApiIpRestrictionIpList = Entry('broker/subAccountApi/ipRestriction/ipList', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_universaltransfer = sapiPostBrokerUniversalTransfer = Entry('broker/universalTransfer', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_permission_universaltransfer = sapiPostBrokerSubAccountApiPermissionUniversalTransfer = Entry('broker/subAccountApi/permission/universalTransfer', 'sapi', 'POST', {'cost': 1})
    sapi_post_broker_subaccountapi_permission_vanillaoptions = sapiPostBrokerSubAccountApiPermissionVanillaOptions = Entry('broker/subAccountApi/permission/vanillaOptions', 'sapi', 'POST', {'cost': 1})
    sapi_post_giftcard_createcode = sapiPostGiftcardCreateCode = Entry('giftcard/createCode', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_giftcard_redeemcode = sapiPostGiftcardRedeemCode = Entry('giftcard/redeemCode', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_giftcard_buycode = sapiPostGiftcardBuyCode = Entry('giftcard/buyCode', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_algo_spot_newordertwap = sapiPostAlgoSpotNewOrderTwap = Entry('algo/spot/newOrderTwap', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_algo_futures_newordervp = sapiPostAlgoFuturesNewOrderVp = Entry('algo/futures/newOrderVp', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_algo_futures_newordertwap = sapiPostAlgoFuturesNewOrderTwap = Entry('algo/futures/newOrderTwap', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_staking_purchase = sapiPostStakingPurchase = Entry('staking/purchase', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_staking_redeem = sapiPostStakingRedeem = Entry('staking/redeem', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_staking_setautostaking = sapiPostStakingSetAutoStaking = Entry('staking/setAutoStaking', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_eth_staking_eth_stake = sapiPostEthStakingEthStake = Entry('eth-staking/eth/stake', 'sapi', 'POST', {'cost': 15})
    sapi_post_eth_staking_eth_redeem = sapiPostEthStakingEthRedeem = Entry('eth-staking/eth/redeem', 'sapi', 'POST', {'cost': 15})
    sapi_post_eth_staking_wbeth_wrap = sapiPostEthStakingWbethWrap = Entry('eth-staking/wbeth/wrap', 'sapi', 'POST', {'cost': 15})
    sapi_post_sol_staking_sol_stake = sapiPostSolStakingSolStake = Entry('sol-staking/sol/stake', 'sapi', 'POST', {'cost': 15})
    sapi_post_sol_staking_sol_redeem = sapiPostSolStakingSolRedeem = Entry('sol-staking/sol/redeem', 'sapi', 'POST', {'cost': 15})
    sapi_post_mining_hash_transfer_config = sapiPostMiningHashTransferConfig = Entry('mining/hash-transfer/config', 'sapi', 'POST', {'cost': 0.5})
    sapi_post_mining_hash_transfer_config_cancel = sapiPostMiningHashTransferConfigCancel = Entry('mining/hash-transfer/config/cancel', 'sapi', 'POST', {'cost': 0.5})
    sapi_post_portfolio_repay = sapiPostPortfolioRepay = Entry('portfolio/repay', 'sapi', 'POST', {'cost': 20.001})
    sapi_post_loan_vip_renew = sapiPostLoanVipRenew = Entry('loan/vip/renew', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_vip_borrow = sapiPostLoanVipBorrow = Entry('loan/vip/borrow', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_borrow = sapiPostLoanBorrow = Entry('loan/borrow', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_repay = sapiPostLoanRepay = Entry('loan/repay', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_adjust_ltv = sapiPostLoanAdjustLtv = Entry('loan/adjust/ltv', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_customize_margin_call = sapiPostLoanCustomizeMarginCall = Entry('loan/customize/margin_call', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_flexible_repay = sapiPostLoanFlexibleRepay = Entry('loan/flexible/repay', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_flexible_adjust_ltv = sapiPostLoanFlexibleAdjustLtv = Entry('loan/flexible/adjust/ltv', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_loan_vip_repay = sapiPostLoanVipRepay = Entry('loan/vip/repay', 'sapi', 'POST', {'cost': 40.002})
    sapi_post_convert_getquote = sapiPostConvertGetQuote = Entry('convert/getQuote', 'sapi', 'POST', {'cost': 1.3334})
    sapi_post_convert_acceptquote = sapiPostConvertAcceptQuote = Entry('convert/acceptQuote', 'sapi', 'POST', {'cost': 3.3335})
    sapi_post_convert_limit_placeorder = sapiPostConvertLimitPlaceOrder = Entry('convert/limit/placeOrder', 'sapi', 'POST', {'cost': 3.3335})
    sapi_post_convert_limit_cancelorder = sapiPostConvertLimitCancelOrder = Entry('convert/limit/cancelOrder', 'sapi', 'POST', {'cost': 1.3334})
    sapi_post_portfolio_auto_collection = sapiPostPortfolioAutoCollection = Entry('portfolio/auto-collection', 'sapi', 'POST', {'cost': 150})
    sapi_post_portfolio_asset_collection = sapiPostPortfolioAssetCollection = Entry('portfolio/asset-collection', 'sapi', 'POST', {'cost': 6})
    sapi_post_portfolio_bnb_transfer = sapiPostPortfolioBnbTransfer = Entry('portfolio/bnb-transfer', 'sapi', 'POST', {'cost': 150})
    sapi_post_portfolio_repay_futures_switch = sapiPostPortfolioRepayFuturesSwitch = Entry('portfolio/repay-futures-switch', 'sapi', 'POST', {'cost': 150})
    sapi_post_portfolio_repay_futures_negative_balance = sapiPostPortfolioRepayFuturesNegativeBalance = Entry('portfolio/repay-futures-negative-balance', 'sapi', 'POST', {'cost': 150})
    sapi_post_portfolio_mint = sapiPostPortfolioMint = Entry('portfolio/mint', 'sapi', 'POST', {'cost': 20})
    sapi_post_portfolio_redeem = sapiPostPortfolioRedeem = Entry('portfolio/redeem', 'sapi', 'POST', {'cost': 20})
    sapi_post_lending_auto_invest_plan_add = sapiPostLendingAutoInvestPlanAdd = Entry('lending/auto-invest/plan/add', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_auto_invest_plan_edit = sapiPostLendingAutoInvestPlanEdit = Entry('lending/auto-invest/plan/edit', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_auto_invest_plan_edit_status = sapiPostLendingAutoInvestPlanEditStatus = Entry('lending/auto-invest/plan/edit-status', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_auto_invest_one_off = sapiPostLendingAutoInvestOneOff = Entry('lending/auto-invest/one-off', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_lending_auto_invest_redeem = sapiPostLendingAutoInvestRedeem = Entry('lending/auto-invest/redeem', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_simple_earn_flexible_subscribe = sapiPostSimpleEarnFlexibleSubscribe = Entry('simple-earn/flexible/subscribe', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_simple_earn_locked_subscribe = sapiPostSimpleEarnLockedSubscribe = Entry('simple-earn/locked/subscribe', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_simple_earn_flexible_redeem = sapiPostSimpleEarnFlexibleRedeem = Entry('simple-earn/flexible/redeem', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_simple_earn_locked_redeem = sapiPostSimpleEarnLockedRedeem = Entry('simple-earn/locked/redeem', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_simple_earn_flexible_setautosubscribe = sapiPostSimpleEarnFlexibleSetAutoSubscribe = Entry('simple-earn/flexible/setAutoSubscribe', 'sapi', 'POST', {'cost': 15})
    sapi_post_simple_earn_locked_setautosubscribe = sapiPostSimpleEarnLockedSetAutoSubscribe = Entry('simple-earn/locked/setAutoSubscribe', 'sapi', 'POST', {'cost': 15})
    sapi_post_simple_earn_locked_setredeemoption = sapiPostSimpleEarnLockedSetRedeemOption = Entry('simple-earn/locked/setRedeemOption', 'sapi', 'POST', {'cost': 5})
    sapi_post_dci_product_subscribe = sapiPostDciProductSubscribe = Entry('dci/product/subscribe', 'sapi', 'POST', {'cost': 0.1})
    sapi_post_dci_product_auto_compound_edit = sapiPostDciProductAutoCompoundEdit = Entry('dci/product/auto_compound/edit', 'sapi', 'POST', {'cost': 0.1})
    sapi_put_userdatastream = sapiPutUserDataStream = Entry('userDataStream', 'sapi', 'PUT', {'cost': 0.1})
    sapi_put_userdatastream_isolated = sapiPutUserDataStreamIsolated = Entry('userDataStream/isolated', 'sapi', 'PUT', {'cost': 0.1})
    sapi_delete_margin_openorders = sapiDeleteMarginOpenOrders = Entry('margin/openOrders', 'sapi', 'DELETE', {'cost': 0.1})
    sapi_delete_margin_order = sapiDeleteMarginOrder = Entry('margin/order', 'sapi', 'DELETE', {'cost': 0.006667})
    sapi_delete_margin_orderlist = sapiDeleteMarginOrderList = Entry('margin/orderList', 'sapi', 'DELETE', {'cost': 0.006667})
    sapi_delete_margin_isolated_account = sapiDeleteMarginIsolatedAccount = Entry('margin/isolated/account', 'sapi', 'DELETE', {'cost': 2.0001})
    sapi_delete_userdatastream = sapiDeleteUserDataStream = Entry('userDataStream', 'sapi', 'DELETE', {'cost': 0.1})
    sapi_delete_userdatastream_isolated = sapiDeleteUserDataStreamIsolated = Entry('userDataStream/isolated', 'sapi', 'DELETE', {'cost': 0.1})
    sapi_delete_broker_subaccountapi = sapiDeleteBrokerSubAccountApi = Entry('broker/subAccountApi', 'sapi', 'DELETE', {'cost': 1})
    sapi_delete_broker_subaccountapi_iprestriction_iplist = sapiDeleteBrokerSubAccountApiIpRestrictionIpList = Entry('broker/subAccountApi/ipRestriction/ipList', 'sapi', 'DELETE', {'cost': 1})
    sapi_delete_algo_spot_order = sapiDeleteAlgoSpotOrder = Entry('algo/spot/order', 'sapi', 'DELETE', {'cost': 0.1})
    sapi_delete_algo_futures_order = sapiDeleteAlgoFuturesOrder = Entry('algo/futures/order', 'sapi', 'DELETE', {'cost': 0.1})
    sapi_delete_sub_account_subaccountapi_iprestriction_iplist = sapiDeleteSubAccountSubAccountApiIpRestrictionIpList = Entry('sub-account/subAccountApi/ipRestriction/ipList', 'sapi', 'DELETE', {'cost': 20.001})
    sapiv2_get_eth_staking_account = sapiV2GetEthStakingAccount = Entry('eth-staking/account', 'sapiV2', 'GET', {'cost': 15})
    sapiv2_get_sub_account_futures_account = sapiV2GetSubAccountFuturesAccount = Entry('sub-account/futures/account', 'sapiV2', 'GET', {'cost': 0.1})
    sapiv2_get_sub_account_futures_accountsummary = sapiV2GetSubAccountFuturesAccountSummary = Entry('sub-account/futures/accountSummary', 'sapiV2', 'GET', {'cost': 1})
    sapiv2_get_sub_account_futures_positionrisk = sapiV2GetSubAccountFuturesPositionRisk = Entry('sub-account/futures/positionRisk', 'sapiV2', 'GET', {'cost': 0.1})
    sapiv2_get_loan_flexible_ongoing_orders = sapiV2GetLoanFlexibleOngoingOrders = Entry('loan/flexible/ongoing/orders', 'sapiV2', 'GET', {'cost': 30})
    sapiv2_get_loan_flexible_borrow_history = sapiV2GetLoanFlexibleBorrowHistory = Entry('loan/flexible/borrow/history', 'sapiV2', 'GET', {'cost': 40})
    sapiv2_get_loan_flexible_repay_history = sapiV2GetLoanFlexibleRepayHistory = Entry('loan/flexible/repay/history', 'sapiV2', 'GET', {'cost': 40})
    sapiv2_get_loan_flexible_ltv_adjustment_history = sapiV2GetLoanFlexibleLtvAdjustmentHistory = Entry('loan/flexible/ltv/adjustment/history', 'sapiV2', 'GET', {'cost': 40})
    sapiv2_get_loan_flexible_loanable_data = sapiV2GetLoanFlexibleLoanableData = Entry('loan/flexible/loanable/data', 'sapiV2', 'GET', {'cost': 40})
    sapiv2_get_loan_flexible_collateral_data = sapiV2GetLoanFlexibleCollateralData = Entry('loan/flexible/collateral/data', 'sapiV2', 'GET', {'cost': 40})
    sapiv2_get_portfolio_account = sapiV2GetPortfolioAccount = Entry('portfolio/account', 'sapiV2', 'GET', {'cost': 2})
    sapiv2_post_eth_staking_eth_stake = sapiV2PostEthStakingEthStake = Entry('eth-staking/eth/stake', 'sapiV2', 'POST', {'cost': 15})
    sapiv2_post_sub_account_subaccountapi_iprestriction = sapiV2PostSubAccountSubAccountApiIpRestriction = Entry('sub-account/subAccountApi/ipRestriction', 'sapiV2', 'POST', {'cost': 20.001})
    sapiv2_post_loan_flexible_borrow = sapiV2PostLoanFlexibleBorrow = Entry('loan/flexible/borrow', 'sapiV2', 'POST', {'cost': 40.002})
    sapiv2_post_loan_flexible_repay = sapiV2PostLoanFlexibleRepay = Entry('loan/flexible/repay', 'sapiV2', 'POST', {'cost': 40.002})
    sapiv2_post_loan_flexible_adjust_ltv = sapiV2PostLoanFlexibleAdjustLtv = Entry('loan/flexible/adjust/ltv', 'sapiV2', 'POST', {'cost': 40.002})
    sapiv3_get_sub_account_assets = sapiV3GetSubAccountAssets = Entry('sub-account/assets', 'sapiV3', 'GET', {'cost': 0.40002})
    sapiv3_post_asset_getuserasset = sapiV3PostAssetGetUserAsset = Entry('asset/getUserAsset', 'sapiV3', 'POST', {'cost': 0.5})
    sapiv4_get_sub_account_assets = sapiV4GetSubAccountAssets = Entry('sub-account/assets', 'sapiV4', 'GET', {'cost': 0.40002})
    dapipublic_get_ping = dapiPublicGetPing = Entry('ping', 'dapiPublic', 'GET', {'cost': 1})
    dapipublic_get_time = dapiPublicGetTime = Entry('time', 'dapiPublic', 'GET', {'cost': 1})
    dapipublic_get_exchangeinfo = dapiPublicGetExchangeInfo = Entry('exchangeInfo', 'dapiPublic', 'GET', {'cost': 1})
    dapipublic_get_depth = dapiPublicGetDepth = Entry('depth', 'dapiPublic', 'GET', {'cost': 2, 'byLimit': [[50, 2], [100, 5], [500, 10], [1000, 20]]})
    dapipublic_get_trades = dapiPublicGetTrades = Entry('trades', 'dapiPublic', 'GET', {'cost': 5})
    dapipublic_get_historicaltrades = dapiPublicGetHistoricalTrades = Entry('historicalTrades', 'dapiPublic', 'GET', {'cost': 20})
    dapipublic_get_aggtrades = dapiPublicGetAggTrades = Entry('aggTrades', 'dapiPublic', 'GET', {'cost': 20})
    dapipublic_get_premiumindex = dapiPublicGetPremiumIndex = Entry('premiumIndex', 'dapiPublic', 'GET', {'cost': 10})
    dapipublic_get_fundingrate = dapiPublicGetFundingRate = Entry('fundingRate', 'dapiPublic', 'GET', {'cost': 1})
    dapipublic_get_klines = dapiPublicGetKlines = Entry('klines', 'dapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    dapipublic_get_continuousklines = dapiPublicGetContinuousKlines = Entry('continuousKlines', 'dapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    dapipublic_get_indexpriceklines = dapiPublicGetIndexPriceKlines = Entry('indexPriceKlines', 'dapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    dapipublic_get_markpriceklines = dapiPublicGetMarkPriceKlines = Entry('markPriceKlines', 'dapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    dapipublic_get_premiumindexklines = dapiPublicGetPremiumIndexKlines = Entry('premiumIndexKlines', 'dapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    dapipublic_get_ticker_24hr = dapiPublicGetTicker24hr = Entry('ticker/24hr', 'dapiPublic', 'GET', {'cost': 1, 'noSymbol': 40})
    dapipublic_get_ticker_price = dapiPublicGetTickerPrice = Entry('ticker/price', 'dapiPublic', 'GET', {'cost': 1, 'noSymbol': 2})
    dapipublic_get_ticker_bookticker = dapiPublicGetTickerBookTicker = Entry('ticker/bookTicker', 'dapiPublic', 'GET', {'cost': 2, 'noSymbol': 5})
    dapipublic_get_constituents = dapiPublicGetConstituents = Entry('constituents', 'dapiPublic', 'GET', {'cost': 2})
    dapipublic_get_openinterest = dapiPublicGetOpenInterest = Entry('openInterest', 'dapiPublic', 'GET', {'cost': 1})
    dapipublic_get_fundinginfo = dapiPublicGetFundingInfo = Entry('fundingInfo', 'dapiPublic', 'GET', {'cost': 1})
    dapidata_get_delivery_price = dapiDataGetDeliveryPrice = Entry('delivery-price', 'dapiData', 'GET', {'cost': 1})
    dapidata_get_openinteresthist = dapiDataGetOpenInterestHist = Entry('openInterestHist', 'dapiData', 'GET', {'cost': 1})
    dapidata_get_toplongshortaccountratio = dapiDataGetTopLongShortAccountRatio = Entry('topLongShortAccountRatio', 'dapiData', 'GET', {'cost': 1})
    dapidata_get_toplongshortpositionratio = dapiDataGetTopLongShortPositionRatio = Entry('topLongShortPositionRatio', 'dapiData', 'GET', {'cost': 1})
    dapidata_get_globallongshortaccountratio = dapiDataGetGlobalLongShortAccountRatio = Entry('globalLongShortAccountRatio', 'dapiData', 'GET', {'cost': 1})
    dapidata_get_takerbuysellvol = dapiDataGetTakerBuySellVol = Entry('takerBuySellVol', 'dapiData', 'GET', {'cost': 1})
    dapidata_get_basis = dapiDataGetBasis = Entry('basis', 'dapiData', 'GET', {'cost': 1})
    dapiprivate_get_positionside_dual = dapiPrivateGetPositionSideDual = Entry('positionSide/dual', 'dapiPrivate', 'GET', {'cost': 30})
    dapiprivate_get_orderamendment = dapiPrivateGetOrderAmendment = Entry('orderAmendment', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_order = dapiPrivateGetOrder = Entry('order', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_openorder = dapiPrivateGetOpenOrder = Entry('openOrder', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_openorders = dapiPrivateGetOpenOrders = Entry('openOrders', 'dapiPrivate', 'GET', {'cost': 1, 'noSymbol': 5})
    dapiprivate_get_allorders = dapiPrivateGetAllOrders = Entry('allOrders', 'dapiPrivate', 'GET', {'cost': 20, 'noSymbol': 40})
    dapiprivate_get_balance = dapiPrivateGetBalance = Entry('balance', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_account = dapiPrivateGetAccount = Entry('account', 'dapiPrivate', 'GET', {'cost': 5})
    dapiprivate_get_positionmargin_history = dapiPrivateGetPositionMarginHistory = Entry('positionMargin/history', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_positionrisk = dapiPrivateGetPositionRisk = Entry('positionRisk', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_usertrades = dapiPrivateGetUserTrades = Entry('userTrades', 'dapiPrivate', 'GET', {'cost': 20, 'noSymbol': 40})
    dapiprivate_get_income = dapiPrivateGetIncome = Entry('income', 'dapiPrivate', 'GET', {'cost': 20})
    dapiprivate_get_leveragebracket = dapiPrivateGetLeverageBracket = Entry('leverageBracket', 'dapiPrivate', 'GET', {'cost': 1})
    dapiprivate_get_forceorders = dapiPrivateGetForceOrders = Entry('forceOrders', 'dapiPrivate', 'GET', {'cost': 20, 'noSymbol': 50})
    dapiprivate_get_adlquantile = dapiPrivateGetAdlQuantile = Entry('adlQuantile', 'dapiPrivate', 'GET', {'cost': 5})
    dapiprivate_get_commissionrate = dapiPrivateGetCommissionRate = Entry('commissionRate', 'dapiPrivate', 'GET', {'cost': 20})
    dapiprivate_get_income_asyn = dapiPrivateGetIncomeAsyn = Entry('income/asyn', 'dapiPrivate', 'GET', {'cost': 5})
    dapiprivate_get_income_asyn_id = dapiPrivateGetIncomeAsynId = Entry('income/asyn/id', 'dapiPrivate', 'GET', {'cost': 5})
    dapiprivate_get_trade_asyn = dapiPrivateGetTradeAsyn = Entry('trade/asyn', 'dapiPrivate', 'GET', {'cost': 0.5})
    dapiprivate_get_trade_asyn_id = dapiPrivateGetTradeAsynId = Entry('trade/asyn/id', 'dapiPrivate', 'GET', {'cost': 0.5})
    dapiprivate_get_order_asyn = dapiPrivateGetOrderAsyn = Entry('order/asyn', 'dapiPrivate', 'GET', {'cost': 0.5})
    dapiprivate_get_order_asyn_id = dapiPrivateGetOrderAsynId = Entry('order/asyn/id', 'dapiPrivate', 'GET', {'cost': 0.5})
    dapiprivate_get_pmexchangeinfo = dapiPrivateGetPmExchangeInfo = Entry('pmExchangeInfo', 'dapiPrivate', 'GET', {'cost': 0.5})
    dapiprivate_get_pmaccountinfo = dapiPrivateGetPmAccountInfo = Entry('pmAccountInfo', 'dapiPrivate', 'GET', {'cost': 0.5})
    dapiprivate_post_positionside_dual = dapiPrivatePostPositionSideDual = Entry('positionSide/dual', 'dapiPrivate', 'POST', {'cost': 1})
    dapiprivate_post_order = dapiPrivatePostOrder = Entry('order', 'dapiPrivate', 'POST', {'cost': 4})
    dapiprivate_post_batchorders = dapiPrivatePostBatchOrders = Entry('batchOrders', 'dapiPrivate', 'POST', {'cost': 5})
    dapiprivate_post_countdowncancelall = dapiPrivatePostCountdownCancelAll = Entry('countdownCancelAll', 'dapiPrivate', 'POST', {'cost': 10})
    dapiprivate_post_leverage = dapiPrivatePostLeverage = Entry('leverage', 'dapiPrivate', 'POST', {'cost': 1})
    dapiprivate_post_margintype = dapiPrivatePostMarginType = Entry('marginType', 'dapiPrivate', 'POST', {'cost': 1})
    dapiprivate_post_positionmargin = dapiPrivatePostPositionMargin = Entry('positionMargin', 'dapiPrivate', 'POST', {'cost': 1})
    dapiprivate_post_listenkey = dapiPrivatePostListenKey = Entry('listenKey', 'dapiPrivate', 'POST', {'cost': 1})
    dapiprivate_put_listenkey = dapiPrivatePutListenKey = Entry('listenKey', 'dapiPrivate', 'PUT', {'cost': 1})
    dapiprivate_put_order = dapiPrivatePutOrder = Entry('order', 'dapiPrivate', 'PUT', {'cost': 1})
    dapiprivate_put_batchorders = dapiPrivatePutBatchOrders = Entry('batchOrders', 'dapiPrivate', 'PUT', {'cost': 5})
    dapiprivate_delete_order = dapiPrivateDeleteOrder = Entry('order', 'dapiPrivate', 'DELETE', {'cost': 1})
    dapiprivate_delete_allopenorders = dapiPrivateDeleteAllOpenOrders = Entry('allOpenOrders', 'dapiPrivate', 'DELETE', {'cost': 1})
    dapiprivate_delete_batchorders = dapiPrivateDeleteBatchOrders = Entry('batchOrders', 'dapiPrivate', 'DELETE', {'cost': 5})
    dapiprivate_delete_listenkey = dapiPrivateDeleteListenKey = Entry('listenKey', 'dapiPrivate', 'DELETE', {'cost': 1})
    dapiprivatev2_get_leveragebracket = dapiPrivateV2GetLeverageBracket = Entry('leverageBracket', 'dapiPrivateV2', 'GET', {'cost': 1})
    fapipublic_get_ping = fapiPublicGetPing = Entry('ping', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_time = fapiPublicGetTime = Entry('time', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_exchangeinfo = fapiPublicGetExchangeInfo = Entry('exchangeInfo', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_depth = fapiPublicGetDepth = Entry('depth', 'fapiPublic', 'GET', {'cost': 2, 'byLimit': [[50, 2], [100, 5], [500, 10], [1000, 20]]})
    fapipublic_get_trades = fapiPublicGetTrades = Entry('trades', 'fapiPublic', 'GET', {'cost': 5})
    fapipublic_get_historicaltrades = fapiPublicGetHistoricalTrades = Entry('historicalTrades', 'fapiPublic', 'GET', {'cost': 20})
    fapipublic_get_aggtrades = fapiPublicGetAggTrades = Entry('aggTrades', 'fapiPublic', 'GET', {'cost': 20})
    fapipublic_get_klines = fapiPublicGetKlines = Entry('klines', 'fapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    fapipublic_get_continuousklines = fapiPublicGetContinuousKlines = Entry('continuousKlines', 'fapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    fapipublic_get_markpriceklines = fapiPublicGetMarkPriceKlines = Entry('markPriceKlines', 'fapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    fapipublic_get_indexpriceklines = fapiPublicGetIndexPriceKlines = Entry('indexPriceKlines', 'fapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    fapipublic_get_premiumindexklines = fapiPublicGetPremiumIndexKlines = Entry('premiumIndexKlines', 'fapiPublic', 'GET', {'cost': 1, 'byLimit': [[99, 1], [499, 2], [1000, 5], [10000, 10]]})
    fapipublic_get_fundingrate = fapiPublicGetFundingRate = Entry('fundingRate', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_fundinginfo = fapiPublicGetFundingInfo = Entry('fundingInfo', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_premiumindex = fapiPublicGetPremiumIndex = Entry('premiumIndex', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_ticker_24hr = fapiPublicGetTicker24hr = Entry('ticker/24hr', 'fapiPublic', 'GET', {'cost': 1, 'noSymbol': 40})
    fapipublic_get_ticker_price = fapiPublicGetTickerPrice = Entry('ticker/price', 'fapiPublic', 'GET', {'cost': 1, 'noSymbol': 2})
    fapipublic_get_ticker_bookticker = fapiPublicGetTickerBookTicker = Entry('ticker/bookTicker', 'fapiPublic', 'GET', {'cost': 1, 'noSymbol': 2})
    fapipublic_get_openinterest = fapiPublicGetOpenInterest = Entry('openInterest', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_indexinfo = fapiPublicGetIndexInfo = Entry('indexInfo', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_assetindex = fapiPublicGetAssetIndex = Entry('assetIndex', 'fapiPublic', 'GET', {'cost': 1, 'noSymbol': 10})
    fapipublic_get_constituents = fapiPublicGetConstituents = Entry('constituents', 'fapiPublic', 'GET', {'cost': 2})
    fapipublic_get_apitradingstatus = fapiPublicGetApiTradingStatus = Entry('apiTradingStatus', 'fapiPublic', 'GET', {'cost': 1, 'noSymbol': 10})
    fapipublic_get_lvtklines = fapiPublicGetLvtKlines = Entry('lvtKlines', 'fapiPublic', 'GET', {'cost': 1})
    fapipublic_get_convert_exchangeinfo = fapiPublicGetConvertExchangeInfo = Entry('convert/exchangeInfo', 'fapiPublic', 'GET', {'cost': 4})
    fapidata_get_delivery_price = fapiDataGetDeliveryPrice = Entry('delivery-price', 'fapiData', 'GET', {'cost': 1})
    fapidata_get_openinteresthist = fapiDataGetOpenInterestHist = Entry('openInterestHist', 'fapiData', 'GET', {'cost': 1})
    fapidata_get_toplongshortaccountratio = fapiDataGetTopLongShortAccountRatio = Entry('topLongShortAccountRatio', 'fapiData', 'GET', {'cost': 1})
    fapidata_get_toplongshortpositionratio = fapiDataGetTopLongShortPositionRatio = Entry('topLongShortPositionRatio', 'fapiData', 'GET', {'cost': 1})
    fapidata_get_globallongshortaccountratio = fapiDataGetGlobalLongShortAccountRatio = Entry('globalLongShortAccountRatio', 'fapiData', 'GET', {'cost': 1})
    fapidata_get_takerlongshortratio = fapiDataGetTakerlongshortRatio = Entry('takerlongshortRatio', 'fapiData', 'GET', {'cost': 1})
    fapidata_get_basis = fapiDataGetBasis = Entry('basis', 'fapiData', 'GET', {'cost': 1})
    fapiprivate_get_forceorders = fapiPrivateGetForceOrders = Entry('forceOrders', 'fapiPrivate', 'GET', {'cost': 20, 'noSymbol': 50})
    fapiprivate_get_allorders = fapiPrivateGetAllOrders = Entry('allOrders', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_openorder = fapiPrivateGetOpenOrder = Entry('openOrder', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_openorders = fapiPrivateGetOpenOrders = Entry('openOrders', 'fapiPrivate', 'GET', {'cost': 1, 'noSymbol': 40})
    fapiprivate_get_order = fapiPrivateGetOrder = Entry('order', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_account = fapiPrivateGetAccount = Entry('account', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_balance = fapiPrivateGetBalance = Entry('balance', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_leveragebracket = fapiPrivateGetLeverageBracket = Entry('leverageBracket', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_positionmargin_history = fapiPrivateGetPositionMarginHistory = Entry('positionMargin/history', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_positionrisk = fapiPrivateGetPositionRisk = Entry('positionRisk', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_positionside_dual = fapiPrivateGetPositionSideDual = Entry('positionSide/dual', 'fapiPrivate', 'GET', {'cost': 30})
    fapiprivate_get_usertrades = fapiPrivateGetUserTrades = Entry('userTrades', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_income = fapiPrivateGetIncome = Entry('income', 'fapiPrivate', 'GET', {'cost': 30})
    fapiprivate_get_commissionrate = fapiPrivateGetCommissionRate = Entry('commissionRate', 'fapiPrivate', 'GET', {'cost': 20})
    fapiprivate_get_ratelimit_order = fapiPrivateGetRateLimitOrder = Entry('rateLimit/order', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apitradingstatus = fapiPrivateGetApiTradingStatus = Entry('apiTradingStatus', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_multiassetsmargin = fapiPrivateGetMultiAssetsMargin = Entry('multiAssetsMargin', 'fapiPrivate', 'GET', {'cost': 30})
    fapiprivate_get_apireferral_ifnewuser = fapiPrivateGetApiReferralIfNewUser = Entry('apiReferral/ifNewUser', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_customization = fapiPrivateGetApiReferralCustomization = Entry('apiReferral/customization', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_usercustomization = fapiPrivateGetApiReferralUserCustomization = Entry('apiReferral/userCustomization', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_tradernum = fapiPrivateGetApiReferralTraderNum = Entry('apiReferral/traderNum', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_overview = fapiPrivateGetApiReferralOverview = Entry('apiReferral/overview', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_tradevol = fapiPrivateGetApiReferralTradeVol = Entry('apiReferral/tradeVol', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_rebatevol = fapiPrivateGetApiReferralRebateVol = Entry('apiReferral/rebateVol', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_apireferral_tradersummary = fapiPrivateGetApiReferralTraderSummary = Entry('apiReferral/traderSummary', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_adlquantile = fapiPrivateGetAdlQuantile = Entry('adlQuantile', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_pmaccountinfo = fapiPrivateGetPmAccountInfo = Entry('pmAccountInfo', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_orderamendment = fapiPrivateGetOrderAmendment = Entry('orderAmendment', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_income_asyn = fapiPrivateGetIncomeAsyn = Entry('income/asyn', 'fapiPrivate', 'GET', {'cost': 1000})
    fapiprivate_get_income_asyn_id = fapiPrivateGetIncomeAsynId = Entry('income/asyn/id', 'fapiPrivate', 'GET', {'cost': 10})
    fapiprivate_get_order_asyn = fapiPrivateGetOrderAsyn = Entry('order/asyn', 'fapiPrivate', 'GET', {'cost': 1000})
    fapiprivate_get_order_asyn_id = fapiPrivateGetOrderAsynId = Entry('order/asyn/id', 'fapiPrivate', 'GET', {'cost': 10})
    fapiprivate_get_trade_asyn = fapiPrivateGetTradeAsyn = Entry('trade/asyn', 'fapiPrivate', 'GET', {'cost': 1000})
    fapiprivate_get_trade_asyn_id = fapiPrivateGetTradeAsynId = Entry('trade/asyn/id', 'fapiPrivate', 'GET', {'cost': 10})
    fapiprivate_get_feeburn = fapiPrivateGetFeeBurn = Entry('feeBurn', 'fapiPrivate', 'GET', {'cost': 1})
    fapiprivate_get_symbolconfig = fapiPrivateGetSymbolConfig = Entry('symbolConfig', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_accountconfig = fapiPrivateGetAccountConfig = Entry('accountConfig', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_get_convert_orderstatus = fapiPrivateGetConvertOrderStatus = Entry('convert/orderStatus', 'fapiPrivate', 'GET', {'cost': 5})
    fapiprivate_post_batchorders = fapiPrivatePostBatchOrders = Entry('batchOrders', 'fapiPrivate', 'POST', {'cost': 5})
    fapiprivate_post_positionside_dual = fapiPrivatePostPositionSideDual = Entry('positionSide/dual', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_positionmargin = fapiPrivatePostPositionMargin = Entry('positionMargin', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_margintype = fapiPrivatePostMarginType = Entry('marginType', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_order = fapiPrivatePostOrder = Entry('order', 'fapiPrivate', 'POST', {'cost': 4})
    fapiprivate_post_leverage = fapiPrivatePostLeverage = Entry('leverage', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_listenkey = fapiPrivatePostListenKey = Entry('listenKey', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_countdowncancelall = fapiPrivatePostCountdownCancelAll = Entry('countdownCancelAll', 'fapiPrivate', 'POST', {'cost': 10})
    fapiprivate_post_multiassetsmargin = fapiPrivatePostMultiAssetsMargin = Entry('multiAssetsMargin', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_apireferral_customization = fapiPrivatePostApiReferralCustomization = Entry('apiReferral/customization', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_apireferral_usercustomization = fapiPrivatePostApiReferralUserCustomization = Entry('apiReferral/userCustomization', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_feeburn = fapiPrivatePostFeeBurn = Entry('feeBurn', 'fapiPrivate', 'POST', {'cost': 1})
    fapiprivate_post_convert_getquote = fapiPrivatePostConvertGetQuote = Entry('convert/getQuote', 'fapiPrivate', 'POST', {'cost': 200})
    fapiprivate_post_convert_acceptquote = fapiPrivatePostConvertAcceptQuote = Entry('convert/acceptQuote', 'fapiPrivate', 'POST', {'cost': 20})
    fapiprivate_put_listenkey = fapiPrivatePutListenKey = Entry('listenKey', 'fapiPrivate', 'PUT', {'cost': 1})
    fapiprivate_put_order = fapiPrivatePutOrder = Entry('order', 'fapiPrivate', 'PUT', {'cost': 1})
    fapiprivate_put_batchorders = fapiPrivatePutBatchOrders = Entry('batchOrders', 'fapiPrivate', 'PUT', {'cost': 5})
    fapiprivate_delete_batchorders = fapiPrivateDeleteBatchOrders = Entry('batchOrders', 'fapiPrivate', 'DELETE', {'cost': 1})
    fapiprivate_delete_order = fapiPrivateDeleteOrder = Entry('order', 'fapiPrivate', 'DELETE', {'cost': 1})
    fapiprivate_delete_allopenorders = fapiPrivateDeleteAllOpenOrders = Entry('allOpenOrders', 'fapiPrivate', 'DELETE', {'cost': 1})
    fapiprivate_delete_listenkey = fapiPrivateDeleteListenKey = Entry('listenKey', 'fapiPrivate', 'DELETE', {'cost': 1})
    fapipublicv2_get_ticker_price = fapiPublicV2GetTickerPrice = Entry('ticker/price', 'fapiPublicV2', 'GET', {'cost': 0})
    fapiprivatev2_get_account = fapiPrivateV2GetAccount = Entry('account', 'fapiPrivateV2', 'GET', {'cost': 1})
    fapiprivatev2_get_balance = fapiPrivateV2GetBalance = Entry('balance', 'fapiPrivateV2', 'GET', {'cost': 1})
    fapiprivatev2_get_positionrisk = fapiPrivateV2GetPositionRisk = Entry('positionRisk', 'fapiPrivateV2', 'GET', {'cost': 1})
    fapiprivatev3_get_account = fapiPrivateV3GetAccount = Entry('account', 'fapiPrivateV3', 'GET', {'cost': 1})
    fapiprivatev3_get_balance = fapiPrivateV3GetBalance = Entry('balance', 'fapiPrivateV3', 'GET', {'cost': 1})
    fapiprivatev3_get_positionrisk = fapiPrivateV3GetPositionRisk = Entry('positionRisk', 'fapiPrivateV3', 'GET', {'cost': 1})
    eapipublic_get_ping = eapiPublicGetPing = Entry('ping', 'eapiPublic', 'GET', {'cost': 1})
    eapipublic_get_time = eapiPublicGetTime = Entry('time', 'eapiPublic', 'GET', {'cost': 1})
    eapipublic_get_exchangeinfo = eapiPublicGetExchangeInfo = Entry('exchangeInfo', 'eapiPublic', 'GET', {'cost': 1})
    eapipublic_get_index = eapiPublicGetIndex = Entry('index', 'eapiPublic', 'GET', {'cost': 1})
    eapipublic_get_ticker = eapiPublicGetTicker = Entry('ticker', 'eapiPublic', 'GET', {'cost': 5})
    eapipublic_get_mark = eapiPublicGetMark = Entry('mark', 'eapiPublic', 'GET', {'cost': 5})
    eapipublic_get_depth = eapiPublicGetDepth = Entry('depth', 'eapiPublic', 'GET', {'cost': 1})
    eapipublic_get_klines = eapiPublicGetKlines = Entry('klines', 'eapiPublic', 'GET', {'cost': 1})
    eapipublic_get_trades = eapiPublicGetTrades = Entry('trades', 'eapiPublic', 'GET', {'cost': 5})
    eapipublic_get_historicaltrades = eapiPublicGetHistoricalTrades = Entry('historicalTrades', 'eapiPublic', 'GET', {'cost': 20})
    eapipublic_get_exercisehistory = eapiPublicGetExerciseHistory = Entry('exerciseHistory', 'eapiPublic', 'GET', {'cost': 3})
    eapipublic_get_openinterest = eapiPublicGetOpenInterest = Entry('openInterest', 'eapiPublic', 'GET', {'cost': 3})
    eapiprivate_get_account = eapiPrivateGetAccount = Entry('account', 'eapiPrivate', 'GET', {'cost': 3})
    eapiprivate_get_position = eapiPrivateGetPosition = Entry('position', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_openorders = eapiPrivateGetOpenOrders = Entry('openOrders', 'eapiPrivate', 'GET', {'cost': 1, 'noSymbol': 40})
    eapiprivate_get_historyorders = eapiPrivateGetHistoryOrders = Entry('historyOrders', 'eapiPrivate', 'GET', {'cost': 3})
    eapiprivate_get_usertrades = eapiPrivateGetUserTrades = Entry('userTrades', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_exerciserecord = eapiPrivateGetExerciseRecord = Entry('exerciseRecord', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_bill = eapiPrivateGetBill = Entry('bill', 'eapiPrivate', 'GET', {'cost': 1})
    eapiprivate_get_income_asyn = eapiPrivateGetIncomeAsyn = Entry('income/asyn', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_income_asyn_id = eapiPrivateGetIncomeAsynId = Entry('income/asyn/id', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_marginaccount = eapiPrivateGetMarginAccount = Entry('marginAccount', 'eapiPrivate', 'GET', {'cost': 3})
    eapiprivate_get_mmp = eapiPrivateGetMmp = Entry('mmp', 'eapiPrivate', 'GET', {'cost': 1})
    eapiprivate_get_countdowncancelall = eapiPrivateGetCountdownCancelAll = Entry('countdownCancelAll', 'eapiPrivate', 'GET', {'cost': 1})
    eapiprivate_get_order = eapiPrivateGetOrder = Entry('order', 'eapiPrivate', 'GET', {'cost': 1})
    eapiprivate_get_block_order_orders = eapiPrivateGetBlockOrderOrders = Entry('block/order/orders', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_block_order_execute = eapiPrivateGetBlockOrderExecute = Entry('block/order/execute', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_block_user_trades = eapiPrivateGetBlockUserTrades = Entry('block/user-trades', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_get_blocktrades = eapiPrivateGetBlockTrades = Entry('blockTrades', 'eapiPrivate', 'GET', {'cost': 5})
    eapiprivate_post_order = eapiPrivatePostOrder = Entry('order', 'eapiPrivate', 'POST', {'cost': 1})
    eapiprivate_post_batchorders = eapiPrivatePostBatchOrders = Entry('batchOrders', 'eapiPrivate', 'POST', {'cost': 5})
    eapiprivate_post_listenkey = eapiPrivatePostListenKey = Entry('listenKey', 'eapiPrivate', 'POST', {'cost': 1})
    eapiprivate_post_mmpset = eapiPrivatePostMmpSet = Entry('mmpSet', 'eapiPrivate', 'POST', {'cost': 1})
    eapiprivate_post_mmpreset = eapiPrivatePostMmpReset = Entry('mmpReset', 'eapiPrivate', 'POST', {'cost': 1})
    eapiprivate_post_countdowncancelall = eapiPrivatePostCountdownCancelAll = Entry('countdownCancelAll', 'eapiPrivate', 'POST', {'cost': 1})
    eapiprivate_post_countdowncancelallheartbeat = eapiPrivatePostCountdownCancelAllHeartBeat = Entry('countdownCancelAllHeartBeat', 'eapiPrivate', 'POST', {'cost': 10})
    eapiprivate_post_block_order_create = eapiPrivatePostBlockOrderCreate = Entry('block/order/create', 'eapiPrivate', 'POST', {'cost': 5})
    eapiprivate_post_block_order_execute = eapiPrivatePostBlockOrderExecute = Entry('block/order/execute', 'eapiPrivate', 'POST', {'cost': 5})
    eapiprivate_put_listenkey = eapiPrivatePutListenKey = Entry('listenKey', 'eapiPrivate', 'PUT', {'cost': 1})
    eapiprivate_put_block_order_create = eapiPrivatePutBlockOrderCreate = Entry('block/order/create', 'eapiPrivate', 'PUT', {'cost': 5})
    eapiprivate_delete_order = eapiPrivateDeleteOrder = Entry('order', 'eapiPrivate', 'DELETE', {'cost': 1})
    eapiprivate_delete_batchorders = eapiPrivateDeleteBatchOrders = Entry('batchOrders', 'eapiPrivate', 'DELETE', {'cost': 1})
    eapiprivate_delete_allopenorders = eapiPrivateDeleteAllOpenOrders = Entry('allOpenOrders', 'eapiPrivate', 'DELETE', {'cost': 1})
    eapiprivate_delete_allopenordersbyunderlying = eapiPrivateDeleteAllOpenOrdersByUnderlying = Entry('allOpenOrdersByUnderlying', 'eapiPrivate', 'DELETE', {'cost': 1})
    eapiprivate_delete_listenkey = eapiPrivateDeleteListenKey = Entry('listenKey', 'eapiPrivate', 'DELETE', {'cost': 1})
    eapiprivate_delete_block_order_create = eapiPrivateDeleteBlockOrderCreate = Entry('block/order/create', 'eapiPrivate', 'DELETE', {'cost': 5})
    public_get_ping = publicGetPing = Entry('ping', 'public', 'GET', {'cost': 0.2})
    public_get_time = publicGetTime = Entry('time', 'public', 'GET', {'cost': 0.2})
    public_get_depth = publicGetDepth = Entry('depth', 'public', 'GET', {'cost': 1, 'byLimit': [[100, 1], [500, 5], [1000, 10], [5000, 50]]})
    public_get_trades = publicGetTrades = Entry('trades', 'public', 'GET', {'cost': 2})
    public_get_aggtrades = publicGetAggTrades = Entry('aggTrades', 'public', 'GET', {'cost': 0.4})
    public_get_historicaltrades = publicGetHistoricalTrades = Entry('historicalTrades', 'public', 'GET', {'cost': 2})
    public_get_klines = publicGetKlines = Entry('klines', 'public', 'GET', {'cost': 0.4})
    public_get_uiklines = publicGetUiKlines = Entry('uiKlines', 'public', 'GET', {'cost': 0.4})
    public_get_ticker_24hr = publicGetTicker24hr = Entry('ticker/24hr', 'public', 'GET', {'cost': 0.4, 'noSymbol': 16})
    public_get_ticker = publicGetTicker = Entry('ticker', 'public', 'GET', {'cost': 0.4, 'noSymbol': 16})
    public_get_ticker_tradingday = publicGetTickerTradingDay = Entry('ticker/tradingDay', 'public', 'GET', {'cost': 0.8})
    public_get_ticker_price = publicGetTickerPrice = Entry('ticker/price', 'public', 'GET', {'cost': 0.4, 'noSymbol': 0.8})
    public_get_ticker_bookticker = publicGetTickerBookTicker = Entry('ticker/bookTicker', 'public', 'GET', {'cost': 0.4, 'noSymbol': 0.8})
    public_get_exchangeinfo = publicGetExchangeInfo = Entry('exchangeInfo', 'public', 'GET', {'cost': 4})
    public_get_avgprice = publicGetAvgPrice = Entry('avgPrice', 'public', 'GET', {'cost': 0.4})
    public_put_userdatastream = publicPutUserDataStream = Entry('userDataStream', 'public', 'PUT', {'cost': 0.4})
    public_post_userdatastream = publicPostUserDataStream = Entry('userDataStream', 'public', 'POST', {'cost': 0.4})
    public_delete_userdatastream = publicDeleteUserDataStream = Entry('userDataStream', 'public', 'DELETE', {'cost': 0.4})
    private_get_allorderlist = privateGetAllOrderList = Entry('allOrderList', 'private', 'GET', {'cost': 4})
    private_get_openorderlist = privateGetOpenOrderList = Entry('openOrderList', 'private', 'GET', {'cost': 1.2})
    private_get_orderlist = privateGetOrderList = Entry('orderList', 'private', 'GET', {'cost': 0.8})
    private_get_order = privateGetOrder = Entry('order', 'private', 'GET', {'cost': 0.8})
    private_get_openorders = privateGetOpenOrders = Entry('openOrders', 'private', 'GET', {'cost': 1.2, 'noSymbol': 16})
    private_get_allorders = privateGetAllOrders = Entry('allOrders', 'private', 'GET', {'cost': 4})
    private_get_account = privateGetAccount = Entry('account', 'private', 'GET', {'cost': 4})
    private_get_mytrades = privateGetMyTrades = Entry('myTrades', 'private', 'GET', {'cost': 4})
    private_get_ratelimit_order = privateGetRateLimitOrder = Entry('rateLimit/order', 'private', 'GET', {'cost': 8})
    private_get_mypreventedmatches = privateGetMyPreventedMatches = Entry('myPreventedMatches', 'private', 'GET', {'cost': 4})
    private_get_myallocations = privateGetMyAllocations = Entry('myAllocations', 'private', 'GET', {'cost': 4})
    private_get_account_commission = privateGetAccountCommission = Entry('account/commission', 'private', 'GET', {'cost': 4})
    private_post_order_oco = privatePostOrderOco = Entry('order/oco', 'private', 'POST', {'cost': 0.2})
    private_post_orderlist_oco = privatePostOrderListOco = Entry('orderList/oco', 'private', 'POST', {'cost': 0.2})
    private_post_orderlist_oto = privatePostOrderListOto = Entry('orderList/oto', 'private', 'POST', {'cost': 0.2})
    private_post_orderlist_otoco = privatePostOrderListOtoco = Entry('orderList/otoco', 'private', 'POST', {'cost': 0.2})
    private_post_sor_order = privatePostSorOrder = Entry('sor/order', 'private', 'POST', {'cost': 0.2})
    private_post_sor_order_test = privatePostSorOrderTest = Entry('sor/order/test', 'private', 'POST', {'cost': 0.2})
    private_post_order = privatePostOrder = Entry('order', 'private', 'POST', {'cost': 0.2})
    private_post_order_cancelreplace = privatePostOrderCancelReplace = Entry('order/cancelReplace', 'private', 'POST', {'cost': 0.2})
    private_post_order_test = privatePostOrderTest = Entry('order/test', 'private', 'POST', {'cost': 0.2})
    private_delete_openorders = privateDeleteOpenOrders = Entry('openOrders', 'private', 'DELETE', {'cost': 0.2})
    private_delete_orderlist = privateDeleteOrderList = Entry('orderList', 'private', 'DELETE', {'cost': 0.2})
    private_delete_order = privateDeleteOrder = Entry('order', 'private', 'DELETE', {'cost': 0.2})
    papi_get_ping = papiGetPing = Entry('ping', 'papi', 'GET', {'cost': 0.2})
    papi_get_um_order = papiGetUmOrder = Entry('um/order', 'papi', 'GET', {'cost': 1})
    papi_get_um_openorder = papiGetUmOpenOrder = Entry('um/openOrder', 'papi', 'GET', {'cost': 1})
    papi_get_um_openorders = papiGetUmOpenOrders = Entry('um/openOrders', 'papi', 'GET', {'cost': 1, 'noSymbol': 40})
    papi_get_um_allorders = papiGetUmAllOrders = Entry('um/allOrders', 'papi', 'GET', {'cost': 5})
    papi_get_cm_order = papiGetCmOrder = Entry('cm/order', 'papi', 'GET', {'cost': 1})
    papi_get_cm_openorder = papiGetCmOpenOrder = Entry('cm/openOrder', 'papi', 'GET', {'cost': 1})
    papi_get_cm_openorders = papiGetCmOpenOrders = Entry('cm/openOrders', 'papi', 'GET', {'cost': 1, 'noSymbol': 40})
    papi_get_cm_allorders = papiGetCmAllOrders = Entry('cm/allOrders', 'papi', 'GET', {'cost': 20})
    papi_get_um_conditional_openorder = papiGetUmConditionalOpenOrder = Entry('um/conditional/openOrder', 'papi', 'GET', {'cost': 1})
    papi_get_um_conditional_openorders = papiGetUmConditionalOpenOrders = Entry('um/conditional/openOrders', 'papi', 'GET', {'cost': 1, 'noSymbol': 40})
    papi_get_um_conditional_orderhistory = papiGetUmConditionalOrderHistory = Entry('um/conditional/orderHistory', 'papi', 'GET', {'cost': 1})
    papi_get_um_conditional_allorders = papiGetUmConditionalAllOrders = Entry('um/conditional/allOrders', 'papi', 'GET', {'cost': 1, 'noSymbol': 40})
    papi_get_cm_conditional_openorder = papiGetCmConditionalOpenOrder = Entry('cm/conditional/openOrder', 'papi', 'GET', {'cost': 1})
    papi_get_cm_conditional_openorders = papiGetCmConditionalOpenOrders = Entry('cm/conditional/openOrders', 'papi', 'GET', {'cost': 1, 'noSymbol': 40})
    papi_get_cm_conditional_orderhistory = papiGetCmConditionalOrderHistory = Entry('cm/conditional/orderHistory', 'papi', 'GET', {'cost': 1})
    papi_get_cm_conditional_allorders = papiGetCmConditionalAllOrders = Entry('cm/conditional/allOrders', 'papi', 'GET', {'cost': 40})
    papi_get_margin_order = papiGetMarginOrder = Entry('margin/order', 'papi', 'GET', {'cost': 10})
    papi_get_margin_openorders = papiGetMarginOpenOrders = Entry('margin/openOrders', 'papi', 'GET', {'cost': 5})
    papi_get_margin_allorders = papiGetMarginAllOrders = Entry('margin/allOrders', 'papi', 'GET', {'cost': 100})
    papi_get_margin_orderlist = papiGetMarginOrderList = Entry('margin/orderList', 'papi', 'GET', {'cost': 5})
    papi_get_margin_allorderlist = papiGetMarginAllOrderList = Entry('margin/allOrderList', 'papi', 'GET', {'cost': 100})
    papi_get_margin_openorderlist = papiGetMarginOpenOrderList = Entry('margin/openOrderList', 'papi', 'GET', {'cost': 5})
    papi_get_margin_mytrades = papiGetMarginMyTrades = Entry('margin/myTrades', 'papi', 'GET', {'cost': 5})
    papi_get_balance = papiGetBalance = Entry('balance', 'papi', 'GET', {'cost': 4})
    papi_get_account = papiGetAccount = Entry('account', 'papi', 'GET', {'cost': 4})
    papi_get_margin_maxborrowable = papiGetMarginMaxBorrowable = Entry('margin/maxBorrowable', 'papi', 'GET', {'cost': 1})
    papi_get_margin_maxwithdraw = papiGetMarginMaxWithdraw = Entry('margin/maxWithdraw', 'papi', 'GET', {'cost': 1})
    papi_get_um_positionrisk = papiGetUmPositionRisk = Entry('um/positionRisk', 'papi', 'GET', {'cost': 1})
    papi_get_cm_positionrisk = papiGetCmPositionRisk = Entry('cm/positionRisk', 'papi', 'GET', {'cost': 0.2})
    papi_get_um_positionside_dual = papiGetUmPositionSideDual = Entry('um/positionSide/dual', 'papi', 'GET', {'cost': 6})
    papi_get_cm_positionside_dual = papiGetCmPositionSideDual = Entry('cm/positionSide/dual', 'papi', 'GET', {'cost': 6})
    papi_get_um_usertrades = papiGetUmUserTrades = Entry('um/userTrades', 'papi', 'GET', {'cost': 5})
    papi_get_cm_usertrades = papiGetCmUserTrades = Entry('cm/userTrades', 'papi', 'GET', {'cost': 20})
    papi_get_um_leveragebracket = papiGetUmLeverageBracket = Entry('um/leverageBracket', 'papi', 'GET', {'cost': 0.2})
    papi_get_cm_leveragebracket = papiGetCmLeverageBracket = Entry('cm/leverageBracket', 'papi', 'GET', {'cost': 0.2})
    papi_get_margin_forceorders = papiGetMarginForceOrders = Entry('margin/forceOrders', 'papi', 'GET', {'cost': 1})
    papi_get_um_forceorders = papiGetUmForceOrders = Entry('um/forceOrders', 'papi', 'GET', {'cost': 20, 'noSymbol': 50})
    papi_get_cm_forceorders = papiGetCmForceOrders = Entry('cm/forceOrders', 'papi', 'GET', {'cost': 20, 'noSymbol': 50})
    papi_get_um_apitradingstatus = papiGetUmApiTradingStatus = Entry('um/apiTradingStatus', 'papi', 'GET', {'cost': 0.2, 'noSymbol': 2})
    papi_get_um_commissionrate = papiGetUmCommissionRate = Entry('um/commissionRate', 'papi', 'GET', {'cost': 4})
    papi_get_cm_commissionrate = papiGetCmCommissionRate = Entry('cm/commissionRate', 'papi', 'GET', {'cost': 4})
    papi_get_margin_marginloan = papiGetMarginMarginLoan = Entry('margin/marginLoan', 'papi', 'GET', {'cost': 2})
    papi_get_margin_repayloan = papiGetMarginRepayLoan = Entry('margin/repayLoan', 'papi', 'GET', {'cost': 2})
    papi_get_margin_margininteresthistory = papiGetMarginMarginInterestHistory = Entry('margin/marginInterestHistory', 'papi', 'GET', {'cost': 0.2})
    papi_get_portfolio_interest_history = papiGetPortfolioInterestHistory = Entry('portfolio/interest-history', 'papi', 'GET', {'cost': 10})
    papi_get_um_income = papiGetUmIncome = Entry('um/income', 'papi', 'GET', {'cost': 6})
    papi_get_cm_income = papiGetCmIncome = Entry('cm/income', 'papi', 'GET', {'cost': 6})
    papi_get_um_account = papiGetUmAccount = Entry('um/account', 'papi', 'GET', {'cost': 1})
    papi_get_cm_account = papiGetCmAccount = Entry('cm/account', 'papi', 'GET', {'cost': 1})
    papi_get_repay_futures_switch = papiGetRepayFuturesSwitch = Entry('repay-futures-switch', 'papi', 'GET', {'cost': 6})
    papi_get_um_adlquantile = papiGetUmAdlQuantile = Entry('um/adlQuantile', 'papi', 'GET', {'cost': 5})
    papi_get_cm_adlquantile = papiGetCmAdlQuantile = Entry('cm/adlQuantile', 'papi', 'GET', {'cost': 5})
    papi_get_um_trade_asyn = papiGetUmTradeAsyn = Entry('um/trade/asyn', 'papi', 'GET', {'cost': 300})
    papi_get_um_trade_asyn_id = papiGetUmTradeAsynId = Entry('um/trade/asyn/id', 'papi', 'GET', {'cost': 2})
    papi_get_um_order_asyn = papiGetUmOrderAsyn = Entry('um/order/asyn', 'papi', 'GET', {'cost': 300})
    papi_get_um_order_asyn_id = papiGetUmOrderAsynId = Entry('um/order/asyn/id', 'papi', 'GET', {'cost': 2})
    papi_get_um_income_asyn = papiGetUmIncomeAsyn = Entry('um/income/asyn', 'papi', 'GET', {'cost': 300})
    papi_get_um_income_asyn_id = papiGetUmIncomeAsynId = Entry('um/income/asyn/id', 'papi', 'GET', {'cost': 2})
    papi_get_um_orderamendment = papiGetUmOrderAmendment = Entry('um/orderAmendment', 'papi', 'GET', {'cost': 1})
    papi_get_cm_orderamendment = papiGetCmOrderAmendment = Entry('cm/orderAmendment', 'papi', 'GET', {'cost': 1})
    papi_get_um_feeburn = papiGetUmFeeBurn = Entry('um/feeBurn', 'papi', 'GET', {'cost': 30})
    papi_get_um_accountconfig = papiGetUmAccountConfig = Entry('um/accountConfig', 'papi', 'GET', {'cost': 1})
    papi_get_um_symbolconfig = papiGetUmSymbolConfig = Entry('um/symbolConfig', 'papi', 'GET', {'cost': 1})
    papi_get_cm_accountconfig = papiGetCmAccountConfig = Entry('cm/accountConfig', 'papi', 'GET', {'cost': 1})
    papi_get_cm_symbolconfig = papiGetCmSymbolConfig = Entry('cm/symbolConfig', 'papi', 'GET', {'cost': 1})
    papi_get_ratelimit_order = papiGetRateLimitOrder = Entry('rateLimit/order', 'papi', 'GET', {'cost': 1})
    papi_post_um_order = papiPostUmOrder = Entry('um/order', 'papi', 'POST', {'cost': 1})
    papi_post_um_conditional_order = papiPostUmConditionalOrder = Entry('um/conditional/order', 'papi', 'POST', {'cost': 1})
    papi_post_cm_order = papiPostCmOrder = Entry('cm/order', 'papi', 'POST', {'cost': 1})
    papi_post_cm_conditional_order = papiPostCmConditionalOrder = Entry('cm/conditional/order', 'papi', 'POST', {'cost': 1})
    papi_post_margin_order = papiPostMarginOrder = Entry('margin/order', 'papi', 'POST', {'cost': 1})
    papi_post_marginloan = papiPostMarginLoan = Entry('marginLoan', 'papi', 'POST', {'cost': 100})
    papi_post_repayloan = papiPostRepayLoan = Entry('repayLoan', 'papi', 'POST', {'cost': 100})
    papi_post_margin_order_oco = papiPostMarginOrderOco = Entry('margin/order/oco', 'papi', 'POST', {'cost': 1})
    papi_post_um_leverage = papiPostUmLeverage = Entry('um/leverage', 'papi', 'POST', {'cost': 0.2})
    papi_post_cm_leverage = papiPostCmLeverage = Entry('cm/leverage', 'papi', 'POST', {'cost': 0.2})
    papi_post_um_positionside_dual = papiPostUmPositionSideDual = Entry('um/positionSide/dual', 'papi', 'POST', {'cost': 0.2})
    papi_post_cm_positionside_dual = papiPostCmPositionSideDual = Entry('cm/positionSide/dual', 'papi', 'POST', {'cost': 0.2})
    papi_post_auto_collection = papiPostAutoCollection = Entry('auto-collection', 'papi', 'POST', {'cost': 150})
    papi_post_bnb_transfer = papiPostBnbTransfer = Entry('bnb-transfer', 'papi', 'POST', {'cost': 150})
    papi_post_repay_futures_switch = papiPostRepayFuturesSwitch = Entry('repay-futures-switch', 'papi', 'POST', {'cost': 150})
    papi_post_repay_futures_negative_balance = papiPostRepayFuturesNegativeBalance = Entry('repay-futures-negative-balance', 'papi', 'POST', {'cost': 150})
    papi_post_listenkey = papiPostListenKey = Entry('listenKey', 'papi', 'POST', {'cost': 0.2})
    papi_post_asset_collection = papiPostAssetCollection = Entry('asset-collection', 'papi', 'POST', {'cost': 6})
    papi_post_margin_repay_debt = papiPostMarginRepayDebt = Entry('margin/repay-debt', 'papi', 'POST', {'cost': 3000})
    papi_post_um_feeburn = papiPostUmFeeBurn = Entry('um/feeBurn', 'papi', 'POST', {'cost': 1})
    papi_put_listenkey = papiPutListenKey = Entry('listenKey', 'papi', 'PUT', {'cost': 0.2})
    papi_put_um_order = papiPutUmOrder = Entry('um/order', 'papi', 'PUT', {'cost': 1})
    papi_put_cm_order = papiPutCmOrder = Entry('cm/order', 'papi', 'PUT', {'cost': 1})
    papi_delete_um_order = papiDeleteUmOrder = Entry('um/order', 'papi', 'DELETE', {'cost': 1})
    papi_delete_um_conditional_order = papiDeleteUmConditionalOrder = Entry('um/conditional/order', 'papi', 'DELETE', {'cost': 1})
    papi_delete_um_allopenorders = papiDeleteUmAllOpenOrders = Entry('um/allOpenOrders', 'papi', 'DELETE', {'cost': 1})
    papi_delete_um_conditional_allopenorders = papiDeleteUmConditionalAllOpenOrders = Entry('um/conditional/allOpenOrders', 'papi', 'DELETE', {'cost': 1})
    papi_delete_cm_order = papiDeleteCmOrder = Entry('cm/order', 'papi', 'DELETE', {'cost': 1})
    papi_delete_cm_conditional_order = papiDeleteCmConditionalOrder = Entry('cm/conditional/order', 'papi', 'DELETE', {'cost': 1})
    papi_delete_cm_allopenorders = papiDeleteCmAllOpenOrders = Entry('cm/allOpenOrders', 'papi', 'DELETE', {'cost': 1})
    papi_delete_cm_conditional_allopenorders = papiDeleteCmConditionalAllOpenOrders = Entry('cm/conditional/allOpenOrders', 'papi', 'DELETE', {'cost': 1})
    papi_delete_margin_order = papiDeleteMarginOrder = Entry('margin/order', 'papi', 'DELETE', {'cost': 2})
    papi_delete_margin_allopenorders = papiDeleteMarginAllOpenOrders = Entry('margin/allOpenOrders', 'papi', 'DELETE', {'cost': 5})
    papi_delete_margin_orderlist = papiDeleteMarginOrderList = Entry('margin/orderList', 'papi', 'DELETE', {'cost': 2})
    papi_delete_listenkey = papiDeleteListenKey = Entry('listenKey', 'papi', 'DELETE', {'cost': 0.2})
