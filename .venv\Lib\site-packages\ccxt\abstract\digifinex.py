from ccxt.base.types import Entry


class ImplicitAPI:
    public_spot_get_market_symbols = publicSpotGetMarketSymbols = Entry('{market}/symbols', ['public', 'spot'], 'GET', {})
    public_spot_get_kline = publicSpotGetKline = Entry('kline', ['public', 'spot'], 'GET', {})
    public_spot_get_margin_currencies = publicSpotGetMarginCurrencies = Entry('margin/currencies', ['public', 'spot'], 'GET', {})
    public_spot_get_margin_symbols = publicSpotGetMarginSymbols = Entry('margin/symbols', ['public', 'spot'], 'GET', {})
    public_spot_get_markets = publicSpotGetMarkets = Entry('markets', ['public', 'spot'], 'GET', {})
    public_spot_get_order_book = publicSpotGetOrderBook = Entry('order_book', ['public', 'spot'], 'GET', {})
    public_spot_get_ping = publicSpotGetPing = Entry('ping', ['public', 'spot'], 'GET', {})
    public_spot_get_spot_symbols = publicSpotGetSpotSymbols = Entry('spot/symbols', ['public', 'spot'], 'GET', {})
    public_spot_get_time = publicSpotGetTime = Entry('time', ['public', 'spot'], 'GET', {})
    public_spot_get_trades = publicSpotGetTrades = Entry('trades', ['public', 'spot'], 'GET', {})
    public_spot_get_trades_symbols = publicSpotGetTradesSymbols = Entry('trades/symbols', ['public', 'spot'], 'GET', {})
    public_spot_get_ticker = publicSpotGetTicker = Entry('ticker', ['public', 'spot'], 'GET', {})
    public_spot_get_currencies = publicSpotGetCurrencies = Entry('currencies', ['public', 'spot'], 'GET', {})
    public_swap_get_public_api_weight = publicSwapGetPublicApiWeight = Entry('public/api_weight', ['public', 'swap'], 'GET', {})
    public_swap_get_public_candles = publicSwapGetPublicCandles = Entry('public/candles', ['public', 'swap'], 'GET', {})
    public_swap_get_public_candles_history = publicSwapGetPublicCandlesHistory = Entry('public/candles_history', ['public', 'swap'], 'GET', {})
    public_swap_get_public_depth = publicSwapGetPublicDepth = Entry('public/depth', ['public', 'swap'], 'GET', {})
    public_swap_get_public_funding_rate = publicSwapGetPublicFundingRate = Entry('public/funding_rate', ['public', 'swap'], 'GET', {})
    public_swap_get_public_funding_rate_history = publicSwapGetPublicFundingRateHistory = Entry('public/funding_rate_history', ['public', 'swap'], 'GET', {})
    public_swap_get_public_instrument = publicSwapGetPublicInstrument = Entry('public/instrument', ['public', 'swap'], 'GET', {})
    public_swap_get_public_instruments = publicSwapGetPublicInstruments = Entry('public/instruments', ['public', 'swap'], 'GET', {})
    public_swap_get_public_ticker = publicSwapGetPublicTicker = Entry('public/ticker', ['public', 'swap'], 'GET', {})
    public_swap_get_public_tickers = publicSwapGetPublicTickers = Entry('public/tickers', ['public', 'swap'], 'GET', {})
    public_swap_get_public_time = publicSwapGetPublicTime = Entry('public/time', ['public', 'swap'], 'GET', {})
    public_swap_get_public_trades = publicSwapGetPublicTrades = Entry('public/trades', ['public', 'swap'], 'GET', {})
    private_spot_get_market_financelog = privateSpotGetMarketFinancelog = Entry('{market}/financelog', ['private', 'spot'], 'GET', {})
    private_spot_get_market_mytrades = privateSpotGetMarketMytrades = Entry('{market}/mytrades', ['private', 'spot'], 'GET', {})
    private_spot_get_market_order = privateSpotGetMarketOrder = Entry('{market}/order', ['private', 'spot'], 'GET', {})
    private_spot_get_market_order_detail = privateSpotGetMarketOrderDetail = Entry('{market}/order/detail', ['private', 'spot'], 'GET', {})
    private_spot_get_market_order_current = privateSpotGetMarketOrderCurrent = Entry('{market}/order/current', ['private', 'spot'], 'GET', {})
    private_spot_get_market_order_history = privateSpotGetMarketOrderHistory = Entry('{market}/order/history', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_assets = privateSpotGetMarginAssets = Entry('margin/assets', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_financelog = privateSpotGetMarginFinancelog = Entry('margin/financelog', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_mytrades = privateSpotGetMarginMytrades = Entry('margin/mytrades', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_order = privateSpotGetMarginOrder = Entry('margin/order', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_order_current = privateSpotGetMarginOrderCurrent = Entry('margin/order/current', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_order_history = privateSpotGetMarginOrderHistory = Entry('margin/order/history', ['private', 'spot'], 'GET', {})
    private_spot_get_margin_positions = privateSpotGetMarginPositions = Entry('margin/positions', ['private', 'spot'], 'GET', {})
    private_spot_get_otc_financelog = privateSpotGetOtcFinancelog = Entry('otc/financelog', ['private', 'spot'], 'GET', {})
    private_spot_get_spot_assets = privateSpotGetSpotAssets = Entry('spot/assets', ['private', 'spot'], 'GET', {})
    private_spot_get_spot_financelog = privateSpotGetSpotFinancelog = Entry('spot/financelog', ['private', 'spot'], 'GET', {})
    private_spot_get_spot_mytrades = privateSpotGetSpotMytrades = Entry('spot/mytrades', ['private', 'spot'], 'GET', {})
    private_spot_get_spot_order = privateSpotGetSpotOrder = Entry('spot/order', ['private', 'spot'], 'GET', {})
    private_spot_get_spot_order_current = privateSpotGetSpotOrderCurrent = Entry('spot/order/current', ['private', 'spot'], 'GET', {})
    private_spot_get_spot_order_history = privateSpotGetSpotOrderHistory = Entry('spot/order/history', ['private', 'spot'], 'GET', {})
    private_spot_get_deposit_address = privateSpotGetDepositAddress = Entry('deposit/address', ['private', 'spot'], 'GET', {})
    private_spot_get_deposit_history = privateSpotGetDepositHistory = Entry('deposit/history', ['private', 'spot'], 'GET', {})
    private_spot_get_withdraw_history = privateSpotGetWithdrawHistory = Entry('withdraw/history', ['private', 'spot'], 'GET', {})
    private_spot_post_market_order_cancel = privateSpotPostMarketOrderCancel = Entry('{market}/order/cancel', ['private', 'spot'], 'POST', {})
    private_spot_post_market_order_new = privateSpotPostMarketOrderNew = Entry('{market}/order/new', ['private', 'spot'], 'POST', {})
    private_spot_post_market_order_batch_new = privateSpotPostMarketOrderBatchNew = Entry('{market}/order/batch_new', ['private', 'spot'], 'POST', {})
    private_spot_post_margin_order_cancel = privateSpotPostMarginOrderCancel = Entry('margin/order/cancel', ['private', 'spot'], 'POST', {})
    private_spot_post_margin_order_new = privateSpotPostMarginOrderNew = Entry('margin/order/new', ['private', 'spot'], 'POST', {})
    private_spot_post_margin_position_close = privateSpotPostMarginPositionClose = Entry('margin/position/close', ['private', 'spot'], 'POST', {})
    private_spot_post_spot_order_cancel = privateSpotPostSpotOrderCancel = Entry('spot/order/cancel', ['private', 'spot'], 'POST', {})
    private_spot_post_spot_order_new = privateSpotPostSpotOrderNew = Entry('spot/order/new', ['private', 'spot'], 'POST', {})
    private_spot_post_transfer = privateSpotPostTransfer = Entry('transfer', ['private', 'spot'], 'POST', {})
    private_spot_post_withdraw_new = privateSpotPostWithdrawNew = Entry('withdraw/new', ['private', 'spot'], 'POST', {})
    private_spot_post_withdraw_cancel = privateSpotPostWithdrawCancel = Entry('withdraw/cancel', ['private', 'spot'], 'POST', {})
    private_swap_get_account_balance = privateSwapGetAccountBalance = Entry('account/balance', ['private', 'swap'], 'GET', {})
    private_swap_get_account_positions = privateSwapGetAccountPositions = Entry('account/positions', ['private', 'swap'], 'GET', {})
    private_swap_get_account_finance_record = privateSwapGetAccountFinanceRecord = Entry('account/finance_record', ['private', 'swap'], 'GET', {})
    private_swap_get_account_trading_fee_rate = privateSwapGetAccountTradingFeeRate = Entry('account/trading_fee_rate', ['private', 'swap'], 'GET', {})
    private_swap_get_account_transfer_record = privateSwapGetAccountTransferRecord = Entry('account/transfer_record', ['private', 'swap'], 'GET', {})
    private_swap_get_account_funding_fee = privateSwapGetAccountFundingFee = Entry('account/funding_fee', ['private', 'swap'], 'GET', {})
    private_swap_get_trade_history_orders = privateSwapGetTradeHistoryOrders = Entry('trade/history_orders', ['private', 'swap'], 'GET', {})
    private_swap_get_trade_history_trades = privateSwapGetTradeHistoryTrades = Entry('trade/history_trades', ['private', 'swap'], 'GET', {})
    private_swap_get_trade_open_orders = privateSwapGetTradeOpenOrders = Entry('trade/open_orders', ['private', 'swap'], 'GET', {})
    private_swap_get_trade_order_info = privateSwapGetTradeOrderInfo = Entry('trade/order_info', ['private', 'swap'], 'GET', {})
    private_swap_post_account_transfer = privateSwapPostAccountTransfer = Entry('account/transfer', ['private', 'swap'], 'POST', {})
    private_swap_post_account_leverage = privateSwapPostAccountLeverage = Entry('account/leverage', ['private', 'swap'], 'POST', {})
    private_swap_post_account_position_mode = privateSwapPostAccountPositionMode = Entry('account/position_mode', ['private', 'swap'], 'POST', {})
    private_swap_post_account_position_margin = privateSwapPostAccountPositionMargin = Entry('account/position_margin', ['private', 'swap'], 'POST', {})
    private_swap_post_trade_batch_cancel_order = privateSwapPostTradeBatchCancelOrder = Entry('trade/batch_cancel_order', ['private', 'swap'], 'POST', {})
    private_swap_post_trade_batch_order = privateSwapPostTradeBatchOrder = Entry('trade/batch_order', ['private', 'swap'], 'POST', {})
    private_swap_post_trade_cancel_order = privateSwapPostTradeCancelOrder = Entry('trade/cancel_order', ['private', 'swap'], 'POST', {})
    private_swap_post_trade_order_place = privateSwapPostTradeOrderPlace = Entry('trade/order_place', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_sponsor_order = privateSwapPostFollowSponsorOrder = Entry('follow/sponsor_order', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_close_order = privateSwapPostFollowCloseOrder = Entry('follow/close_order', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_cancel_order = privateSwapPostFollowCancelOrder = Entry('follow/cancel_order', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_user_center_current = privateSwapPostFollowUserCenterCurrent = Entry('follow/user_center_current', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_user_center_history = privateSwapPostFollowUserCenterHistory = Entry('follow/user_center_history', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_expert_current_open_order = privateSwapPostFollowExpertCurrentOpenOrder = Entry('follow/expert_current_open_order', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_add_algo = privateSwapPostFollowAddAlgo = Entry('follow/add_algo', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_cancel_algo = privateSwapPostFollowCancelAlgo = Entry('follow/cancel_algo', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_account_available = privateSwapPostFollowAccountAvailable = Entry('follow/account_available', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_plan_task = privateSwapPostFollowPlanTask = Entry('follow/plan_task', ['private', 'swap'], 'POST', {})
    private_swap_post_follow_instrument_list = privateSwapPostFollowInstrumentList = Entry('follow/instrument_list', ['private', 'swap'], 'POST', {})
