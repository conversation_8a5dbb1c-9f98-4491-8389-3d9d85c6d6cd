from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_market_books_full = publicGetMarketBooksFull = Entry('market/books-full', 'public', 'GET', {'cost': 2})
    public_get_market_tickers = publicGetMarketTickers = Entry('market/tickers', 'public', 'GET', {'cost': 1})
    public_get_market_ticker = publicGetMarketTicker = Entry('market/ticker', 'public', 'GET', {'cost': 1})
    public_get_market_index_tickers = publicGetMarketIndexTickers = Entry('market/index-tickers', 'public', 'GET', {'cost': 1})
    public_get_market_books = publicGetMarketBooks = Entry('market/books', 'public', 'GET', {'cost': 0.5})
    public_get_market_books_lite = publicGetMarketBooksLite = Entry('market/books-lite', 'public', 'GET', {'cost': 1.****************})
    public_get_market_candles = publicGetMarketCandles = Entry('market/candles', 'public', 'GET', {'cost': 0.5})
    public_get_market_history_candles = publicGetMarketHistoryCandles = Entry('market/history-candles', 'public', 'GET', {'cost': 1})
    public_get_market_index_candles = publicGetMarketIndexCandles = Entry('market/index-candles', 'public', 'GET', {'cost': 1})
    public_get_market_history_index_candles = publicGetMarketHistoryIndexCandles = Entry('market/history-index-candles', 'public', 'GET', {'cost': 2})
    public_get_market_mark_price_candles = publicGetMarketMarkPriceCandles = Entry('market/mark-price-candles', 'public', 'GET', {'cost': 1})
    public_get_market_history_mark_price_candles = publicGetMarketHistoryMarkPriceCandles = Entry('market/history-mark-price-candles', 'public', 'GET', {'cost': 2})
    public_get_market_trades = publicGetMarketTrades = Entry('market/trades', 'public', 'GET', {'cost': 0.2})
    public_get_market_history_trades = publicGetMarketHistoryTrades = Entry('market/history-trades', 'public', 'GET', {'cost': 2})
    public_get_market_option_instrument_family_trades = publicGetMarketOptionInstrumentFamilyTrades = Entry('market/option/instrument-family-trades', 'public', 'GET', {'cost': 1})
    public_get_market_platform_24_volume = publicGetMarketPlatform24Volume = Entry('market/platform-24-volume', 'public', 'GET', {'cost': 10})
    public_get_market_open_oracle = publicGetMarketOpenOracle = Entry('market/open-oracle', 'public', 'GET', {'cost': 50})
    public_get_market_exchange_rate = publicGetMarketExchangeRate = Entry('market/exchange-rate', 'public', 'GET', {'cost': 20})
    public_get_market_index_components = publicGetMarketIndexComponents = Entry('market/index-components', 'public', 'GET', {'cost': 1})
    public_get_public_economic_calendar = publicGetPublicEconomicCalendar = Entry('public/economic-calendar', 'public', 'GET', {'cost': 50})
    public_get_market_block_tickers = publicGetMarketBlockTickers = Entry('market/block-tickers', 'public', 'GET', {'cost': 1})
    public_get_market_block_ticker = publicGetMarketBlockTicker = Entry('market/block-ticker', 'public', 'GET', {'cost': 1})
    public_get_public_block_trades = publicGetPublicBlockTrades = Entry('public/block-trades', 'public', 'GET', {'cost': 1})
    public_get_public_instruments = publicGetPublicInstruments = Entry('public/instruments', 'public', 'GET', {'cost': 1})
    public_get_public_delivery_exercise_history = publicGetPublicDeliveryExerciseHistory = Entry('public/delivery-exercise-history', 'public', 'GET', {'cost': 0.5})
    public_get_public_open_interest = publicGetPublicOpenInterest = Entry('public/open-interest', 'public', 'GET', {'cost': 1})
    public_get_public_funding_rate = publicGetPublicFundingRate = Entry('public/funding-rate', 'public', 'GET', {'cost': 1})
    public_get_public_funding_rate_history = publicGetPublicFundingRateHistory = Entry('public/funding-rate-history', 'public', 'GET', {'cost': 1})
    public_get_public_price_limit = publicGetPublicPriceLimit = Entry('public/price-limit', 'public', 'GET', {'cost': 1})
    public_get_public_opt_summary = publicGetPublicOptSummary = Entry('public/opt-summary', 'public', 'GET', {'cost': 1})
    public_get_public_estimated_price = publicGetPublicEstimatedPrice = Entry('public/estimated-price', 'public', 'GET', {'cost': 2})
    public_get_public_discount_rate_interest_free_quota = publicGetPublicDiscountRateInterestFreeQuota = Entry('public/discount-rate-interest-free-quota', 'public', 'GET', {'cost': 10})
    public_get_public_time = publicGetPublicTime = Entry('public/time', 'public', 'GET', {'cost': 2})
    public_get_public_mark_price = publicGetPublicMarkPrice = Entry('public/mark-price', 'public', 'GET', {'cost': 2})
    public_get_public_position_tiers = publicGetPublicPositionTiers = Entry('public/position-tiers', 'public', 'GET', {'cost': 2})
    public_get_public_interest_rate_loan_quota = publicGetPublicInterestRateLoanQuota = Entry('public/interest-rate-loan-quota', 'public', 'GET', {'cost': 10})
    public_get_public_vip_interest_rate_loan_quota = publicGetPublicVipInterestRateLoanQuota = Entry('public/vip-interest-rate-loan-quota', 'public', 'GET', {'cost': 10})
    public_get_public_underlying = publicGetPublicUnderlying = Entry('public/underlying', 'public', 'GET', {'cost': 1})
    public_get_public_insurance_fund = publicGetPublicInsuranceFund = Entry('public/insurance-fund', 'public', 'GET', {'cost': 2})
    public_get_public_convert_contract_coin = publicGetPublicConvertContractCoin = Entry('public/convert-contract-coin', 'public', 'GET', {'cost': 2})
    public_get_public_option_trades = publicGetPublicOptionTrades = Entry('public/option-trades', 'public', 'GET', {'cost': 1})
    public_get_public_instrument_tick_bands = publicGetPublicInstrumentTickBands = Entry('public/instrument-tick-bands', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_trading_data_support_coin = publicGetRubikStatTradingDataSupportCoin = Entry('rubik/stat/trading-data/support-coin', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_taker_volume = publicGetRubikStatTakerVolume = Entry('rubik/stat/taker-volume', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_margin_loan_ratio = publicGetRubikStatMarginLoanRatio = Entry('rubik/stat/margin/loan-ratio', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_contracts_long_short_account_ratio = publicGetRubikStatContractsLongShortAccountRatio = Entry('rubik/stat/contracts/long-short-account-ratio', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_contracts_long_short_account_ratio_contract = publicGetRubikStatContractsLongShortAccountRatioContract = Entry('rubik/stat/contracts/long-short-account-ratio-contract', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_contracts_open_interest_volume = publicGetRubikStatContractsOpenInterestVolume = Entry('rubik/stat/contracts/open-interest-volume', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_option_open_interest_volume = publicGetRubikStatOptionOpenInterestVolume = Entry('rubik/stat/option/open-interest-volume', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_option_open_interest_volume_ratio = publicGetRubikStatOptionOpenInterestVolumeRatio = Entry('rubik/stat/option/open-interest-volume-ratio', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_option_open_interest_volume_expiry = publicGetRubikStatOptionOpenInterestVolumeExpiry = Entry('rubik/stat/option/open-interest-volume-expiry', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_option_open_interest_volume_strike = publicGetRubikStatOptionOpenInterestVolumeStrike = Entry('rubik/stat/option/open-interest-volume-strike', 'public', 'GET', {'cost': 4})
    public_get_rubik_stat_option_taker_block_volume = publicGetRubikStatOptionTakerBlockVolume = Entry('rubik/stat/option/taker-block-volume', 'public', 'GET', {'cost': 4})
    public_get_system_status = publicGetSystemStatus = Entry('system/status', 'public', 'GET', {'cost': 50})
    public_get_sprd_spreads = publicGetSprdSpreads = Entry('sprd/spreads', 'public', 'GET', {'cost': 1})
    public_get_sprd_books = publicGetSprdBooks = Entry('sprd/books', 'public', 'GET', {'cost': 0.5})
    public_get_sprd_ticker = publicGetSprdTicker = Entry('sprd/ticker', 'public', 'GET', {'cost': 1})
    public_get_sprd_public_trades = publicGetSprdPublicTrades = Entry('sprd/public-trades', 'public', 'GET', {'cost': 0.2})
    public_get_market_sprd_ticker = publicGetMarketSprdTicker = Entry('market/sprd-ticker', 'public', 'GET', {'cost': 2})
    public_get_market_sprd_candles = publicGetMarketSprdCandles = Entry('market/sprd-candles', 'public', 'GET', {'cost': 2})
    public_get_market_sprd_history_candles = publicGetMarketSprdHistoryCandles = Entry('market/sprd-history-candles', 'public', 'GET', {'cost': 2})
    public_get_tradingbot_grid_ai_param = publicGetTradingBotGridAiParam = Entry('tradingBot/grid/ai-param', 'public', 'GET', {'cost': 1})
    public_get_tradingbot_grid_min_investment = publicGetTradingBotGridMinInvestment = Entry('tradingBot/grid/min-investment', 'public', 'GET', {'cost': 1})
    public_get_tradingbot_public_rsi_back_testing = publicGetTradingBotPublicRsiBackTesting = Entry('tradingBot/public/rsi-back-testing', 'public', 'GET', {'cost': 1})
    public_get_asset_exchange_list = publicGetAssetExchangeList = Entry('asset/exchange-list', 'public', 'GET', {'cost': 1.****************})
    public_get_finance_staking_defi_eth_apy_history = publicGetFinanceStakingDefiEthApyHistory = Entry('finance/staking-defi/eth/apy-history', 'public', 'GET', {'cost': 1.****************})
    public_get_finance_staking_defi_sol_apy_history = publicGetFinanceStakingDefiSolApyHistory = Entry('finance/staking-defi/sol/apy-history', 'public', 'GET', {'cost': 1.****************})
    public_get_finance_savings_lending_rate_summary = publicGetFinanceSavingsLendingRateSummary = Entry('finance/savings/lending-rate-summary', 'public', 'GET', {'cost': 1.****************})
    public_get_finance_savings_lending_rate_history = publicGetFinanceSavingsLendingRateHistory = Entry('finance/savings/lending-rate-history', 'public', 'GET', {'cost': 1.****************})
    public_get_finance_fixed_loan_lending_offers = publicGetFinanceFixedLoanLendingOffers = Entry('finance/fixed-loan/lending-offers', 'public', 'GET', {'cost': 3.****************})
    public_get_finance_fixed_loan_lending_apy_history = publicGetFinanceFixedLoanLendingApyHistory = Entry('finance/fixed-loan/lending-apy-history', 'public', 'GET', {'cost': 3.****************})
    public_get_finance_fixed_loan_pending_lending_volume = publicGetFinanceFixedLoanPendingLendingVolume = Entry('finance/fixed-loan/pending-lending-volume', 'public', 'GET', {'cost': 3.****************})
    public_get_finance_sfp_dcd_products = publicGetFinanceSfpDcdProducts = Entry('finance/sfp/dcd/products', 'public', 'GET', {'cost': 0.6666666666666666})
    public_get_copytrading_public_lead_traders = publicGetCopytradingPublicLeadTraders = Entry('copytrading/public-lead-traders', 'public', 'GET', {'cost': 4})
    public_get_copytrading_public_weekly_pnl = publicGetCopytradingPublicWeeklyPnl = Entry('copytrading/public-weekly-pnl', 'public', 'GET', {'cost': 4})
    public_get_copytrading_public_stats = publicGetCopytradingPublicStats = Entry('copytrading/public-stats', 'public', 'GET', {'cost': 4})
    public_get_copytrading_public_preference_currency = publicGetCopytradingPublicPreferenceCurrency = Entry('copytrading/public-preference-currency', 'public', 'GET', {'cost': 4})
    public_get_copytrading_public_current_subpositions = publicGetCopytradingPublicCurrentSubpositions = Entry('copytrading/public-current-subpositions', 'public', 'GET', {'cost': 4})
    public_get_copytrading_public_subpositions_history = publicGetCopytradingPublicSubpositionsHistory = Entry('copytrading/public-subpositions-history', 'public', 'GET', {'cost': 4})
    public_get_support_announcements_types = publicGetSupportAnnouncementsTypes = Entry('support/announcements-types', 'public', 'GET', {'cost': 20})
    private_get_rfq_counterparties = privateGetRfqCounterparties = Entry('rfq/counterparties', 'private', 'GET', {'cost': 4})
    private_get_rfq_maker_instrument_settings = privateGetRfqMakerInstrumentSettings = Entry('rfq/maker-instrument-settings', 'private', 'GET', {'cost': 4})
    private_get_rfq_mmp_config = privateGetRfqMmpConfig = Entry('rfq/mmp-config', 'private', 'GET', {'cost': 4})
    private_get_rfq_rfqs = privateGetRfqRfqs = Entry('rfq/rfqs', 'private', 'GET', {'cost': 10})
    private_get_rfq_quotes = privateGetRfqQuotes = Entry('rfq/quotes', 'private', 'GET', {'cost': 10})
    private_get_rfq_trades = privateGetRfqTrades = Entry('rfq/trades', 'private', 'GET', {'cost': 4})
    private_get_rfq_public_trades = privateGetRfqPublicTrades = Entry('rfq/public-trades', 'private', 'GET', {'cost': 4})
    private_get_sprd_order = privateGetSprdOrder = Entry('sprd/order', 'private', 'GET', {'cost': 0.**************33})
    private_get_sprd_orders_pending = privateGetSprdOrdersPending = Entry('sprd/orders-pending', 'private', 'GET', {'cost': 0.**************33})
    private_get_sprd_orders_history = privateGetSprdOrdersHistory = Entry('sprd/orders-history', 'private', 'GET', {'cost': 0.5})
    private_get_sprd_orders_history_archive = privateGetSprdOrdersHistoryArchive = Entry('sprd/orders-history-archive', 'private', 'GET', {'cost': 0.5})
    private_get_sprd_trades = privateGetSprdTrades = Entry('sprd/trades', 'private', 'GET', {'cost': 0.**************33})
    private_get_trade_order = privateGetTradeOrder = Entry('trade/order', 'private', 'GET', {'cost': 0.**************33})
    private_get_trade_orders_pending = privateGetTradeOrdersPending = Entry('trade/orders-pending', 'private', 'GET', {'cost': 0.**************33})
    private_get_trade_orders_history = privateGetTradeOrdersHistory = Entry('trade/orders-history', 'private', 'GET', {'cost': 0.5})
    private_get_trade_orders_history_archive = privateGetTradeOrdersHistoryArchive = Entry('trade/orders-history-archive', 'private', 'GET', {'cost': 1})
    private_get_trade_fills = privateGetTradeFills = Entry('trade/fills', 'private', 'GET', {'cost': 0.**************33})
    private_get_trade_fills_history = privateGetTradeFillsHistory = Entry('trade/fills-history', 'private', 'GET', {'cost': 2.2})
    private_get_trade_fills_archive = privateGetTradeFillsArchive = Entry('trade/fills-archive', 'private', 'GET', {'cost': 2})
    private_get_trade_order_algo = privateGetTradeOrderAlgo = Entry('trade/order-algo', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_algo_pending = privateGetTradeOrdersAlgoPending = Entry('trade/orders-algo-pending', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_algo_history = privateGetTradeOrdersAlgoHistory = Entry('trade/orders-algo-history', 'private', 'GET', {'cost': 1})
    private_get_trade_easy_convert_currency_list = privateGetTradeEasyConvertCurrencyList = Entry('trade/easy-convert-currency-list', 'private', 'GET', {'cost': 20})
    private_get_trade_easy_convert_history = privateGetTradeEasyConvertHistory = Entry('trade/easy-convert-history', 'private', 'GET', {'cost': 20})
    private_get_trade_one_click_repay_currency_list = privateGetTradeOneClickRepayCurrencyList = Entry('trade/one-click-repay-currency-list', 'private', 'GET', {'cost': 20})
    private_get_trade_one_click_repay_currency_list_v2 = privateGetTradeOneClickRepayCurrencyListV2 = Entry('trade/one-click-repay-currency-list-v2', 'private', 'GET', {'cost': 20})
    private_get_trade_one_click_repay_history = privateGetTradeOneClickRepayHistory = Entry('trade/one-click-repay-history', 'private', 'GET', {'cost': 20})
    private_get_trade_one_click_repay_history_v2 = privateGetTradeOneClickRepayHistoryV2 = Entry('trade/one-click-repay-history-v2', 'private', 'GET', {'cost': 20})
    private_get_trade_account_rate_limit = privateGetTradeAccountRateLimit = Entry('trade/account-rate-limit', 'private', 'GET', {'cost': 1})
    private_get_asset_currencies = privateGetAssetCurrencies = Entry('asset/currencies', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_balances = privateGetAssetBalances = Entry('asset/balances', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_non_tradable_assets = privateGetAssetNonTradableAssets = Entry('asset/non-tradable-assets', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_asset_valuation = privateGetAssetAssetValuation = Entry('asset/asset-valuation', 'private', 'GET', {'cost': 10})
    private_get_asset_transfer_state = privateGetAssetTransferState = Entry('asset/transfer-state', 'private', 'GET', {'cost': 10})
    private_get_asset_bills = privateGetAssetBills = Entry('asset/bills', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_deposit_lightning = privateGetAssetDepositLightning = Entry('asset/deposit-lightning', 'private', 'GET', {'cost': 5})
    private_get_asset_deposit_address = privateGetAssetDepositAddress = Entry('asset/deposit-address', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_deposit_history = privateGetAssetDepositHistory = Entry('asset/deposit-history', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_withdrawal_history = privateGetAssetWithdrawalHistory = Entry('asset/withdrawal-history', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_deposit_withdraw_status = privateGetAssetDepositWithdrawStatus = Entry('asset/deposit-withdraw-status', 'private', 'GET', {'cost': 20})
    private_get_asset_convert_currencies = privateGetAssetConvertCurrencies = Entry('asset/convert/currencies', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_convert_currency_pair = privateGetAssetConvertCurrencyPair = Entry('asset/convert/currency-pair', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_convert_history = privateGetAssetConvertHistory = Entry('asset/convert/history', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_monthly_statement = privateGetAssetMonthlyStatement = Entry('asset/monthly-statement', 'private', 'GET', {'cost': 2})
    private_get_account_instruments = privateGetAccountInstruments = Entry('account/instruments', 'private', 'GET', {'cost': 1})
    private_get_account_balance = privateGetAccountBalance = Entry('account/balance', 'private', 'GET', {'cost': 2})
    private_get_account_positions = privateGetAccountPositions = Entry('account/positions', 'private', 'GET', {'cost': 2})
    private_get_account_positions_history = privateGetAccountPositionsHistory = Entry('account/positions-history', 'private', 'GET', {'cost': 100})
    private_get_account_account_position_risk = privateGetAccountAccountPositionRisk = Entry('account/account-position-risk', 'private', 'GET', {'cost': 2})
    private_get_account_bills = privateGetAccountBills = Entry('account/bills', 'private', 'GET', {'cost': 1.****************})
    private_get_account_bills_archive = privateGetAccountBillsArchive = Entry('account/bills-archive', 'private', 'GET', {'cost': 1.****************})
    private_get_account_bills_history_archive = privateGetAccountBillsHistoryArchive = Entry('account/bills-history-archive', 'private', 'GET', {'cost': 2})
    private_get_account_config = privateGetAccountConfig = Entry('account/config', 'private', 'GET', {'cost': 4})
    private_get_account_max_size = privateGetAccountMaxSize = Entry('account/max-size', 'private', 'GET', {'cost': 1})
    private_get_account_max_avail_size = privateGetAccountMaxAvailSize = Entry('account/max-avail-size', 'private', 'GET', {'cost': 1})
    private_get_account_leverage_info = privateGetAccountLeverageInfo = Entry('account/leverage-info', 'private', 'GET', {'cost': 1})
    private_get_account_adjust_leverage_info = privateGetAccountAdjustLeverageInfo = Entry('account/adjust-leverage-info', 'private', 'GET', {'cost': 4})
    private_get_account_max_loan = privateGetAccountMaxLoan = Entry('account/max-loan', 'private', 'GET', {'cost': 1})
    private_get_account_trade_fee = privateGetAccountTradeFee = Entry('account/trade-fee', 'private', 'GET', {'cost': 4})
    private_get_account_interest_accrued = privateGetAccountInterestAccrued = Entry('account/interest-accrued', 'private', 'GET', {'cost': 4})
    private_get_account_interest_rate = privateGetAccountInterestRate = Entry('account/interest-rate', 'private', 'GET', {'cost': 4})
    private_get_account_max_withdrawal = privateGetAccountMaxWithdrawal = Entry('account/max-withdrawal', 'private', 'GET', {'cost': 1})
    private_get_account_risk_state = privateGetAccountRiskState = Entry('account/risk-state', 'private', 'GET', {'cost': 2})
    private_get_account_quick_margin_borrow_repay_history = privateGetAccountQuickMarginBorrowRepayHistory = Entry('account/quick-margin-borrow-repay-history', 'private', 'GET', {'cost': 4})
    private_get_account_borrow_repay_history = privateGetAccountBorrowRepayHistory = Entry('account/borrow-repay-history', 'private', 'GET', {'cost': 4})
    private_get_account_vip_interest_accrued = privateGetAccountVipInterestAccrued = Entry('account/vip-interest-accrued', 'private', 'GET', {'cost': 4})
    private_get_account_vip_interest_deducted = privateGetAccountVipInterestDeducted = Entry('account/vip-interest-deducted', 'private', 'GET', {'cost': 4})
    private_get_account_vip_loan_order_list = privateGetAccountVipLoanOrderList = Entry('account/vip-loan-order-list', 'private', 'GET', {'cost': 4})
    private_get_account_vip_loan_order_detail = privateGetAccountVipLoanOrderDetail = Entry('account/vip-loan-order-detail', 'private', 'GET', {'cost': 4})
    private_get_account_interest_limits = privateGetAccountInterestLimits = Entry('account/interest-limits', 'private', 'GET', {'cost': 4})
    private_get_account_greeks = privateGetAccountGreeks = Entry('account/greeks', 'private', 'GET', {'cost': 2})
    private_get_account_position_tiers = privateGetAccountPositionTiers = Entry('account/position-tiers', 'private', 'GET', {'cost': 2})
    private_get_account_mmp_config = privateGetAccountMmpConfig = Entry('account/mmp-config', 'private', 'GET', {'cost': 4})
    private_get_account_fixed_loan_borrowing_limit = privateGetAccountFixedLoanBorrowingLimit = Entry('account/fixed-loan/borrowing-limit', 'private', 'GET', {'cost': 4})
    private_get_account_fixed_loan_borrowing_quote = privateGetAccountFixedLoanBorrowingQuote = Entry('account/fixed-loan/borrowing-quote', 'private', 'GET', {'cost': 5})
    private_get_account_fixed_loan_borrowing_orders_list = privateGetAccountFixedLoanBorrowingOrdersList = Entry('account/fixed-loan/borrowing-orders-list', 'private', 'GET', {'cost': 5})
    private_get_account_spot_manual_borrow_repay = privateGetAccountSpotManualBorrowRepay = Entry('account/spot-manual-borrow-repay', 'private', 'GET', {'cost': 10})
    private_get_account_set_auto_repay = privateGetAccountSetAutoRepay = Entry('account/set-auto-repay', 'private', 'GET', {'cost': 4})
    private_get_account_spot_borrow_repay_history = privateGetAccountSpotBorrowRepayHistory = Entry('account/spot-borrow-repay-history', 'private', 'GET', {'cost': 4})
    private_get_account_move_positions_history = privateGetAccountMovePositionsHistory = Entry('account/move-positions-history', 'private', 'GET', {'cost': 10})
    private_get_users_subaccount_list = privateGetUsersSubaccountList = Entry('users/subaccount/list', 'private', 'GET', {'cost': 10})
    private_get_account_subaccount_balances = privateGetAccountSubaccountBalances = Entry('account/subaccount/balances', 'private', 'GET', {'cost': 3.****************})
    private_get_asset_subaccount_balances = privateGetAssetSubaccountBalances = Entry('asset/subaccount/balances', 'private', 'GET', {'cost': 3.****************})
    private_get_account_subaccount_max_withdrawal = privateGetAccountSubaccountMaxWithdrawal = Entry('account/subaccount/max-withdrawal', 'private', 'GET', {'cost': 1})
    private_get_asset_subaccount_bills = privateGetAssetSubaccountBills = Entry('asset/subaccount/bills', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_subaccount_managed_subaccount_bills = privateGetAssetSubaccountManagedSubaccountBills = Entry('asset/subaccount/managed-subaccount-bills', 'private', 'GET', {'cost': 1.****************})
    private_get_users_entrust_subaccount_list = privateGetUsersEntrustSubaccountList = Entry('users/entrust-subaccount-list', 'private', 'GET', {'cost': 10})
    private_get_account_subaccount_interest_limits = privateGetAccountSubaccountInterestLimits = Entry('account/subaccount/interest-limits', 'private', 'GET', {'cost': 4})
    private_get_users_subaccount_apikey = privateGetUsersSubaccountApikey = Entry('users/subaccount/apikey', 'private', 'GET', {'cost': 10})
    private_get_tradingbot_grid_orders_algo_pending = privateGetTradingBotGridOrdersAlgoPending = Entry('tradingBot/grid/orders-algo-pending', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_grid_orders_algo_history = privateGetTradingBotGridOrdersAlgoHistory = Entry('tradingBot/grid/orders-algo-history', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_grid_orders_algo_details = privateGetTradingBotGridOrdersAlgoDetails = Entry('tradingBot/grid/orders-algo-details', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_grid_sub_orders = privateGetTradingBotGridSubOrders = Entry('tradingBot/grid/sub-orders', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_grid_positions = privateGetTradingBotGridPositions = Entry('tradingBot/grid/positions', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_grid_ai_param = privateGetTradingBotGridAiParam = Entry('tradingBot/grid/ai-param', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_signals = privateGetTradingBotSignalSignals = Entry('tradingBot/signal/signals', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_orders_algo_details = privateGetTradingBotSignalOrdersAlgoDetails = Entry('tradingBot/signal/orders-algo-details', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_orders_algo_history = privateGetTradingBotSignalOrdersAlgoHistory = Entry('tradingBot/signal/orders-algo-history', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_positions = privateGetTradingBotSignalPositions = Entry('tradingBot/signal/positions', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_positions_history = privateGetTradingBotSignalPositionsHistory = Entry('tradingBot/signal/positions-history', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_sub_orders = privateGetTradingBotSignalSubOrders = Entry('tradingBot/signal/sub-orders', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_signal_event_history = privateGetTradingBotSignalEventHistory = Entry('tradingBot/signal/event-history', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_recurring_orders_algo_pending = privateGetTradingBotRecurringOrdersAlgoPending = Entry('tradingBot/recurring/orders-algo-pending', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_recurring_orders_algo_history = privateGetTradingBotRecurringOrdersAlgoHistory = Entry('tradingBot/recurring/orders-algo-history', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_recurring_orders_algo_details = privateGetTradingBotRecurringOrdersAlgoDetails = Entry('tradingBot/recurring/orders-algo-details', 'private', 'GET', {'cost': 1})
    private_get_tradingbot_recurring_sub_orders = privateGetTradingBotRecurringSubOrders = Entry('tradingBot/recurring/sub-orders', 'private', 'GET', {'cost': 1})
    private_get_finance_savings_balance = privateGetFinanceSavingsBalance = Entry('finance/savings/balance', 'private', 'GET', {'cost': 1.****************})
    private_get_finance_savings_lending_history = privateGetFinanceSavingsLendingHistory = Entry('finance/savings/lending-history', 'private', 'GET', {'cost': 1.****************})
    private_get_finance_staking_defi_offers = privateGetFinanceStakingDefiOffers = Entry('finance/staking-defi/offers', 'private', 'GET', {'cost': 3.****************})
    private_get_finance_staking_defi_orders_active = privateGetFinanceStakingDefiOrdersActive = Entry('finance/staking-defi/orders-active', 'private', 'GET', {'cost': 3.****************})
    private_get_finance_staking_defi_orders_history = privateGetFinanceStakingDefiOrdersHistory = Entry('finance/staking-defi/orders-history', 'private', 'GET', {'cost': 3.****************})
    private_get_finance_staking_defi_eth_balance = privateGetFinanceStakingDefiEthBalance = Entry('finance/staking-defi/eth/balance', 'private', 'GET', {'cost': 1.****************})
    private_get_finance_staking_defi_eth_purchase_redeem_history = privateGetFinanceStakingDefiEthPurchaseRedeemHistory = Entry('finance/staking-defi/eth/purchase-redeem-history', 'private', 'GET', {'cost': 1.****************})
    private_get_finance_staking_defi_eth_product_info = privateGetFinanceStakingDefiEthProductInfo = Entry('finance/staking-defi/eth/product-info', 'private', 'GET', {'cost': 3})
    private_get_finance_staking_defi_sol_balance = privateGetFinanceStakingDefiSolBalance = Entry('finance/staking-defi/sol/balance', 'private', 'GET', {'cost': 1.****************})
    private_get_finance_staking_defi_sol_purchase_redeem_history = privateGetFinanceStakingDefiSolPurchaseRedeemHistory = Entry('finance/staking-defi/sol/purchase-redeem-history', 'private', 'GET', {'cost': 1.****************})
    private_get_copytrading_current_subpositions = privateGetCopytradingCurrentSubpositions = Entry('copytrading/current-subpositions', 'private', 'GET', {'cost': 1})
    private_get_copytrading_subpositions_history = privateGetCopytradingSubpositionsHistory = Entry('copytrading/subpositions-history', 'private', 'GET', {'cost': 1})
    private_get_copytrading_instruments = privateGetCopytradingInstruments = Entry('copytrading/instruments', 'private', 'GET', {'cost': 4})
    private_get_copytrading_profit_sharing_details = privateGetCopytradingProfitSharingDetails = Entry('copytrading/profit-sharing-details', 'private', 'GET', {'cost': 4})
    private_get_copytrading_total_profit_sharing = privateGetCopytradingTotalProfitSharing = Entry('copytrading/total-profit-sharing', 'private', 'GET', {'cost': 4})
    private_get_copytrading_unrealized_profit_sharing_details = privateGetCopytradingUnrealizedProfitSharingDetails = Entry('copytrading/unrealized-profit-sharing-details', 'private', 'GET', {'cost': 4})
    private_get_copytrading_copy_settings = privateGetCopytradingCopySettings = Entry('copytrading/copy-settings', 'private', 'GET', {'cost': 4})
    private_get_copytrading_batch_leverage_info = privateGetCopytradingBatchLeverageInfo = Entry('copytrading/batch-leverage-info', 'private', 'GET', {'cost': 4})
    private_get_copytrading_current_lead_traders = privateGetCopytradingCurrentLeadTraders = Entry('copytrading/current-lead-traders', 'private', 'GET', {'cost': 4})
    private_get_copytrading_lead_traders_history = privateGetCopytradingLeadTradersHistory = Entry('copytrading/lead-traders-history', 'private', 'GET', {'cost': 4})
    private_get_broker_nd_info = privateGetBrokerNdInfo = Entry('broker/nd/info', 'private', 'GET', {'cost': 10})
    private_get_broker_nd_subaccount_info = privateGetBrokerNdSubaccountInfo = Entry('broker/nd/subaccount-info', 'private', 'GET', {'cost': 10})
    private_get_broker_nd_subaccount_apikey = privateGetBrokerNdSubaccountApikey = Entry('broker/nd/subaccount/apikey', 'private', 'GET', {'cost': 10})
    private_get_asset_broker_nd_subaccount_deposit_address = privateGetAssetBrokerNdSubaccountDepositAddress = Entry('asset/broker/nd/subaccount-deposit-address', 'private', 'GET', {'cost': 1.****************})
    private_get_asset_broker_nd_subaccount_deposit_history = privateGetAssetBrokerNdSubaccountDepositHistory = Entry('asset/broker/nd/subaccount-deposit-history', 'private', 'GET', {'cost': 4})
    private_get_asset_broker_nd_subaccount_withdrawal_history = privateGetAssetBrokerNdSubaccountWithdrawalHistory = Entry('asset/broker/nd/subaccount-withdrawal-history', 'private', 'GET', {'cost': 4})
    private_get_broker_nd_rebate_daily = privateGetBrokerNdRebateDaily = Entry('broker/nd/rebate-daily', 'private', 'GET', {'cost': 100})
    private_get_broker_nd_rebate_per_orders = privateGetBrokerNdRebatePerOrders = Entry('broker/nd/rebate-per-orders', 'private', 'GET', {'cost': 300})
    private_get_finance_sfp_dcd_order = privateGetFinanceSfpDcdOrder = Entry('finance/sfp/dcd/order', 'private', 'GET', {'cost': 2})
    private_get_finance_sfp_dcd_orders = privateGetFinanceSfpDcdOrders = Entry('finance/sfp/dcd/orders', 'private', 'GET', {'cost': 2})
    private_get_broker_fd_rebate_per_orders = privateGetBrokerFdRebatePerOrders = Entry('broker/fd/rebate-per-orders', 'private', 'GET', {'cost': 300})
    private_get_broker_fd_if_rebate = privateGetBrokerFdIfRebate = Entry('broker/fd/if-rebate', 'private', 'GET', {'cost': 5})
    private_get_affiliate_invitee_detail = privateGetAffiliateInviteeDetail = Entry('affiliate/invitee/detail', 'private', 'GET', {'cost': 1})
    private_get_users_partner_if_rebate = privateGetUsersPartnerIfRebate = Entry('users/partner/if-rebate', 'private', 'GET', {'cost': 1})
    private_get_support_announcements = privateGetSupportAnnouncements = Entry('support/announcements', 'private', 'GET', {'cost': 4})
    private_post_rfq_create_rfq = privatePostRfqCreateRfq = Entry('rfq/create-rfq', 'private', 'POST', {'cost': 4})
    private_post_rfq_cancel_rfq = privatePostRfqCancelRfq = Entry('rfq/cancel-rfq', 'private', 'POST', {'cost': 4})
    private_post_rfq_cancel_batch_rfqs = privatePostRfqCancelBatchRfqs = Entry('rfq/cancel-batch-rfqs', 'private', 'POST', {'cost': 10})
    private_post_rfq_cancel_all_rfqs = privatePostRfqCancelAllRfqs = Entry('rfq/cancel-all-rfqs', 'private', 'POST', {'cost': 10})
    private_post_rfq_execute_quote = privatePostRfqExecuteQuote = Entry('rfq/execute-quote', 'private', 'POST', {'cost': 15})
    private_post_rfq_maker_instrument_settings = privatePostRfqMakerInstrumentSettings = Entry('rfq/maker-instrument-settings', 'private', 'POST', {'cost': 4})
    private_post_rfq_mmp_reset = privatePostRfqMmpReset = Entry('rfq/mmp-reset', 'private', 'POST', {'cost': 4})
    private_post_rfq_mmp_config = privatePostRfqMmpConfig = Entry('rfq/mmp-config', 'private', 'POST', {'cost': 100})
    private_post_rfq_create_quote = privatePostRfqCreateQuote = Entry('rfq/create-quote', 'private', 'POST', {'cost': 0.4})
    private_post_rfq_cancel_quote = privatePostRfqCancelQuote = Entry('rfq/cancel-quote', 'private', 'POST', {'cost': 0.4})
    private_post_rfq_cancel_batch_quotes = privatePostRfqCancelBatchQuotes = Entry('rfq/cancel-batch-quotes', 'private', 'POST', {'cost': 10})
    private_post_rfq_cancel_all_quotes = privatePostRfqCancelAllQuotes = Entry('rfq/cancel-all-quotes', 'private', 'POST', {'cost': 10})
    private_post_sprd_order = privatePostSprdOrder = Entry('sprd/order', 'private', 'POST', {'cost': 1})
    private_post_sprd_cancel_order = privatePostSprdCancelOrder = Entry('sprd/cancel-order', 'private', 'POST', {'cost': 1})
    private_post_sprd_mass_cancel = privatePostSprdMassCancel = Entry('sprd/mass-cancel', 'private', 'POST', {'cost': 1})
    private_post_sprd_amend_order = privatePostSprdAmendOrder = Entry('sprd/amend-order', 'private', 'POST', {'cost': 1})
    private_post_sprd_cancel_all_after = privatePostSprdCancelAllAfter = Entry('sprd/cancel-all-after', 'private', 'POST', {'cost': 10})
    private_post_trade_order = privatePostTradeOrder = Entry('trade/order', 'private', 'POST', {'cost': 0.**************33})
    private_post_trade_batch_orders = privatePostTradeBatchOrders = Entry('trade/batch-orders', 'private', 'POST', {'cost': 0.0****************})
    private_post_trade_cancel_order = privatePostTradeCancelOrder = Entry('trade/cancel-order', 'private', 'POST', {'cost': 0.**************33})
    private_post_trade_cancel_batch_orders = privatePostTradeCancelBatchOrders = Entry('trade/cancel-batch-orders', 'private', 'POST', {'cost': 0.0****************})
    private_post_trade_amend_order = privatePostTradeAmendOrder = Entry('trade/amend-order', 'private', 'POST', {'cost': 0.**************33})
    private_post_trade_amend_batch_orders = privatePostTradeAmendBatchOrders = Entry('trade/amend-batch-orders', 'private', 'POST', {'cost': 0.00****************})
    private_post_trade_close_position = privatePostTradeClosePosition = Entry('trade/close-position', 'private', 'POST', {'cost': 1})
    private_post_trade_fills_archive = privatePostTradeFillsArchive = Entry('trade/fills-archive', 'private', 'POST', {'cost': 172800})
    private_post_trade_order_algo = privatePostTradeOrderAlgo = Entry('trade/order-algo', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_algos = privatePostTradeCancelAlgos = Entry('trade/cancel-algos', 'private', 'POST', {'cost': 1})
    private_post_trade_amend_algos = privatePostTradeAmendAlgos = Entry('trade/amend-algos', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_advance_algos = privatePostTradeCancelAdvanceAlgos = Entry('trade/cancel-advance-algos', 'private', 'POST', {'cost': 1})
    private_post_trade_easy_convert = privatePostTradeEasyConvert = Entry('trade/easy-convert', 'private', 'POST', {'cost': 20})
    private_post_trade_one_click_repay = privatePostTradeOneClickRepay = Entry('trade/one-click-repay', 'private', 'POST', {'cost': 20})
    private_post_trade_one_click_repay_v2 = privatePostTradeOneClickRepayV2 = Entry('trade/one-click-repay-v2', 'private', 'POST', {'cost': 20})
    private_post_trade_mass_cancel = privatePostTradeMassCancel = Entry('trade/mass-cancel', 'private', 'POST', {'cost': 4})
    private_post_trade_cancel_all_after = privatePostTradeCancelAllAfter = Entry('trade/cancel-all-after', 'private', 'POST', {'cost': 10})
    private_post_asset_transfer = privatePostAssetTransfer = Entry('asset/transfer', 'private', 'POST', {'cost': 10})
    private_post_asset_withdrawal = privatePostAssetWithdrawal = Entry('asset/withdrawal', 'private', 'POST', {'cost': 1.****************})
    private_post_asset_withdrawal_lightning = privatePostAssetWithdrawalLightning = Entry('asset/withdrawal-lightning', 'private', 'POST', {'cost': 5})
    private_post_asset_cancel_withdrawal = privatePostAssetCancelWithdrawal = Entry('asset/cancel-withdrawal', 'private', 'POST', {'cost': 1.****************})
    private_post_asset_convert_dust_assets = privatePostAssetConvertDustAssets = Entry('asset/convert-dust-assets', 'private', 'POST', {'cost': 10})
    private_post_asset_convert_estimate_quote = privatePostAssetConvertEstimateQuote = Entry('asset/convert/estimate-quote', 'private', 'POST', {'cost': 1})
    private_post_asset_convert_trade = privatePostAssetConvertTrade = Entry('asset/convert/trade', 'private', 'POST', {'cost': 1})
    private_post_asset_monthly_statement = privatePostAssetMonthlyStatement = Entry('asset/monthly-statement', 'private', 'POST', {'cost': 1})
    private_post_account_set_position_mode = privatePostAccountSetPositionMode = Entry('account/set-position-mode', 'private', 'POST', {'cost': 4})
    private_post_account_set_leverage = privatePostAccountSetLeverage = Entry('account/set-leverage', 'private', 'POST', {'cost': 1})
    private_post_account_position_margin_balance = privatePostAccountPositionMarginBalance = Entry('account/position/margin-balance', 'private', 'POST', {'cost': 1})
    private_post_account_set_greeks = privatePostAccountSetGreeks = Entry('account/set-greeks', 'private', 'POST', {'cost': 4})
    private_post_account_set_isolated_mode = privatePostAccountSetIsolatedMode = Entry('account/set-isolated-mode', 'private', 'POST', {'cost': 4})
    private_post_account_quick_margin_borrow_repay = privatePostAccountQuickMarginBorrowRepay = Entry('account/quick-margin-borrow-repay', 'private', 'POST', {'cost': 4})
    private_post_account_borrow_repay = privatePostAccountBorrowRepay = Entry('account/borrow-repay', 'private', 'POST', {'cost': 1.****************})
    private_post_account_simulated_margin = privatePostAccountSimulatedMargin = Entry('account/simulated_margin', 'private', 'POST', {'cost': 10})
    private_post_account_position_builder = privatePostAccountPositionBuilder = Entry('account/position-builder', 'private', 'POST', {'cost': 10})
    private_post_account_set_riskoffset_type = privatePostAccountSetRiskOffsetType = Entry('account/set-riskOffset-type', 'private', 'POST', {'cost': 2})
    private_post_account_activate_option = privatePostAccountActivateOption = Entry('account/activate-option', 'private', 'POST', {'cost': 4})
    private_post_account_set_auto_loan = privatePostAccountSetAutoLoan = Entry('account/set-auto-loan', 'private', 'POST', {'cost': 4})
    private_post_account_set_account_level = privatePostAccountSetAccountLevel = Entry('account/set-account-level', 'private', 'POST', {'cost': 4})
    private_post_account_mmp_reset = privatePostAccountMmpReset = Entry('account/mmp-reset', 'private', 'POST', {'cost': 4})
    private_post_account_mmp_config = privatePostAccountMmpConfig = Entry('account/mmp-config', 'private', 'POST', {'cost': 100})
    private_post_account_fixed_loan_borrowing_order = privatePostAccountFixedLoanBorrowingOrder = Entry('account/fixed-loan/borrowing-order', 'private', 'POST', {'cost': 5})
    private_post_account_fixed_loan_amend_borrowing_order = privatePostAccountFixedLoanAmendBorrowingOrder = Entry('account/fixed-loan/amend-borrowing-order', 'private', 'POST', {'cost': 5})
    private_post_account_fixed_loan_manual_reborrow = privatePostAccountFixedLoanManualReborrow = Entry('account/fixed-loan/manual-reborrow', 'private', 'POST', {'cost': 5})
    private_post_account_fixed_loan_repay_borrowing_order = privatePostAccountFixedLoanRepayBorrowingOrder = Entry('account/fixed-loan/repay-borrowing-order', 'private', 'POST', {'cost': 5})
    private_post_account_bills_history_archive = privatePostAccountBillsHistoryArchive = Entry('account/bills-history-archive', 'private', 'POST', {'cost': 72000})
    private_post_account_move_positions = privatePostAccountMovePositions = Entry('account/move-positions', 'private', 'POST', {'cost': 10})
    private_post_users_subaccount_modify_apikey = privatePostUsersSubaccountModifyApikey = Entry('users/subaccount/modify-apikey', 'private', 'POST', {'cost': 10})
    private_post_asset_subaccount_transfer = privatePostAssetSubaccountTransfer = Entry('asset/subaccount/transfer', 'private', 'POST', {'cost': 10})
    private_post_users_subaccount_set_transfer_out = privatePostUsersSubaccountSetTransferOut = Entry('users/subaccount/set-transfer-out', 'private', 'POST', {'cost': 10})
    private_post_account_subaccount_set_loan_allocation = privatePostAccountSubaccountSetLoanAllocation = Entry('account/subaccount/set-loan-allocation', 'private', 'POST', {'cost': 4})
    private_post_users_subaccount_create_subaccount = privatePostUsersSubaccountCreateSubaccount = Entry('users/subaccount/create-subaccount', 'private', 'POST', {'cost': 10})
    private_post_users_subaccount_subaccount_apikey = privatePostUsersSubaccountSubaccountApikey = Entry('users/subaccount/subaccount-apikey', 'private', 'POST', {'cost': 10})
    private_post_users_subaccount_delete_apikey = privatePostUsersSubaccountDeleteApikey = Entry('users/subaccount/delete-apikey', 'private', 'POST', {'cost': 10})
    private_post_tradingbot_grid_order_algo = privatePostTradingBotGridOrderAlgo = Entry('tradingBot/grid/order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_amend_order_algo = privatePostTradingBotGridAmendOrderAlgo = Entry('tradingBot/grid/amend-order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_stop_order_algo = privatePostTradingBotGridStopOrderAlgo = Entry('tradingBot/grid/stop-order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_close_position = privatePostTradingBotGridClosePosition = Entry('tradingBot/grid/close-position', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_cancel_close_order = privatePostTradingBotGridCancelCloseOrder = Entry('tradingBot/grid/cancel-close-order', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_order_instant_trigger = privatePostTradingBotGridOrderInstantTrigger = Entry('tradingBot/grid/order-instant-trigger', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_withdraw_income = privatePostTradingBotGridWithdrawIncome = Entry('tradingBot/grid/withdraw-income', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_compute_margin_balance = privatePostTradingBotGridComputeMarginBalance = Entry('tradingBot/grid/compute-margin-balance', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_margin_balance = privatePostTradingBotGridMarginBalance = Entry('tradingBot/grid/margin-balance', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_min_investment = privatePostTradingBotGridMinInvestment = Entry('tradingBot/grid/min-investment', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_grid_adjust_investment = privatePostTradingBotGridAdjustInvestment = Entry('tradingBot/grid/adjust-investment', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_create_signal = privatePostTradingBotSignalCreateSignal = Entry('tradingBot/signal/create-signal', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_order_algo = privatePostTradingBotSignalOrderAlgo = Entry('tradingBot/signal/order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_stop_order_algo = privatePostTradingBotSignalStopOrderAlgo = Entry('tradingBot/signal/stop-order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_margin_balance = privatePostTradingBotSignalMarginBalance = Entry('tradingBot/signal/margin-balance', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_amendtpsl = privatePostTradingBotSignalAmendTPSL = Entry('tradingBot/signal/amendTPSL', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_set_instruments = privatePostTradingBotSignalSetInstruments = Entry('tradingBot/signal/set-instruments', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_close_position = privatePostTradingBotSignalClosePosition = Entry('tradingBot/signal/close-position', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_sub_order = privatePostTradingBotSignalSubOrder = Entry('tradingBot/signal/sub-order', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_signal_cancel_sub_order = privatePostTradingBotSignalCancelSubOrder = Entry('tradingBot/signal/cancel-sub-order', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_recurring_order_algo = privatePostTradingBotRecurringOrderAlgo = Entry('tradingBot/recurring/order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_recurring_amend_order_algo = privatePostTradingBotRecurringAmendOrderAlgo = Entry('tradingBot/recurring/amend-order-algo', 'private', 'POST', {'cost': 1})
    private_post_tradingbot_recurring_stop_order_algo = privatePostTradingBotRecurringStopOrderAlgo = Entry('tradingBot/recurring/stop-order-algo', 'private', 'POST', {'cost': 1})
    private_post_finance_savings_purchase_redempt = privatePostFinanceSavingsPurchaseRedempt = Entry('finance/savings/purchase-redempt', 'private', 'POST', {'cost': 1.****************})
    private_post_finance_savings_set_lending_rate = privatePostFinanceSavingsSetLendingRate = Entry('finance/savings/set-lending-rate', 'private', 'POST', {'cost': 1.****************})
    private_post_finance_staking_defi_purchase = privatePostFinanceStakingDefiPurchase = Entry('finance/staking-defi/purchase', 'private', 'POST', {'cost': 3})
    private_post_finance_staking_defi_redeem = privatePostFinanceStakingDefiRedeem = Entry('finance/staking-defi/redeem', 'private', 'POST', {'cost': 3})
    private_post_finance_staking_defi_cancel = privatePostFinanceStakingDefiCancel = Entry('finance/staking-defi/cancel', 'private', 'POST', {'cost': 3})
    private_post_finance_staking_defi_eth_purchase = privatePostFinanceStakingDefiEthPurchase = Entry('finance/staking-defi/eth/purchase', 'private', 'POST', {'cost': 5})
    private_post_finance_staking_defi_eth_redeem = privatePostFinanceStakingDefiEthRedeem = Entry('finance/staking-defi/eth/redeem', 'private', 'POST', {'cost': 5})
    private_post_finance_staking_defi_sol_purchase = privatePostFinanceStakingDefiSolPurchase = Entry('finance/staking-defi/sol/purchase', 'private', 'POST', {'cost': 5})
    private_post_finance_staking_defi_sol_redeem = privatePostFinanceStakingDefiSolRedeem = Entry('finance/staking-defi/sol/redeem', 'private', 'POST', {'cost': 5})
    private_post_copytrading_algo_order = privatePostCopytradingAlgoOrder = Entry('copytrading/algo-order', 'private', 'POST', {'cost': 1})
    private_post_copytrading_close_subposition = privatePostCopytradingCloseSubposition = Entry('copytrading/close-subposition', 'private', 'POST', {'cost': 1})
    private_post_copytrading_set_instruments = privatePostCopytradingSetInstruments = Entry('copytrading/set-instruments', 'private', 'POST', {'cost': 4})
    private_post_copytrading_first_copy_settings = privatePostCopytradingFirstCopySettings = Entry('copytrading/first-copy-settings', 'private', 'POST', {'cost': 4})
    private_post_copytrading_amend_copy_settings = privatePostCopytradingAmendCopySettings = Entry('copytrading/amend-copy-settings', 'private', 'POST', {'cost': 4})
    private_post_copytrading_stop_copy_trading = privatePostCopytradingStopCopyTrading = Entry('copytrading/stop-copy-trading', 'private', 'POST', {'cost': 4})
    private_post_copytrading_batch_set_leverage = privatePostCopytradingBatchSetLeverage = Entry('copytrading/batch-set-leverage', 'private', 'POST', {'cost': 4})
    private_post_broker_nd_create_subaccount = privatePostBrokerNdCreateSubaccount = Entry('broker/nd/create-subaccount', 'private', 'POST', {'cost': 0.25})
    private_post_broker_nd_delete_subaccount = privatePostBrokerNdDeleteSubaccount = Entry('broker/nd/delete-subaccount', 'private', 'POST', {'cost': 1})
    private_post_broker_nd_subaccount_apikey = privatePostBrokerNdSubaccountApikey = Entry('broker/nd/subaccount/apikey', 'private', 'POST', {'cost': 0.25})
    private_post_broker_nd_subaccount_modify_apikey = privatePostBrokerNdSubaccountModifyApikey = Entry('broker/nd/subaccount/modify-apikey', 'private', 'POST', {'cost': 1})
    private_post_broker_nd_subaccount_delete_apikey = privatePostBrokerNdSubaccountDeleteApikey = Entry('broker/nd/subaccount/delete-apikey', 'private', 'POST', {'cost': 1})
    private_post_broker_nd_set_subaccount_level = privatePostBrokerNdSetSubaccountLevel = Entry('broker/nd/set-subaccount-level', 'private', 'POST', {'cost': 4})
    private_post_broker_nd_set_subaccount_fee_rate = privatePostBrokerNdSetSubaccountFeeRate = Entry('broker/nd/set-subaccount-fee-rate', 'private', 'POST', {'cost': 4})
    private_post_broker_nd_set_subaccount_assets = privatePostBrokerNdSetSubaccountAssets = Entry('broker/nd/set-subaccount-assets', 'private', 'POST', {'cost': 0.25})
    private_post_asset_broker_nd_subaccount_deposit_address = privatePostAssetBrokerNdSubaccountDepositAddress = Entry('asset/broker/nd/subaccount-deposit-address', 'private', 'POST', {'cost': 1})
    private_post_asset_broker_nd_modify_subaccount_deposit_address = privatePostAssetBrokerNdModifySubaccountDepositAddress = Entry('asset/broker/nd/modify-subaccount-deposit-address', 'private', 'POST', {'cost': 1.****************})
    private_post_broker_nd_rebate_per_orders = privatePostBrokerNdRebatePerOrders = Entry('broker/nd/rebate-per-orders', 'private', 'POST', {'cost': 36000})
    private_post_finance_sfp_dcd_quote = privatePostFinanceSfpDcdQuote = Entry('finance/sfp/dcd/quote', 'private', 'POST', {'cost': 10})
    private_post_finance_sfp_dcd_order = privatePostFinanceSfpDcdOrder = Entry('finance/sfp/dcd/order', 'private', 'POST', {'cost': 10})
    private_post_broker_nd_report_subaccount_ip = privatePostBrokerNdReportSubaccountIp = Entry('broker/nd/report-subaccount-ip', 'private', 'POST', {'cost': 0.25})
    private_post_broker_fd_rebate_per_orders = privatePostBrokerFdRebatePerOrders = Entry('broker/fd/rebate-per-orders', 'private', 'POST', {'cost': 36000})
