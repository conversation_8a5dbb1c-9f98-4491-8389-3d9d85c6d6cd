from ccxt.base.types import Entry


class ImplicitAPI:
    webexchange_get = webExchangeGet = Entry('', 'webExchange', 'GET', {})
    web_get_rest_api = webGetRestApi = Entry('rest-api', 'web', 'GET', {})
    public_get_v1_symbols = publicGetV1Symbols = Entry('v1/symbols', 'public', 'GET', {'cost': 5})
    public_get_v1_symbols_details_symbol = publicGetV1SymbolsDetailsSymbol = Entry('v1/symbols/details/{symbol}', 'public', 'GET', {'cost': 5})
    public_get_v1_staking_rates = publicGetV1StakingRates = Entry('v1/staking/rates', 'public', 'GET', {'cost': 5})
    public_get_v1_pubticker_symbol = publicGetV1PubtickerSymbol = Entry('v1/pubticker/{symbol}', 'public', 'GET', {'cost': 5})
    public_get_v2_ticker_symbol = publicGetV2TickerSymbol = Entry('v2/ticker/{symbol}', 'public', 'GET', {'cost': 5})
    public_get_v2_candles_symbol_timeframe = publicGetV2CandlesSymbolTimeframe = Entry('v2/candles/{symbol}/{timeframe}', 'public', 'GET', {'cost': 5})
    public_get_v1_trades_symbol = publicGetV1TradesSymbol = Entry('v1/trades/{symbol}', 'public', 'GET', {'cost': 5})
    public_get_v1_auction_symbol = publicGetV1AuctionSymbol = Entry('v1/auction/{symbol}', 'public', 'GET', {'cost': 5})
    public_get_v1_auction_symbol_history = publicGetV1AuctionSymbolHistory = Entry('v1/auction/{symbol}/history', 'public', 'GET', {'cost': 5})
    public_get_v1_pricefeed = publicGetV1Pricefeed = Entry('v1/pricefeed', 'public', 'GET', {'cost': 5})
    public_get_v1_book_symbol = publicGetV1BookSymbol = Entry('v1/book/{symbol}', 'public', 'GET', {'cost': 5})
    public_get_v1_earn_rates = publicGetV1EarnRates = Entry('v1/earn/rates', 'public', 'GET', {'cost': 5})
    private_post_v1_staking_unstake = privatePostV1StakingUnstake = Entry('v1/staking/unstake', 'private', 'POST', {'cost': 1})
    private_post_v1_staking_stake = privatePostV1StakingStake = Entry('v1/staking/stake', 'private', 'POST', {'cost': 1})
    private_post_v1_staking_rewards = privatePostV1StakingRewards = Entry('v1/staking/rewards', 'private', 'POST', {'cost': 1})
    private_post_v1_staking_history = privatePostV1StakingHistory = Entry('v1/staking/history', 'private', 'POST', {'cost': 1})
    private_post_v1_order_new = privatePostV1OrderNew = Entry('v1/order/new', 'private', 'POST', {'cost': 1})
    private_post_v1_order_cancel = privatePostV1OrderCancel = Entry('v1/order/cancel', 'private', 'POST', {'cost': 1})
    private_post_v1_wrap_symbol = privatePostV1WrapSymbol = Entry('v1/wrap/{symbol}', 'private', 'POST', {'cost': 1})
    private_post_v1_order_cancel_session = privatePostV1OrderCancelSession = Entry('v1/order/cancel/session', 'private', 'POST', {'cost': 1})
    private_post_v1_order_cancel_all = privatePostV1OrderCancelAll = Entry('v1/order/cancel/all', 'private', 'POST', {'cost': 1})
    private_post_v1_order_status = privatePostV1OrderStatus = Entry('v1/order/status', 'private', 'POST', {'cost': 1})
    private_post_v1_orders = privatePostV1Orders = Entry('v1/orders', 'private', 'POST', {'cost': 1})
    private_post_v1_mytrades = privatePostV1Mytrades = Entry('v1/mytrades', 'private', 'POST', {'cost': 1})
    private_post_v1_notionalvolume = privatePostV1Notionalvolume = Entry('v1/notionalvolume', 'private', 'POST', {'cost': 1})
    private_post_v1_tradevolume = privatePostV1Tradevolume = Entry('v1/tradevolume', 'private', 'POST', {'cost': 1})
    private_post_v1_clearing_new = privatePostV1ClearingNew = Entry('v1/clearing/new', 'private', 'POST', {'cost': 1})
    private_post_v1_clearing_status = privatePostV1ClearingStatus = Entry('v1/clearing/status', 'private', 'POST', {'cost': 1})
    private_post_v1_clearing_cancel = privatePostV1ClearingCancel = Entry('v1/clearing/cancel', 'private', 'POST', {'cost': 1})
    private_post_v1_clearing_confirm = privatePostV1ClearingConfirm = Entry('v1/clearing/confirm', 'private', 'POST', {'cost': 1})
    private_post_v1_balances = privatePostV1Balances = Entry('v1/balances', 'private', 'POST', {'cost': 1})
    private_post_v1_balances_staking = privatePostV1BalancesStaking = Entry('v1/balances/staking', 'private', 'POST', {'cost': 1})
    private_post_v1_notionalbalances_currency = privatePostV1NotionalbalancesCurrency = Entry('v1/notionalbalances/{currency}', 'private', 'POST', {'cost': 1})
    private_post_v1_transfers = privatePostV1Transfers = Entry('v1/transfers', 'private', 'POST', {'cost': 1})
    private_post_v1_addresses_network = privatePostV1AddressesNetwork = Entry('v1/addresses/{network}', 'private', 'POST', {'cost': 1})
    private_post_v1_deposit_network_newaddress = privatePostV1DepositNetworkNewAddress = Entry('v1/deposit/{network}/newAddress', 'private', 'POST', {'cost': 1})
    private_post_v1_deposit_currency_newaddress = privatePostV1DepositCurrencyNewAddress = Entry('v1/deposit/{currency}/newAddress', 'private', 'POST', {'cost': 1})
    private_post_v1_withdraw_currency = privatePostV1WithdrawCurrency = Entry('v1/withdraw/{currency}', 'private', 'POST', {'cost': 1})
    private_post_v1_account_transfer_currency = privatePostV1AccountTransferCurrency = Entry('v1/account/transfer/{currency}', 'private', 'POST', {'cost': 1})
    private_post_v1_payments_addbank = privatePostV1PaymentsAddbank = Entry('v1/payments/addbank', 'private', 'POST', {'cost': 1})
    private_post_v1_payments_methods = privatePostV1PaymentsMethods = Entry('v1/payments/methods', 'private', 'POST', {'cost': 1})
    private_post_v1_payments_sen_withdraw = privatePostV1PaymentsSenWithdraw = Entry('v1/payments/sen/withdraw', 'private', 'POST', {'cost': 1})
    private_post_v1_balances_earn = privatePostV1BalancesEarn = Entry('v1/balances/earn', 'private', 'POST', {'cost': 1})
    private_post_v1_earn_interest = privatePostV1EarnInterest = Entry('v1/earn/interest', 'private', 'POST', {'cost': 1})
    private_post_v1_earn_history = privatePostV1EarnHistory = Entry('v1/earn/history', 'private', 'POST', {'cost': 1})
    private_post_v1_approvedaddresses_network_request = privatePostV1ApprovedAddressesNetworkRequest = Entry('v1/approvedAddresses/{network}/request', 'private', 'POST', {'cost': 1})
    private_post_v1_approvedaddresses_account_network = privatePostV1ApprovedAddressesAccountNetwork = Entry('v1/approvedAddresses/account/{network}', 'private', 'POST', {'cost': 1})
    private_post_v1_approvedaddresses_network_remove = privatePostV1ApprovedAddressesNetworkRemove = Entry('v1/approvedAddresses/{network}/remove', 'private', 'POST', {'cost': 1})
    private_post_v1_account = privatePostV1Account = Entry('v1/account', 'private', 'POST', {'cost': 1})
    private_post_v1_account_create = privatePostV1AccountCreate = Entry('v1/account/create', 'private', 'POST', {'cost': 1})
    private_post_v1_account_list = privatePostV1AccountList = Entry('v1/account/list', 'private', 'POST', {'cost': 1})
    private_post_v1_heartbeat = privatePostV1Heartbeat = Entry('v1/heartbeat', 'private', 'POST', {'cost': 1})
    private_post_v1_roles = privatePostV1Roles = Entry('v1/roles', 'private', 'POST', {'cost': 1})
